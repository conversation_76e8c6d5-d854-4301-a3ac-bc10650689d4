import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useMemo } from "react";
import { queryKeys } from "@/lib/queryKeys";
import {
  DeviceFilters,
  DevicePagination,
  Device,
} from "@/stores/useDeviceStore";

interface DevicesResponse {
  devices: Device[];
  pagination: DevicePagination;
  summary: {
    totalDevices: number;
    onlineDevices: number;
    offlineDevices: number;
    allocatedDevices: number;
  };
}

interface DeviceQueryParams {
  page: number;
  pageSize: number;
  search?: string;
  status?: string;
  allocation?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  companyId?: string;
  [key: string]: unknown;
}

// Fetch devices function
async function fetchDevices(
  params: DeviceQueryParams
): Promise<DevicesResponse> {
  const searchParams = new URLSearchParams({
    page: params.page.toString(),
    pageSize: params.pageSize.toString(),
  });

  if (params.search) searchParams.append("search", params.search);
  if (params.status && params.status !== "all")
    searchParams.append("status", params.status);
  if (params.allocation && params.allocation !== "all")
    searchParams.append("allocation", params.allocation);
  if (params.sortBy) searchParams.append("sortBy", params.sortBy);
  if (params.sortOrder) searchParams.append("sortOrder", params.sortOrder);
  if (params.companyId) searchParams.append("companyId", params.companyId);

  const response = await fetch(`/api/admin/devices?${searchParams}`);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to fetch devices");
  }

  return response.json();
}

// Hook for fetching devices
export function useDevicesQuery(
  filters: DeviceFilters,
  pagination: DevicePagination,
  companyId?: string
) {
  const params: DeviceQueryParams = useMemo(
    () => ({
      page: pagination.page,
      pageSize: pagination.pageSize,
      search: filters.search || undefined,
      status: filters.status,
      allocation: filters.allocation,
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder,
      companyId: companyId,
    }),
    [
      pagination.page,
      pagination.pageSize,
      filters.search,
      filters.status,
      filters.allocation,
      filters.sortBy,
      filters.sortOrder,
      companyId,
    ]
  );

  return useQuery({
    queryKey: queryKeys.devices.list(params),
    queryFn: () => fetchDevices(params),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for fetching a single device
export function useDeviceQuery(id: string) {
  return useQuery({
    queryKey: queryKeys.devices.detail(id),
    queryFn: async () => {
      const response = await fetch(`/api/admin/devices/${id}`);
      if (!response.ok) {
        throw new Error("Failed to fetch device");
      }
      return response.json();
    },
    staleTime: 60 * 1000, // 1 minute
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Device action mutation
export function useDeviceActionMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      action,
      deviceSlno,
      ...data
    }: {
      action: string;
      deviceSlno: string;
      [key: string]: unknown;
    }) => {
      const response = await fetch("/api/admin/devices/actions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action, deviceSlno, ...data }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || `Failed to perform ${action}`);
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate devices list to refresh data
      queryClient.invalidateQueries({ queryKey: queryKeys.devices.lists() });
    },
  });
}

// Device allocation mutation
export function useDeviceAllocationMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      deviceSerialNumbers,
      companyId,
      mode,
    }: {
      deviceSerialNumbers: string[];
      companyId?: string;
      mode: "allocate" | "deallocate";
    }) => {
      const endpoint =
        mode === "allocate"
          ? "/api/admin/devices/allocate"
          : "/api/admin/devices/deallocate";

      const response = await fetch(endpoint, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ deviceSerialNumbers, companyId }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || `Failed to ${mode} devices`);
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate both devices and companies lists
      queryClient.invalidateQueries({ queryKey: queryKeys.devices.lists() });
      queryClient.invalidateQueries({ queryKey: queryKeys.companies.lists() });
    },
  });
}

// Device sync mutation
export function useDeviceSyncMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      sourceDeviceSerial,
      targetDeviceSerials,
    }: {
      sourceDeviceSerial: string;
      targetDeviceSerials: string[];
    }) => {
      const response = await fetch("/api/admin/devices/sync", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ sourceDeviceSerial, targetDeviceSerials }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to sync devices");
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate devices list
      queryClient.invalidateQueries({ queryKey: queryKeys.devices.lists() });
    },
  });
}

// Device creation mutation (if needed for adding devices)
export function useCreateDeviceMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (deviceData: Partial<Device>) => {
      const response = await fetch("/api/admin/devices", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(deviceData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create device");
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate devices list
      queryClient.invalidateQueries({ queryKey: queryKeys.devices.lists() });
    },
  });
}

// Device update mutation
export function useUpdateDeviceMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Device> }) => {
      const response = await fetch(`/api/admin/devices/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update device");
      }

      return response.json();
    },
    onSuccess: (_, { id }) => {
      // Invalidate specific device and lists
      queryClient.invalidateQueries({ queryKey: queryKeys.devices.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.devices.lists() });
    },
  });
}

// Device deletion mutation
export function useDeleteDeviceMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/admin/devices/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to delete device");
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate devices list
      queryClient.invalidateQueries({ queryKey: queryKeys.devices.lists() });
    },
  });
}
