"use client";

import { useState } from "react";

import { DashboardLayout } from "@/components/shared/dashboard-layout";
import { DataTable, Column } from "@/components/shared/data-table";
import { SearchInput } from "@/components/shared/SearchInput";
import { CompanyRegistrationForm } from "@/components/features/company-registration-form";
import { EditCompanyDialog } from "@/components/features/edit-company-dialog";
import { ExtendValidityDialog } from "@/components/features/extend-validity-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Calendar,
  Building2,
  LogIn,
} from "lucide-react";
import { CompanyStatus, UserType, CompanyWithAllocations } from "@/types";
import { useCompanyManagement } from "@/hooks/useCompanyManagement";
import { Company } from "@/stores/useCompanyStore";
import { toast } from "react-toastify";

export default function CompaniesPage() {
  // Use the updated company management hook
  const {
    companies,
    pagination,
    loading,
    filters,
    selectedCompanies,
    showRegistrationForm,
    setShowRegistrationForm,
    setSelectedCompanies,
    handlePageChange,
    handleSearch,
    handleSort,
    handleBulkStatusUpdate,
    handleLoginAsCompany,
    refetch,
  } = useCompanyManagement();

  // Local state for dialogs
  const [editCompany, setEditCompany] = useState<CompanyWithAllocations | null>(
    null
  );
  const [showExtendValidity, setShowExtendValidity] = useState(false);
  const [extendValidityCompanies, setExtendValidityCompanies] = useState<
    CompanyWithAllocations[]
  >([]);

  const handleCopyLoginToken = async (company: CompanyWithAllocations) => {
    if (company.loginToken) {
      try {
        await navigator.clipboard.writeText(company.loginToken);
        toast.success("Login token copied to clipboard!");
      } catch (error) {
        toast.error("Failed to copy login token");
      }
    }
  };

  // Helper function to convert CompanyWithAllocations to Company
  const convertToCompany = (company: CompanyWithAllocations): Company => ({
    id: company.id,
    name: company.name,
    organizationId: company.organizationId,
    email: company.email,
    userType: company.userType,
    status: company.status,
    expiresAt:
      company.expiresAt instanceof Date
        ? company.expiresAt.toISOString()
        : company.expiresAt,
    description: company.description || "",
    createdAt:
      company.createdAt instanceof Date
        ? company.createdAt.toISOString()
        : company.createdAt,
    updatedAt:
      company.updatedAt instanceof Date
        ? company.updatedAt.toISOString()
        : company.updatedAt,
    loginToken: company.loginToken,
    allocations: company.allocations.map((a) => ({
      deviceSerialNo: a.deviceSerialNo,
    })),
  });

  // Handle individual company deletion using bulk delete API
  const handleDeleteCompany = async (companyId: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this company? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      // Use the working bulk delete API with single company ID
      const response = await fetch("/api/admin/companies/bulk-delete", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          companyIds: [companyId],
        }),
      });

      if (response.ok) {
        await response.json();
        toast.success("Company deleted successfully");
        // Trigger refetch to update the data
        refetch();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to delete company");
      }
    } catch (error) {
      toast.error("Failed to delete company");
    }
  };

  // Handle bulk company deletion
  const handleBulkDelete = async () => {
    if (selectedCompanies.length === 0) {
      toast.error("Please select companies to delete");
      return;
    }

    if (
      !confirm(
        `Are you sure you want to delete ${selectedCompanies.length} companies? This action cannot be undone.`
      )
    ) {
      return;
    }

    try {
      const response = await fetch("/api/admin/companies/bulk-delete", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          companyIds: selectedCompanies.map((c) => c.id),
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(result.message || "Companies deleted successfully");
        // Clear selection and refetch data
        setSelectedCompanies([]);
        refetch();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to delete companies");
      }
    } catch (error) {
      toast.error("Failed to delete companies");
    }
  };

  // Handle bulk extend validity with dialog
  const handleBulkExtendValidity = () => {
    if (selectedCompanies.length === 0) {
      toast.error("Please select companies to extend validity");
      return;
    }
    // Convert Company[] to CompanyWithAllocations[] by finding matching companies
    const matchingCompanies = companies.filter((company) =>
      selectedCompanies.some((selected) => selected.id === company.id)
    );
    setExtendValidityCompanies(matchingCompanies);
    setShowExtendValidity(true);
  };

  // No need for fetchCompanies function - handled by TanStack Query

  // Wrapper function to handle type conversion for selection
  const handleSelectionChange = (selectedRows: CompanyWithAllocations[]) => {
    const convertedCompanies = selectedRows.map(convertToCompany);
    setSelectedCompanies(convertedCompanies);
  };

  const formatDate = (date: string | Date) => {
    const dateObj = date instanceof Date ? date : new Date(date);
    return dateObj.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getStatusBadge = (status: CompanyStatus, expiresAt: string | Date) => {
    const expiryDate =
      expiresAt instanceof Date ? expiresAt : new Date(expiresAt);
    const isExpired = expiryDate < new Date();

    if (isExpired) {
      return <Badge variant="destructive">Expired</Badge>;
    }

    if (status === "ACTIVE") {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800">
          Active
        </Badge>
      );
    }

    return <Badge variant="secondary">Deactivated</Badge>;
  };

  const getUserTypeBadge = (userType: UserType) => {
    const colors = {
      API_USER: "bg-blue-100 text-blue-800",
      SCHOOL_MANAGEMENT: "bg-purple-100 text-purple-800",
      EMPLOYEE_MANAGEMENT: "bg-orange-100 text-orange-800",
    };

    return (
      <Badge variant="secondary" className={colors[userType]}>
        {userType.replace("_", " ")}
      </Badge>
    );
  };

  const columns: Column<CompanyWithAllocations>[] = [
    {
      key: "name",
      label: "Company",
      sortable: true,
      render: (_, company) => (
        <div>
          <div className="font-medium">{company.name}</div>
          <div className="text-sm text-gray-500">{company.organizationId}</div>
        </div>
      ),
    },
    {
      key: "email",
      label: "Contact",
      sortable: true,
      render: (_, company) => (
        <div>
          <div className="text-sm">{company.email}</div>
          <div className="text-xs text-gray-500">
            {getUserTypeBadge(company.userType)}
          </div>
          {company.loginTokenHash && (
            <div
              className="text-sm text-center bg-slate-100 text-gray-200 hover:text-gray-900 cursor-pointer transition-colors duration-200 mt-1 p-1 font-mono rounded-xs select-none"
              onClick={() => handleCopyLoginToken(company)}
              title="Click to copy login token"
            >
              {company.loginToken || "••••••••••••••••••••••••••••••••"}
            </div>
          )}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      sortable: true,
      render: (_, company) =>
        getStatusBadge(company.status, company.expiresAt.toString()),
    },
    {
      key: "allocations",
      label: "Devices",
      sortable: true,
      render: (_, company) => (
        <div className="text-center">
          <span className="font-medium">{company.allocations.length}</span>
          <div className="text-xs text-gray-500">allocated</div>
        </div>
      ),
    },
    {
      key: "expiresAt",
      label: "Validity",
      sortable: true,
      render: (_, company) => {
        const expirationDate = new Date(company.expiresAt);
        const today = new Date();
        const timeDiff = expirationDate.getTime() - today.getTime();
        const daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));

        return (
          <div className="text-sm">
            <div
              className={`font-medium ${
                daysRemaining <= 7
                  ? "text-red-600"
                  : daysRemaining <= 30
                  ? "text-yellow-600"
                  : "text-green-600"
              }`}
            >
              {daysRemaining > 0
                ? `${daysRemaining} days left`
                : `Expired ${Math.abs(daysRemaining)} days ago`}
            </div>
            <div className="text-xs text-gray-500">
              {formatDate(company.expiresAt.toString())}
            </div>
          </div>
        );
      },
    },
    {
      key: "createdAt",
      label: "Created",
      render: (_, company) => (
        <div className="text-sm text-gray-500">
          {formatDate(company.createdAt)}
        </div>
      ),
    },
    {
      key: "actions",
      label: "",
      render: (_, company) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleLoginAsCompany(company.id)}>
              <LogIn className="h-4 w-4 mr-2" />
              Login as Company
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setEditCompany(company)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                setExtendValidityCompanies([company]);
                setShowExtendValidity(true);
              }}
            >
              <Calendar className="h-4 w-4 mr-2" />
              Extend Validity
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-red-600"
              onClick={() => handleDeleteCompany(company.id)}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  const bulkActions = [
    {
      label: "Activate",
      onClick: () => handleBulkStatusUpdate("ACTIVE"),
      disabled: (companies: CompanyWithAllocations[]) => companies.length === 0,
    },
    {
      label: "Deactivate",
      onClick: () => handleBulkStatusUpdate("DEACTIVATED"),
      variant: "destructive" as const,
      disabled: (companies: CompanyWithAllocations[]) => companies.length === 0,
    },
    {
      label: "Extend Validity",
      onClick: handleBulkExtendValidity,
      disabled: (companies: CompanyWithAllocations[]) => companies.length === 0,
    },
    {
      label: "Delete",
      onClick: handleBulkDelete,
      variant: "destructive" as const,
      disabled: (companies: CompanyWithAllocations[]) => companies.length === 0,
    },
  ];

  return (
    <DashboardLayout userRole="admin" userEmail="<EMAIL>">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex flex-col">
            <h1 className="text-3xl font-bold text-gray-900">Companies</h1>
            <p className="text-gray-600">
              Manage company registrations and access
            </p>
          </div>
          <Button onClick={() => setShowRegistrationForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Register Company
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Companies
              </CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pagination.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active</CardTitle>
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {
                  companies.filter(
                    (c) =>
                      c.status === "ACTIVE" &&
                      new Date(c.expiresAt) > new Date()
                  ).length
                }
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Expired</CardTitle>
              <div className="h-2 w-2 bg-red-500 rounded-full"></div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {
                  companies.filter((c) => new Date(c.expiresAt) < new Date())
                    .length
                }
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Devices
              </CardTitle>
              <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {companies.reduce((sum, c) => sum + c.allocations.length, 0)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Companies Table */}
        <Card>
          <CardHeader>
            <CardTitle>All Companies</CardTitle>
            <CardDescription>
              Manage company registrations, status, and device allocations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Search Input */}
              <SearchInput
                placeholder="Search companies by name, email, or organization ID..."
                onSearch={handleSearch}
                initialValue={filters.search}
                className="max-w-md"
              />

              {/* Data Table */}
              <DataTable<CompanyWithAllocations>
                data={companies}
                columns={columns}
                loading={loading}
                selectable
                onSelectionChange={handleSelectionChange}
                pagination={pagination}
                onPageChange={handlePageChange}
                onSort={handleSort}
                sortBy={filters.sortBy}
                sortOrder={filters.sortOrder}
                emptyMessage={
                  companies.length === 0 && !loading
                    ? "No companies registered yet. Click 'Register Company' to add the first one."
                    : "No companies found matching your search."
                }
                actions={bulkActions}
              />
            </div>
          </CardContent>
        </Card>

        {/* Registration Form */}
        <CompanyRegistrationForm
          open={showRegistrationForm}
          onOpenChange={setShowRegistrationForm}
          onSuccess={() => {
            // Data will be automatically refetched by TanStack Query
            setShowRegistrationForm(false);
          }}
        />

        {/* Edit Company Dialog */}
        {editCompany && (
          <EditCompanyDialog
            open={!!editCompany}
            onOpenChange={(open) => !open && setEditCompany(null)}
            company={convertToCompany(editCompany)}
          />
        )}

        {/* Extend Validity Dialog */}
        <ExtendValidityDialog
          open={showExtendValidity}
          onOpenChange={setShowExtendValidity}
          companies={extendValidityCompanies.map(convertToCompany)}
        />
      </div>
    </DashboardLayout>
  );
}
