"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Shield } from "lucide-react";
import { toast } from "sonner";

export function AdminReturnBanner() {
  const [isAdminImpersonation, setIsAdminImpersonation] = useState(false);

  useEffect(() => {
    // Check if this is an admin impersonation session
    const adminReturn = sessionStorage.getItem("adminReturnSession");
    setIsAdminImpersonation(!!adminReturn);
  }, []);

  const handleReturnToAdmin = async () => {
    try {
      const response = await fetch("/api/auth/return-to-admin", {
        method: "POST",
      });

      if (response.ok) {
        // Clear the admin return flag
        sessionStorage.removeItem("adminReturnSession");
        toast.success("Returning to admin dashboard...");
        window.location.href = "/admin/dashboard";
      } else {
        toast.error("Failed to return to admin dashboard");
      }
    } catch (error) {
      toast.error("An error occurred while returning to admin dashboard");
    }
  };

  if (!isAdminImpersonation) {
    return null;
  }

  return (
    <div className="bg-blue-600 text-white px-4 py-2 flex items-center justify-between">
      <div className="flex items-center space-x-2">
        <Shield className="h-4 w-4" />
        <span className="text-sm font-medium">
          {`Admin Mode: You are viewing this company's dashboard`}
        </span>
      </div>
      <Button
        variant="outline"
        size="sm"
        onClick={handleReturnToAdmin}
        className="bg-white text-blue-600 hover:bg-gray-100 border-white"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Return to Admin
      </Button>
    </div>
  );
}
