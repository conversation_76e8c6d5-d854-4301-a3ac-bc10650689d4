"use client";

import React, { useState } from "react";
import { DashboardLayout } from "@/components/shared/dashboard-layout";
import { AdminReturnBanner } from "@/components/shared/admin-return-banner";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertTriangle, Save, Globe } from "lucide-react";
import { toast } from "react-toastify";
import {
  useCompanySettingsQuery,
  useUpdateCompanySettingsMutation,
} from "@/hooks/queries/useSettingsQueries";
import { SettingsLayout, GeneralSettings } from "@/components/settings";
import { useCompanyProfileForLayout } from "@/hooks/useCompanyProfileForLayout";

export default function SettingsPage() {
  // Use real-time data hooks
  const { data: settings, isLoading, error } = useCompanySettingsQuery();
  const updateSettingsMutation = useUpdateCompanySettingsMutation();
  const { userEmail, userName } = useCompanyProfileForLayout();

  const [editedSettings, setEditedSettings] = useState<any>(null);

  // Initialize edited settings when data loads
  React.useEffect(() => {
    if (settings && !editedSettings) {
      setEditedSettings(settings);
    }
  }, [settings, editedSettings]);

  const handleSave = async () => {
    if (!editedSettings) return;

    try {
      await updateSettingsMutation.mutateAsync(editedSettings);
      toast.success("Settings saved successfully");
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.error || "Failed to save settings";
      toast.error(errorMessage);
    }
  };

  // Helper function to update general settings
  const updateGeneralSetting = (field: string, value: any) => {
    if (!editedSettings) return;
    setEditedSettings((prev: any) => {
      if (!prev) return null;
      return {
        ...prev,
        general: {
          ...prev.general,
          [field]: value,
        },
      };
    });
  };

  // Loading state
  if (isLoading) {
    return (
      <>
        <AdminReturnBanner />
        <DashboardLayout
          userRole="company"
          userEmail={userEmail}
          userName={userName}
        >
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-8 w-32 mb-2" />
                <Skeleton className="h-4 w-64" />
              </div>
              <Skeleton className="h-10 w-32" />
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {[...Array(4)].map((_, i) => (
                <Card key={i}>
                  <CardHeader>
                    <Skeleton className="h-6 w-32 mb-2" />
                    <Skeleton className="h-4 w-48" />
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[...Array(3)].map((_, j) => (
                        <div key={j} className="space-y-2">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-10 w-full" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </DashboardLayout>
      </>
    );
  }

  // Error state
  if (error) {
    return (
      <>
        <AdminReturnBanner />
        <DashboardLayout
          userRole="company"
          userEmail="<EMAIL>"
          userName="Company User"
        >
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
              <p className="text-gray-600">
                Configure your company settings and preferences
              </p>
            </div>
            <Card>
              <CardContent className="text-center py-12">
                <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-500" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Failed to load settings
                </h3>
                <p className="text-gray-600 mb-4">
                  There was an error loading your company settings.
                </p>
                <Button onClick={() => window.location.reload()}>
                  Try Again
                </Button>
              </CardContent>
            </Card>
          </div>
        </DashboardLayout>
      </>
    );
  }

  if (!editedSettings || !editedSettings.general) {
    return null;
  }

  const settingsTabs = [
    {
      id: "general",
      label: "General",
      icon: Globe,
      content: (
        <GeneralSettings
          data={{
            timezone: editedSettings.general.timezone,
            dateFormat: editedSettings.general.dateFormat,
          }}
          onChange={updateGeneralSetting}
          type="company"
        />
      ),
    },
  ];

  return (
    <>
      <AdminReturnBanner />
      <DashboardLayout
        userRole="company"
        userEmail="<EMAIL>"
        userName="Company User"
      >
        <SettingsLayout
          title="Settings"
          description="Configure your company settings and preferences"
          tabs={settingsTabs}
          onSave={handleSave}
          isSaving={updateSettingsMutation.isPending}
          hasChanges={
            JSON.stringify(settings) !== JSON.stringify(editedSettings)
          }
          defaultTab="general"
        />
      </DashboardLayout>
    </>
  );
}
