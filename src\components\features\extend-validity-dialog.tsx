"use client";

import { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Clock, X, Check } from "lucide-react";
import { format, addDays, addMonths, addYears } from "date-fns";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { Company } from "@/stores/useCompanyStore";
import { useExtendValidityMutation } from "@/hooks/queries/useCompaniesQuery";
import DatePicker from "@/components/DatePicker";

interface ExtendValidityDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  companies: Company[];
  isBulk?: boolean;
}

type ExtensionType = "days" | "months" | "years" | "custom";

export function ExtendValidityDialog({
  open,
  onOpenChange,
  companies,
  isBulk = false,
}: ExtendValidityDialogProps) {
  const [extensionType, setExtensionType] = useState<ExtensionType>("days");
  const [extensionValue, setExtensionValue] = useState("30");
  const [customDate, setCustomDate] = useState<Date>(new Date());
  const [errors, setErrors] = useState<Record<string, string>>({});

  const extendValidityMutation = useExtendValidityMutation();

  const calculateNewExpiryDate = (currentExpiry: string) => {
    const currentDate = new Date(currentExpiry);

    switch (extensionType) {
      case "days":
        return addDays(currentDate, parseInt(extensionValue) || 0);
      case "months":
        return addMonths(currentDate, parseInt(extensionValue) || 0);
      case "years":
        return addYears(currentDate, parseInt(extensionValue) || 0);
      case "custom":
        return customDate;
      default:
        return currentDate;
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (extensionType !== "custom") {
      const value = parseInt(extensionValue);
      if (!value || value <= 0) {
        newErrors.extensionValue = "Please enter a valid positive number";
      }
      if (extensionType === "days" && value > 3650) {
        newErrors.extensionValue = "Maximum 3650 days (10 years) allowed";
      }
      if (extensionType === "months" && value > 120) {
        newErrors.extensionValue = "Maximum 120 months (10 years) allowed";
      }
      if (extensionType === "years" && value > 10) {
        newErrors.extensionValue = "Maximum 10 years allowed";
      }
    } else {
      if (customDate <= new Date()) {
        newErrors.customDate = "Custom date must be in the future";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || companies.length === 0) {
      return;
    }

    try {
      const extensionData = {
        type: extensionType,
        value:
          extensionType === "custom" ? undefined : parseInt(extensionValue),
        customDate:
          extensionType === "custom" ? customDate.toISOString() : undefined,
      };

      await extendValidityMutation.mutateAsync({
        companyIds: companies.map((c) => c.id),
        ...extensionData,
      });

      const companyText =
        companies.length === 1 ? "company" : `${companies.length} companies`;
      toast.success(`Validity extended for ${companyText} successfully`);
      onOpenChange(false);
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.error || "Failed to extend validity";
      toast.error(errorMessage);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    setErrors({});
    setExtensionType("days");
    setExtensionValue("30");
    setCustomDate(new Date());
  };

  const getPreviewText = () => {
    if (companies.length === 0) return "";

    const sampleCompany = companies[0];
    const newDate = calculateNewExpiryDate(sampleCompany.expiresAt);

    if (companies.length === 1) {
      return `${sampleCompany.name} validity will be extended to ${format(
        newDate,
        "PPP"
      )}`;
    } else {
      return `${companies.length} companies will have their validity extended`;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            Extend Validity
          </DialogTitle>
          <DialogDescription>
            {isBulk
              ? `Extend validity for ${companies.length} selected companies`
              : companies.length === 1
              ? `Extend validity for ${companies[0]?.name}`
              : "Extend validity"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Extension Type */}
          <div className="space-y-2">
            <Label>Extension Type</Label>
            <Select
              value={extensionType}
              onValueChange={(value: ExtensionType) => setExtensionType(value)}
              disabled={extendValidityMutation.isPending}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="days">Days</SelectItem>
                <SelectItem value="months">Months</SelectItem>
                <SelectItem value="years">Years</SelectItem>
                <SelectItem value="custom">Custom Date</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Extension Value */}
          {extensionType !== "custom" && (
            <div className="space-y-2">
              <Label htmlFor="extensionValue">Number of {extensionType}</Label>
              <Input
                id="extensionValue"
                type="number"
                min="1"
                value={extensionValue}
                onChange={(e) => setExtensionValue(e.target.value)}
                className={errors.extensionValue ? "border-red-500" : ""}
                disabled={extendValidityMutation.isPending}
                placeholder={`Enter number of ${extensionType}`}
              />
              {errors.extensionValue && (
                <p className="text-sm text-red-600">{errors.extensionValue}</p>
              )}
            </div>
          )}

          {/* Custom Date */}
          {extensionType === "custom" && (
            <div className="space-y-2">
              <Label>New Expiry Date</Label>
              <DatePicker
                value={customDate}
                onChange={(date) => setCustomDate(date || new Date())}
                placeholder="Select new expiry date"
                className="w-full"
                disabled={extendValidityMutation.isPending}
                minDate={new Date()}
                dateFormat="MM/dd/yyyy"
              />
              {errors.customDate && (
                <p className="text-sm text-red-600">{errors.customDate}</p>
              )}
            </div>
          )}

          {/* Preview */}
          {companies.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <h4 className="text-sm font-medium text-blue-900 mb-1">
                Preview
              </h4>
              <p className="text-sm text-blue-700">{getPreviewText()}</p>

              {companies.length === 1 && (
                <div className="mt-2 text-xs text-blue-600">
                  Current expiry:{" "}
                  {format(new Date(companies[0].expiresAt), "PPP")}
                </div>
              )}
            </div>
          )}

          {/* Quick Actions */}
          <div className="grid grid-cols-3 gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                setExtensionType("days");
                setExtensionValue("30");
              }}
              disabled={extendValidityMutation.isPending}
            >
              +30 Days
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                setExtensionType("months");
                setExtensionValue("6");
              }}
              disabled={extendValidityMutation.isPending}
            >
              +6 Months
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                setExtensionType("years");
                setExtensionValue("1");
              }}
              disabled={extendValidityMutation.isPending}
            >
              +1 Year
            </Button>
          </div>

          {/* Actions */}
          <div className="flex space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={extendValidityMutation.isPending}
              className="flex-1"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                extendValidityMutation.isPending || companies.length === 0
              }
              className="flex-1"
            >
              <Check className="h-4 w-4 mr-2" />
              {extendValidityMutation.isPending
                ? "Extending..."
                : "Extend Validity"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
