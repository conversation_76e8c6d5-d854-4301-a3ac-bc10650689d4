import React from "react";
import { Clock } from "lucide-react";
import { SettingsSection } from "../settings-section";
import { SettingsField } from "../settings-field";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface WorkingHoursSettingsData {
  workingHours?: {
    start: string;
    end: string;
  };
  workingDays?: string[];
}

interface WorkingHoursSettingsProps {
  data: WorkingHoursSettingsData;
  onChange: (field: keyof WorkingHoursSettingsData, value: any) => void;
  type: "admin" | "company";
}

const daysOfWeek = [
  { value: "Monday", label: "Monday" },
  { value: "Tuesday", label: "Tuesday" },
  { value: "Wednesday", label: "Wednesday" },
  { value: "Thursday", label: "Thursday" },
  { value: "Friday", label: "Friday" },
  { value: "Saturday", label: "Saturday" },
  { value: "Sunday", label: "Sunday" },
];

export function WorkingHoursSettings({ data, onChange, type }: WorkingHoursSettingsProps) {
  // Only show for companies
  if (type !== "company") {
    return null;
  }

  const handleWorkingDayChange = (day: string, checked: boolean) => {
    const currentDays = data.workingDays || [];
    let newDays;
    
    if (checked) {
      newDays = [...currentDays, day];
    } else {
      newDays = currentDays.filter(d => d !== day);
    }
    
    onChange("workingDays", newDays);
  };

  return (
    <SettingsSection
      title="Working Hours"
      description="Configure your company's working hours and days"
      icon={Clock}
    >
      {data.workingHours && (
        <>
          <SettingsField
            type="time"
            label="Start Time"
            value={data.workingHours.start}
            onChange={(value) => onChange("workingHours", { ...data.workingHours!, start: value })}
            description="Daily work start time"
          />

          <SettingsField
            type="time"
            label="End Time"
            value={data.workingHours.end}
            onChange={(value) => onChange("workingHours", { ...data.workingHours!, end: value })}
            description="Daily work end time"
          />
        </>
      )}

      {data.workingDays && (
        <div className="space-y-2">
          <Label>Working Days</Label>
          <div className="grid grid-cols-2 gap-3">
            {daysOfWeek.map((day) => (
              <div key={day.value} className="flex items-center space-x-2">
                <Checkbox
                  id={day.value}
                  checked={data.workingDays!.includes(day.value)}
                  onCheckedChange={(checked) => handleWorkingDayChange(day.value, checked as boolean)}
                />
                <Label htmlFor={day.value} className="text-sm font-normal">
                  {day.label}
                </Label>
              </div>
            ))}
          </div>
          <p className="text-sm text-gray-600">
            Select the days when your company operates
          </p>
        </div>
      )}
    </SettingsSection>
  );
}
