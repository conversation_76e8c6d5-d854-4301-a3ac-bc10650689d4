import { useQuery } from "@tanstack/react-query";

interface SiteNameResponse {
  siteName: string;
}

// Fetch site name from admin settings
const fetchSiteName = async (): Promise<SiteNameResponse> => {
  const response = await fetch("/api/admin/settings/site-name");

  if (!response.ok) {
    // Return default if API fails
    return { siteName: "SRITechnologies" };
  }

  return response.json();
};

// Hook to get site name with fallback
export const useSiteName = () => {
  const { data, isLoading, error } = useQuery({
    queryKey: ["site-name"],
    queryFn: fetchSiteName,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 1,
  });

  return {
    siteName: data?.siteName || "SRITechnologies",
    isLoading,
    error,
  };
};
