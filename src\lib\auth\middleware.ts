import { NextRequest, NextResponse } from "next/server";
import { verifyJWT, JWTPayload } from "@/lib/auth";

export interface AuthenticatedRequest extends NextRequest {
  user?: JWTPayload;
}

export function withAuth<T = Record<string, unknown>>(
  handler: (req: AuthenticatedRequest, context?: T) => Promise<NextResponse>
) {
  return async (req: NextRequest, context?: T): Promise<NextResponse> => {
    try {
      // Get token from cookie
      const token = req.cookies.get("auth-token")?.value;

      if (!token) {
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401 }
        );
      }

      // Verify token
      const payload = verifyJWT(token);

      if (!payload) {
        return NextResponse.json(
          { error: "Invalid or expired token" },
          { status: 401 }
        );
      }

      // Add user to request
      const authenticatedReq = req as AuthenticatedRequest;
      authenticatedReq.user = payload;

      return handler(authenticatedReq, context);
    } catch (error) {
      console.error("Auth middleware error:", error);
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 401 }
      );
    }
  };
}

export function withAdminAuth<T = Record<string, unknown>>(
  handler: (req: AuthenticatedRequest, context?: T) => Promise<NextResponse>
) {
  return withAuth<T>(
    async (req: AuthenticatedRequest, context?: T): Promise<NextResponse> => {
      if (req.user?.role !== "admin") {
        return NextResponse.json(
          { error: "Admin access required" },
          { status: 403 }
        );
      }

      return handler(req, context);
    }
  );
}

export function withCompanyAuth<T = Record<string, unknown>>(
  handler: (req: AuthenticatedRequest, context?: T) => Promise<NextResponse>
) {
  return withAuth<T>(
    async (req: AuthenticatedRequest, context?: T): Promise<NextResponse> => {
      if (req.user?.role !== "company") {
        return NextResponse.json(
          { error: "Company access required" },
          { status: 403 }
        );
      }

      return handler(req, context);
    }
  );
}

// Helper to get current user from request
export function getCurrentUser(req: NextRequest): JWTPayload | null {
  try {
    const token = req.cookies.get("auth-token")?.value;

    if (!token) {
      return null;
    }

    return verifyJWT(token);
  } catch (error) {
    console.error("Get current user error:", error);
    return null;
  }
}
