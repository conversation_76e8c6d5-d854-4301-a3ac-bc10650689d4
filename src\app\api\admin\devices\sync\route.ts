import { NextRequest, NextResponse } from 'next/server';
import { externalDeviceAPI } from '@/lib/api/external-device-api';
import { verifyAdminAuth } from '@/lib/auth';

// POST /api/admin/devices/sync - Sync users and settings from source device to destination devices
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { sourceDeviceSerial, destinationDeviceSerials, syncType = 'users' } = body;

    // Validate required fields
    if (!sourceDeviceSerial || !destinationDeviceSerials || !Array.isArray(destinationDeviceSerials)) {
      return NextResponse.json({
        error: 'Source device serial and destination device serials array are required'
      }, { status: 400 });
    }

    if (destinationDeviceSerials.length === 0) {
      return NextResponse.json({
        error: 'At least one destination device is required'
      }, { status: 400 });
    }

    console.log(`Starting sync from ${sourceDeviceSerial} to ${destinationDeviceSerials.length} devices`);
    console.log(`Sync type: ${syncType}`);

    const results = [];
    let successCount = 0;
    let failureCount = 0;

    // Sync to each destination device
    for (const destinationSerial of destinationDeviceSerials) {
      try {
        console.log(`Syncing from ${sourceDeviceSerial} to ${destinationSerial}...`);

        // Call external API to sync device
        const response = await externalDeviceAPI.syncDevice(sourceDeviceSerial, destinationSerial);

        if (response.status === '200') {
          results.push({
            destinationDevice: destinationSerial,
            status: 'success',
            message: response.msg || 'Sync completed successfully'
          });
          successCount++;
          console.log(`✅ Successfully synced to ${destinationSerial}`);
        } else {
          results.push({
            destinationDevice: destinationSerial,
            status: 'error',
            message: response.msg || 'Sync failed'
          });
          failureCount++;
          console.log(`❌ Failed to sync to ${destinationSerial}: ${response.msg}`);
        }

        // Add a small delay between sync operations to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        results.push({
          destinationDevice: destinationSerial,
          status: 'error',
          message: `Network error: ${errorMessage}`
        });
        failureCount++;
        console.log(`❌ Error syncing to ${destinationSerial}:`, error);
      }
    }

    const summary = {
      total: destinationDeviceSerials.length,
      successful: successCount,
      failed: failureCount,
      sourceDevice: sourceDeviceSerial,
      syncType,
      completedAt: new Date().toISOString()
    };

    console.log('Sync operation completed:', summary);

    return NextResponse.json({
      success: failureCount === 0,
      message: failureCount === 0 
        ? `Successfully synced to all ${successCount} devices`
        : `Sync completed with ${successCount} successes and ${failureCount} failures`,
      data: {
        summary,
        results
      }
    });

  } catch (error) {
    console.error('Error in device sync operation:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// GET /api/admin/devices/sync/status - Get sync operation status (for future use)
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // For now, return a simple status
    // In the future, this could track ongoing sync operations
    return NextResponse.json({
      success: true,
      data: {
        availableOperations: [
          'users', // Sync user data between devices
          'settings', // Sync device settings (future implementation)
          'attendance' // Sync attendance logs (future implementation)
        ],
        status: 'ready'
      }
    });

  } catch (error) {
    console.error('Error getting sync status:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
