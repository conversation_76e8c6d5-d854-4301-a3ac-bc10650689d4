import { NextRequest, NextResponse } from "next/server";
import { withCompanyAuth, AuthenticatedRequest } from "@/lib/auth/middleware";
import { prisma } from "@/lib/prisma";

// GET /api/dashboard/devices/[deviceId]/info - Get device info for company users
async function getDeviceInfo(req: AuthenticatedRequest) {
  try {
    const user = req.user!;

    // Extract deviceId from URL path
    const url = new URL(req.url!);
    const pathParts = url.pathname.split("/");
    const deviceId = pathParts[pathParts.length - 2]; // Get deviceId from path
    const deviceSerial = deviceId;

    console.log(
      `Company ${user.companyId} requesting info for device: ${deviceSerial}`
    );

    // Check if device is allocated to this company
    const allocation = await prisma.allocation.findFirst({
      where: {
        deviceSerialNo: deviceSerial,
        companyId: user.companyId,
      },
      include: {
        company: true,
      },
    });

    if (!allocation) {
      return NextResponse.json(
        {
          error: "Device not allocated to your company",
        },
        { status: 403 }
      );
    }

    // Get cached device data from database
    const cachedDevice = await prisma.deviceApiCache.findUnique({
      where: { deviceSerialNumber: deviceSerial },
    });

    if (!cachedDevice) {
      return NextResponse.json(
        {
          error: "Device not found in cache",
        },
        { status: 404 }
      );
    }

    // Transform cached data to device info format (same as admin API)
    const deviceInfo = {
      deviceName: cachedDevice.deviceName,
      serialNumber: cachedDevice.deviceSerialNumber,
      model: cachedDevice.modelNo,
      modelName: cachedDevice.modelName,
      location: cachedDevice.location,
      ipAddress: cachedDevice.endpointUrl
        ? cachedDevice.endpointUrl
        : "Unknown",
      port: cachedDevice.endpointUrl ? cachedDevice.endpointUrl : "Unknown",
      status: "Unknown", // This would come from real-time status
      lastSeen: cachedDevice.lastApiUpdate?.toISOString() || null,
      firmware: "Unknown", // Not available in external API
      capacity: 1000, // Default capacity
      usedSpace: cachedDevice.userCount || 0,
      temperature: 25, // Default temperature
      uptime: "Unknown", // Not available in external API
      userCount: cachedDevice.userCount,
      logCount: cachedDevice.logCount,
      timeZone: cachedDevice.timeZone,
      features: {
        faceRecognition: cachedDevice.isface,
        fingerprintScanner: cachedDevice.isfinger,
        cardReader: cachedDevice.iscard,
        passwordAuth: cachedDevice.ispassword,
      },
      biometricCounts: {
        fingerprints: cachedDevice.fingerprintCount,
        faces: cachedDevice.faceCount,
        cards: cachedDevice.cardCount,
        passwords: cachedDevice.passwordCount,
      },
      rawApiResponse: cachedDevice.apiResponse,
    };

    console.log(`Successfully retrieved device info for ${deviceSerial}`);

    return NextResponse.json({
      success: true,
      deviceInfo,
      meta: {
        deviceSerial,
        lastUpdated: cachedDevice.lastApiUpdate?.toISOString(),
        cacheAge: cachedDevice.lastApiUpdate
          ? Date.now() - cachedDevice.lastApiUpdate.getTime()
          : null,
      },
    });
  } catch (error) {
    console.error("Error fetching device info:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// // Helper functions to extract IP and port from URL
// function extractIpFromUrl(url: string): string {
//   try {
//     if (url.includes("://")) {
//       return new URL(url).hostname;
//     } else if (url.includes(":")) {
//       return url.split(":")[0];
//     }
//     return url;
//   } catch {
//     return "Unknown";
//   }
// }

// function extractPortFromUrl(url: string): number {
//   try {
//     if (url.includes("://")) {
//       const urlObj = new URL(url);
//       return urlObj.port ? parseInt(urlObj.port) : 4370;
//     } else if (url.includes(":")) {
//       const parts = url.split(":");
//       const port = parseInt(parts[parts.length - 1]);
//       return isNaN(port) ? 4370 : port;
//     }
//     return 4370;
//   } catch {
//     return 4370;
//   }
// }

export const GET = withCompanyAuth(getDeviceInfo);
