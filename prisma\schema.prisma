generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "linux-musl"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Admin {
  id               String    @id @default(cuid())
  name             String
  email            String    @unique
  passwordHash     String    @map("password_hash")
  phone            String?
  department       String?
  bio              String?
  avatar           String?
  twoFactorSecret  String?   @map("two_factor_secret")
  twoFactorEnabled Boolean   @default(false) @map("two_factor_enabled")
  lastLogin        DateTime? @map("last_login")
  loginCount       Int       @default(0) @map("login_count")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  @@map("admins")
}

model Company {
  id               String           @id @default(cuid())
  name             String
  organizationId   String           @unique @map("organization_id")
  email            String           @unique
  passwordHash     String           @map("password_hash")
  loginTokenHash   String?          @unique @map("login_token_hash")
  userType         UserType         @map("user_type")
  status           CompanyStatus    @default(ACTIVE)
  expiresAt        DateTime         @map("expires_at")
  description      String?
  documentUrl      String?          @map("document_url")
  createdAt        DateTime         @default(now()) @map("created_at")
  updatedAt        DateTime         @updatedAt @map("updated_at")
  loginToken       String?          @map("login_token")
  address          String?
  contactPerson    String?          @map("contact_person")
  lastLogin        DateTime?        @map("last_login")
  loginCount       Int              @default(0) @map("login_count")
  phone            String?
  avatar           String?
  twoFactorEnabled Boolean          @default(false) @map("two_factor_enabled")
  twoFactorSecret  String?          @map("two_factor_secret")
  website          String?
  externalApiEndpoint String? @map("external_api_endpoint")
  allocations      Allocation[]
  settings         CompanySettings?

  @@map("companies")
}

model Allocation {
  id             String       @id @default(cuid())
  companyId      String       @map("company_id")
  deviceSerialNo String       @map("device_serial_no")
  createdAt      DateTime     @default(now()) @map("created_at")
  company        Company      @relation(fields: [companyId], references: [id], onDelete: Cascade)
  device         DeviceApiCache @relation(fields: [deviceSerialNo], references: [deviceSerialNumber], onDelete: Cascade)

  @@unique([companyId, deviceSerialNo])
  @@map("allocations")
}

model AdminSettings {
  id                      String   @id @default(cuid())
  siteName                String   @default("Smart Attendance Portal") @map("site_name")
  siteDescription         String   @default("Advanced attendance management system") @map("site_description")
  defaultTimeZoneId       Int      @default(57) @map("default_time_zone_id")
  dateFormat              String   @default("MM/DD/YYYY") @map("date_format")
  logoUrl                 String   @default("") @map("logo_url")
  faviconUrl              String   @default("") @map("favicon_url")
  language                String   @default("en")
  maintenanceMode         Boolean  @default(false) @map("maintenance_mode")
  sessionTimeout          Int      @default(480) @map("session_timeout")
  maxLoginAttempts        Int      @default(5) @map("max_login_attempts")
  passwordMinLength       Int      @default(8) @map("password_min_length")
  requireTwoFactor        Boolean  @default(false) @map("require_two_factor")
  allowPasswordReset      Boolean  @default(true) @map("allow_password_reset")
  ipWhitelist             String   @default("") @map("ip_whitelist")
  emailNotifications      Boolean  @default(true) @map("email_notifications")
  deviceOfflineAlerts     Boolean  @default(true) @map("device_offline_alerts")
  companyExpiryAlerts     Boolean  @default(true) @map("company_expiry_alerts")
  systemMaintenanceAlerts Boolean  @default(true) @map("system_maintenance_alerts")
  adminEmail              String   @default("<EMAIL>") @map("admin_email")
  deviceOfflineMinutes    Int      @default(30) @map("device_offline_minutes")
  companyExpiryDays       Int      @default(7) @map("company_expiry_days")
  autoSyncInterval        Int      @default(60) @map("auto_sync_interval")
  maxDevicesPerCompany    Int      @default(10) @map("max_devices_per_company")
  defaultDeviceTimeout    Int      @default(300) @map("default_device_timeout")
  allowBulkOperations     Boolean  @default(true) @map("allow_bulk_operations")
  requireDeviceApproval   Boolean  @default(false) @map("require_device_approval")
  autoBackup              Boolean  @default(true) @map("auto_backup")
  backupFrequency         String   @default("daily") @map("backup_frequency")
  retentionDays           Int      @default(30) @map("retention_days")
  backupLocation          String   @default("/backups") @map("backup_location")
  createdAt               DateTime @default(now()) @map("created_at")
  updatedAt               DateTime @updatedAt @map("updated_at")

  @@map("admin_settings")
}

model CompanySettings {
  id                       String   @id @default(cuid())
  companyId                String   @unique @map("company_id")
  companyName              String   @map("company_name")
  timezone                 String   @default("UTC")
  dateFormat               String   @default("MM/DD/YYYY") @map("date_format")
  workingHoursStart        String   @default("09:00") @map("working_hours_start")
  workingHoursEnd          String   @default("17:00") @map("working_hours_end")
  workingDays              String   @default("Monday,Tuesday,Wednesday,Thursday,Friday") @map("working_days")
  emailAlerts              Boolean  @default(true) @map("email_alerts")
  attendanceReminders      Boolean  @default(true) @map("attendance_reminders")
  lateArrivalAlerts        Boolean  @default(true) @map("late_arrival_alerts")
  absenteeAlerts           Boolean  @default(true) @map("absentee_alerts")
  deviceOfflineAlerts      Boolean  @default(true) @map("device_offline_alerts")
  weeklyReports            Boolean  @default(true) @map("weekly_reports")
  monthlyReports           Boolean  @default(false) @map("monthly_reports")
  graceTime                Int      @default(15) @map("grace_time")
  autoClockOut             Boolean  @default(false) @map("auto_clock_out")
  autoClockOutTime         String   @default("18:00") @map("auto_clock_out_time")
  allowManualEntry         Boolean  @default(false) @map("allow_manual_entry")
  requireApproval          Boolean  @default(true) @map("require_approval")
  trackBreaks              Boolean  @default(false) @map("track_breaks")
  maxWorkingHours          Int      @default(12) @map("max_working_hours")
  allowMultipleCheckIns    Boolean  @default(false) @map("allow_multiple_check_ins")
  deviceSyncInterval       Int      @default(30) @map("device_sync_interval")
  offlineDataRetention     Int      @default(7) @map("offline_data_retention")
  faceRecognitionThreshold Int      @default(80) @map("face_recognition_threshold")
  fingerprintThreshold     Int      @default(85) @map("fingerprint_threshold")
  createdAt                DateTime @default(now()) @map("created_at")
  updatedAt                DateTime @updatedAt @map("updated_at")
  company                  Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@map("company_settings")
}

enum UserType {
  API_USER
  SCHOOL_MANAGEMENT
  EMPLOYEE_MANAGEMENT
}

enum CompanyStatus {
  ACTIVE
  DEACTIVATED
}

model TimeZone {
  id           String @id @default(cuid())
  timezoneId   Int    @unique @map("timezone_id")
  timezoneName String @map("timezone_name")
  timezoneTime String @map("timezone_time")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("timezones")
}

model DeviceApiCache {
  id                   String   @id @default(cuid())
  deviceSerialNumber   String   @unique @map("device_serial_number")
  apiResponse          Json     @map("api_response")
  deviceName           String   @map("device_name")
  modelNo              String   @map("model_no")
  modelName            String   @map("model_name")
  location             String
  userCount            Int      @map("user_count")
  logCount             Int      @map("log_count")
  timeZone             Int      @map("time_zone")
  endpointUrl          String   @map("endpoint_url")
  isface               Boolean
  isfinger             Boolean
  iscard               Boolean
  ispassword           Boolean
  fingerprintCount     Int      @map("fingerprint_count")
  faceCount            Int      @map("face_count")
  cardCount            Int      @map("card_count")
  passwordCount        Int      @map("password_count")
  lastApiUpdate        DateTime @map("last_api_update")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")
  allocations          Allocation[]

  @@map("device_api_cache")
}
