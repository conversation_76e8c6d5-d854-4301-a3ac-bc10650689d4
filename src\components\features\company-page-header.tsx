"use client";

import { memo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, RefreshCw } from "lucide-react";

interface CompanyPageHeaderProps {
  onShowRegistrationForm: () => void;
  onRefreshData: () => void;
}

export const CompanyPageHeader = memo(function CompanyPageHeader({
  onShowRegistrationForm,
  onRefreshData,
}: CompanyPageHeaderProps) {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Company Management</h1>
        <p className="text-gray-600">
          Manage company registrations and device allocations
        </p>
      </div>
      <div className="flex space-x-3">
        <Button variant="outline" onClick={onRefreshData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
        <Button onClick={onShowRegistrationForm}>
          <Plus className="h-4 w-4 mr-2" />
          Register Company
        </Button>
      </div>
    </div>
  );
});
