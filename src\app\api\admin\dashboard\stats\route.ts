import { NextRequest, NextResponse } from 'next/server'
import { withAdminAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { prisma } from '@/lib/prisma'
import { externalDeviceAPI } from '@/lib/api/external-device-api'

async function getStats(req: AuthenticatedRequest) {
  try {
    // Try to get company stats from database
    let totalCompanies = 0
    let activeCompanies = 0
    let expiredCompanies = 0
    let allocatedDevices = 0

    try {
      const [
        totalCount,
        activeCount,
        expiredCount
      ] = await Promise.all([
        prisma.company.count(),
        prisma.company.count({
          where: {
            status: 'ACTIVE',
            expiresAt: {
              gt: new Date()
            }
          }
        }),
        prisma.company.count({
          where: {
            OR: [
              { status: 'DEACTIVATED' },
              { expiresAt: { lt: new Date() } }
            ]
          }
        })
      ])

      totalCompanies = totalCount
      activeCompanies = activeCount
      expiredCompanies = expiredCount

      // Get allocation count
      const allocationCount = await prisma.allocation.count()
      allocatedDevices = allocationCount

    } catch (dbError) {
      console.warn('Database not available, using zero values:', dbError)
      // Use zero values when database is not available
      totalCompanies = 0
      activeCompanies = 0
      expiredCompanies = 0
      allocatedDevices = 0
    }

    // Get device stats from external API
    let totalDevices = 0

    try {
      const deviceResponse = await externalDeviceAPI.selectDeviceList()
      if (deviceResponse.status === '200' && deviceResponse.data) {
        totalDevices = deviceResponse.data.length
      }
    } catch (error) {
      console.warn('Failed to fetch device data from external API, using zero value:', error)
      // Use zero value when external API is not available
      totalDevices = 0
    }

    const unallocatedDevices = Math.max(0, totalDevices - allocatedDevices)

    const stats = {
      totalCompanies,
      activeCompanies,
      expiredCompanies,
      totalDevices,
      allocatedDevices,
      unallocatedDevices
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Get dashboard stats error:', error)

    return NextResponse.json(
      { error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    )
  }
}

export const GET = withAdminAuth(getStats)
