---
type: "always_apply"
---

# Nuzn Infotech API Documentation

This document provides a comprehensive overview of the Nuzn Infotech API, which is designed to manage biometric devices and their associated data. The API enables users to perform a wide range of operations, including adding, editing, and deleting devices, as well as managing user data and retrieving attendance logs.

---

## **API Endpoint**

The base URL for all API calls is:
[cite_start]`http://103.240.90.194:7766/api/v1/` [cite: 5]
all the api calls are POST method only
---

### **http://103.240.90.194:7766/api/v1/SELECTTIMEZONE**

[cite_start]This API is used to get all time zones, which is required when adding a machine. [cite: 5]

* **Input Parameters:** `[{}]`
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Success",
      "data": [
        {
          "TIMEZONE_ID": 1,
          "TIMEZONE_NAME": "(GMT-12:00) International Date Line West",
          "TIMEZONE TIME": "-12:00"
        },
        {}
      ]
    }
    ```
    [cite_start][cite: 5]

---

### **http://103.240.90.194:7766/api/v1/ADDEDITDEVICE**

[cite_start]This API is used to add or edit a device. [cite: 5]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008",
        "DEVICENAME": "NUZN",
        "MODELNO": "AI SERIES",
        "TIMEZONEID": "57",
        "LOCATION": "DELHI",
        "ENDPOINT URL": ""
      },
      {
        "DEVICESLNO": "0123D1c170009",
        "DEVICENAME": "NUZN1",
        "MODELNO": "AI SERIES",
        "TIMEZONEID": "57",
        "LOCATION": "DELHI",
        "ENDPOINT_URL": "http//ab.com:"
      }
    ]
    ```
    [cite_start][cite: 5]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Device Add/Edit Success",
      "data": null
    }
    ```
    [cite_start][cite: 5]

---

### **http://103.240.90.194:7766/api/v1/DELETDEVICE**

[cite_start]This API is used to delete a machine from the database. [cite: 5]

* **Input Parameters:**

    ```json
    [
      { "DEVICESLNO": "0123D1c1700088" },
      { "DEVICESLNO": "0123D1c170009" }
    ]
    ```
    [cite_start][cite: 5]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Device Deleted Success",
      "data": null
    }
    ```
    [cite_start][cite: 5]

---

### **http://103.240.90.194:7766/api/v1/SELECTDEVICELIST**

[cite_start]This API is used to get a list of all devices saved in the database. [cite: 5]

* [cite_start]**Input Parameters:** `[{}]` [cite: 5]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Success",
      "data": [
        {
          "MACHINE ID": 1,
          "MACHINE NAME": "NUZN",
          "MODEL NO": "AI SERIES",
          "SERIAL NO": "0123D1c170008",
          "TIME ZONE": 57,
          "MACHINE LOCATION": "DELHI",
          "STATUS": {},
          "LOG COUNT": 12,
          "USER COUNT": 6,
          "LAST_UPDATE_DATE": "2025-03-15T15:55:44",
          "ISONLINECHECK": {},
          "ISALIAS": 1,
          "ISFACE": 1,
          "ISFINGER": 0,
          "ISCARD": 1,
          "ISPASSWORD": 1,
          "FINGERPRINTCOUNT": 0,
          "FACECOUNT": 1,
          "PASSWORDCOUNT": 5,
          "CARDCOUNT": 1,
          "MODEL NAME": "AiFace",
          "ENDPOINT URL": ""
        }
      ]
    }
    ```
    [cite_start][cite: 5, 12]

---

### **http://103.240.90.194:7766/api/v1/SENDCMDATTENDANCELOG**

[cite_start]This API is used to send a command to the machine to download the attendance log. [cite: 12]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008",
        "FROMDATE": "2025-01-02 00:00",
        "TODATE": "2025-03-15 23:50"
      },
      {
        "DEVICESLNO": "0123D1c1700088",
        "FROMDATE": "2025-01-02 00:00",
        "TODATE": "2025-03-15 23:50"
      }
    ]
    ```
    [cite_start][cite: 12]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Get Attendance Log Command sent Success to Machine after some time Plase call GETATTENDANCELOG ",
      "data": null
    }
    ```
    [cite_start][cite: 12]

---

### **http://103.240.90.194:7766/api/v1/GETATTENDANCELOG**

[cite_start]This API is used to get the attendance log from the table. [cite: 12]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008",
        "FROMDATE": "2025-01-02 00:00",
        "TODATE": "2025-03-15 23:50"
      }
    ]
    ```
    [cite_start][cite: 12]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Success",
      "data": [
        {
          "EnrollmentNo": "4",
          "DeviceSlno": "0123D1c170008",
          "PunchDateTime": "2025-03-15T12 10:17",
          "inout_value": 0,
          "Mode value": 8,
          "Event_value": 0
        }
      ]
    }
    ```
    [cite_start][cite: 12]

---

### **http://103.240.90.194:7766/api/v1/GETCMDUSERDATA**

[cite_start]This API sends a command to the machine to get user data (e.g., face, finger, name, card) and save it in a table. [cite: 12]

* **Input Parameters:**

    ```json
    [
      { "DEVICESLNO": "0123D1c1700088" },
      { "DEVICESLNO": "0123D1c170008" }
    ]
    ```
    [cite_start][cite: 12]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Get User Data Command sent Success to Machine after some time Please call GETUSERDATA ",
      "data": null
    }
    ```
    [cite_start][cite: 12]

---

### **http://103.240.90.194:7766/api/v1/GETUSERDATA**

[cite_start]This API gets user data (e.g., face, finger, name, card) from the table. [cite: 12]

* **Input Parameters:**

    ```json
    [
      { "DEVICESLNO": "0123D1c1700088" },
      { "DEVICESLNO": "0123D1c170008" }
    ]
    ```
    [cite_start][cite: 12]
* **Response:**

    ```json
    {
      "status": "Success",
      "msg": "Success",
      "data": [
        {
          "RowId": 1,
          "DevicesSlno": "0123D1c170008",
          "EnrollmentNo": "4",
          "Name": "",
          "Admin": 0,
          "FingerData": {},
          "FaceData": {},
          "Privilege": {},
          "Enabled": {},
          "Card": {},
          "PWD": {}
        }
      ]
    }
    ```
    [cite_start][cite: 12, 19]

---

### **http://103.240.90.194:7766/api/v1/GETSINGLEUSERDATA**

[cite_start]This API is used to get single user data (e.g., face, finger, name, card) from the table. [cite: 19]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "AYSF21075005",
        "USERID": "1"
      }
    ]
    ```
    [cite_start][cite: 19]
* **Response:**

    ```json
    {
      "status": "Success",
      "msg": "Success",
      "data": [
        {
          "RowId": 1,
          "DevicesSlno": "0123D1c170008",
          "EnrollmentNo": "4",
          "Name": "",
          "Admin": 0,
          "FingerData": {},
          "FaceData": {},
          "Privilege": {},
          "Enabled": {},
          "Card": {},
          "PWD": {}
        }
      ]
    }
    ```
    [cite_start][cite: 19]

---

### **http://103.240.90.194:7766/api/v1/SYNCDEVICE**

[cite_start]This API is used to set user data from a source machine to a destination machine. [cite: 19]

* **Input Parameters:**

    ```json
    [
      {
        "SOURCEDEVICESLNO": "AYSF21075005",
        "DESTINATIONDEVICESLNO": "0123D1c170008"
      }
    ]
    ```
    [cite_start][cite: 19]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Add User In Device Success.",
      "data": null
    }
    ```
    [cite_start][cite: 19]

---

### **http://103.240.90.194:7766/api/v1/ADDUSER**

[cite_start]This API is used to set user data in the machine. [cite: 19]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008",
        "USERID": "14",
        "USERNAME": "A",
        "FINGERDATA": "",
        "FACEDATA": "",
        "CARDDATA": "12345",
        "PASSWORDDATA": "123",
        "ADMIN": "0"
      }
    ]
    ```
    [cite_start][cite: 19]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Add User In Device Success.",
      "data": null
    }
    ```
    [cite_start][cite: 19]

---

### **http://103.240.90.194:7766/api/v1/ADDUSERWITHVALIDITY**

[cite_start]This API is used to add a user with a validity period. [cite: 19]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008",
        "USERID": "876",
        "USERNAME": "A",
        "FINGERDATA": "",
        "FACEDATA": "",
        "CARDDATA": "12345",
        "PASSWORDDATA": "123",
        "ADMIN": "0",
        "FROMDATE": "2025-01-03 14 20:50",
        "TODATE": "2025-03-20 22 10:00"
      }
    ]
    ```
    [cite_start][cite: 19]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Add User In Device Success.",
      "data": null
    }
    ```
    [cite_start][cite: 19]

---

### **http://103.240.90.194:7766/api/v1/DELETEUSER**

[cite_start]This API is used to delete user data in the machine. [cite: 19]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008",
        "USERID": "14"
      }
    ]
    ```
    [cite_start][cite: 19]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Delete User in Device Success.",
      "data": null
    }
    ```
    [cite_start][cite: 25]

---

### **http://103.240.90.194:7766/api/v1/ENABLEUSER**

[cite_start]This API is used to enable or disable user data in the machine. [cite: 25]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008",
        "USERID": "5",
        "STATUS": "DISABLE"
      },
      {
        "DEVICESLNO": "0123D1c170008",
        "USERID": "14",
        "STATUS": "ENABLE"
      }
    ]
    ```
    [cite_start][cite: 25]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "User Enable/Disable Successfully Success",
      "data": null
    }
    ```
    [cite_start][cite: 25]

---

### **http://103.240.90.194:7766/api/v1/ADDADMIN**

[cite_start]This API is used to add an admin in the machine. [cite: 25]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008",
        "USERID": "5"
      },
      [{
        "DEVICESLNO": "0123D1c170008",
        "USERID": "6"
      }]
    ]
    ```
    [cite_start][cite: 25]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "User Enable/Disable Successfully Success",
      "data": null
    }
    ```
    [cite_start][cite: 25]

---

### **http://103.240.90.194:7766/api/v1/DELETEADMIN**

[cite_start]This API is used to delete an admin in the machine. [cite: 25]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008",
        "USERID": "5"
      },
      [{
        "DEVICESLNO": "0123D1c170008",
        "USERID": "6"
      }]
    ]
    ```
    [cite_start][cite: 25]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "User Enable/Disable Successfully Success",
      "data": null
    }
    ```
    [cite_start][cite: 25]

---

### **http://103.240.90.194:7766/api/v1/CLEARADMIN**

[cite_start]This API is used to delete all admins in the machine. [cite: 25]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008"
      }
    ]
    ```
    [cite_start][cite: 25]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "User Enable/Disable Successfully Success",
      "data": null
    }
    ```
    [cite_start][cite: 25]

---

### **http://103.240.90.194:7766/api/v1/CLEARALLUSER**

[cite_start]This API is used to delete all users from the machine. [cite: 25]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008"
      }
    ]
    ```
    [cite_start][cite: 32]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "User Enable/Disable Successfully Success",
      "data": null
    }
    ```
    [cite_start][cite: 32]

---

### **http://103.240.90.194:7766/api/v1/SETVALIDITY**

[cite_start]This API is used to set the validity date for a user in the machine. [cite: 32]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008",
        "USERID": "5",
        "FROMDATE": "2025-01-01 10:10",
        "TODATE": "2025-05-01 10:10"
      }
    ]
    ```
    [cite_start][cite: 32]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "User Enable/Disable Successfully Success",
      "data": null
    }
    ```
    [cite_start][cite: 32]

---

### **http://103.240.90.194:7766/api/v1/REMOTEREGISTERFACE**

[cite_start]This API is used to remotely register a face in the device. [cite: 32]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008",
        "USERID": "88",
        "USERNAME": "Aman"
      }
    ]
    ```
    [cite_start][cite: 32]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "User Enable/Disable Successfully Success",
      "data": null
    }
    ```
    [cite_start][cite: 32]

---

### **http://103.240.90.194:7766/api/v1/REMOTEREGISTERFINGER**

[cite_start]This API is used to remotely register a finger in the device. [cite: 32]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008",
        "USERID": "88",
        "USERNAME": "Aman"
      }
    ]
    ```
    [cite_start][cite: 32]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "User Enable/Disable Successfully Success",
      "data": null
    }
    ```
    [cite_start][cite: 32]

---

### **http://103.240.90.194:7766/api/v1/OPENDOOR**

[cite_start]This API is used to remotely open a door in the device. [cite: 32]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008"
      }
    ]
    ```
    [cite_start][cite: 32]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Open Door Successfully",
      "data": null
    }
    ```
    [cite_start][cite: 32]

---

### **http://103.240.90.194:7766/api/v1/CLEARALLATTENDANCELOG**

[cite_start]This API is used to clear all attendance logs from the device. [cite: 32]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008"
      }
    ]
    ```
    [cite_start][cite: 32]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Clear All Attendance Log in Device Success",
      "data": null
    }
    ```
    [cite_start][cite: 39]

---

### **http://103.240.90.194:7766/api/v1/REBOOTDEVICE**

[cite_start]This API is used to reboot the machine. [cite: 39]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008"
      }
    ]
    ```
    [cite_start][cite: 39]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "Device Reboot Successfully",
      "data": null
    }
    ```
    [cite_start][cite: 39]

---

### **http://103.240.90.194:7766/api/v1/GETDEVICEONLINESTATUS**

[cite_start]This API is used to check the device online/offline status. [cite: 39]

* **Input Parameters:**

    ```json
    [
      {
        "DEVICESLNO": "0123D1c170008"
      }
    ]
    ```
    [cite_start][cite: 39]
* **Response:**

    ```json
    {
      "status": "200",
      "msg": "This Device is Online",
      "data": null
    }
    ```
    [cite_start][cite: 39]