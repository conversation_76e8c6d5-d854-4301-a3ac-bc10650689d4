"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Fingerprint,
  ScanFace,
  DoorOpen,
  RotateCcw,
  RefreshCw,
  AlertTriangle,
} from "lucide-react";
import { toast } from "react-toastify";

interface DeviceActionsViewProps {
  deviceId: string;
  isDeviceOnline: () => boolean;
}

export function DeviceActionsView({
  deviceId,
  isDeviceOnline,
}: DeviceActionsViewProps) {
  const [loading, setLoading] = useState(false);
  const [remoteRegisterData, setRemoteRegisterData] = useState({
    userId: "",
    userName: "",
  });

  const checkDeviceOnlineBeforeAction = (actionName: string): boolean => {
    if (!isDeviceOnline()) {
      toast.error(
        `Cannot ${actionName} - device is offline. Please ensure the device is connected and try again.`
      );
      return false;
    }
    return true;
  };

  const handleRemoteRegisterFace = async () => {
    if (!remoteRegisterData.userId || !remoteRegisterData.userName) {
      toast.error("Please enter both User ID and User Name");
      return;
    }

    if (!checkDeviceOnlineBeforeAction("register face remotely")) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/admin/devices/${deviceId}/actions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "remoteRegisterFace",
          userId: remoteRegisterData.userId,
          userName: remoteRegisterData.userName,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(result.message);
        // Clear form after successful registration
        setRemoteRegisterData({ userId: "", userName: "" });
      } else {
        const error = await response.json();
        toast.error(
          error.error || "Failed to initiate remote face registration"
        );
      }
    } catch (error) {
      console.error("Error initiating remote face registration:", error);
      toast.error("Failed to initiate remote face registration");
    } finally {
      setLoading(false);
    }
  };

  const handleRemoteRegisterFinger = async () => {
    if (!remoteRegisterData.userId || !remoteRegisterData.userName) {
      toast.error("Please enter both User ID and User Name");
      return;
    }

    if (!checkDeviceOnlineBeforeAction("register fingerprint remotely")) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/admin/devices/${deviceId}/actions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "remoteRegisterFinger",
          userId: remoteRegisterData.userId,
          userName: remoteRegisterData.userName,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(result.message);
        // Clear form after successful registration
        setRemoteRegisterData({ userId: "", userName: "" });
      } else {
        const error = await response.json();
        toast.error(
          error.error || "Failed to initiate remote fingerprint registration"
        );
      }
    } catch (error) {
      console.error("Error initiating remote fingerprint registration:", error);
      toast.error("Failed to initiate remote fingerprint registration");
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDoor = async () => {
    if (!checkDeviceOnlineBeforeAction("open door")) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/admin/devices/${deviceId}/actions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "openDoor",
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(result.message);
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to open door");
      }
    } catch (error) {
      console.error("Error opening door:", error);
      toast.error("Failed to open door");
    } finally {
      setLoading(false);
    }
  };

  const handleRebootDevice = async () => {
    if (!checkDeviceOnlineBeforeAction("reboot device")) {
      return;
    }

    // Show confirmation dialog
    if (
      !confirm(
        "Are you sure you want to reboot this device? The device will restart and may be temporarily unavailable."
      )
    ) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/admin/devices/${deviceId}/actions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "rebootDevice",
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(result.message);
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to reboot device");
      }
    } catch (error) {
      console.error("Error rebooting device:", error);
      toast.error("Failed to reboot device");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Device Status Warning */}
      {!isDeviceOnline() && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="pt-0">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-orange-800">
                  Device is currently offline
                </p>
                <p className="text-sm text-orange-600">
                  Device actions are only available when the device is online.
                  Please ensure the device is connected.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Remote Registration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ScanFace className="h-5 w-5" />
              Remote Registration
            </CardTitle>
            <CardDescription>
              Remotely register biometric data for users on this device
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="remoteUserId">User ID</Label>
                <Input
                  id="remoteUserId"
                  value={remoteRegisterData.userId}
                  onChange={(e) =>
                    setRemoteRegisterData((prev) => ({
                      ...prev,
                      userId: e.target.value,
                    }))
                  }
                  placeholder="Enter user ID"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="remoteUserName">User Name</Label>
                <Input
                  id="remoteUserName"
                  value={remoteRegisterData.userName}
                  onChange={(e) =>
                    setRemoteRegisterData((prev) => ({
                      ...prev,
                      userName: e.target.value,
                    }))
                  }
                  placeholder="Enter user name"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Button
                onClick={handleRemoteRegisterFace}
                disabled={loading || !isDeviceOnline()}
                variant="outline"
                className="w-full"
              >
                {loading ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <ScanFace className="h-4 w-4 mr-2" />
                )}
                Register Face
              </Button>
              <Button
                onClick={handleRemoteRegisterFinger}
                disabled={loading || !isDeviceOnline()}
                variant="outline"
                className="w-full"
              >
                {loading ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Fingerprint className="h-4 w-4 mr-2" />
                )}
                Register Finger
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Device Control Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DoorOpen className="h-5 w-5" />
              Device Control
            </CardTitle>
            <CardDescription>
              Control device hardware and system operations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={handleOpenDoor}
              disabled={loading || !isDeviceOnline()}
              variant="outline"
              className="w-full justify-start"
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <DoorOpen className="h-4 w-4 mr-2" />
              )}
              Open Door
            </Button>

            <Button
              onClick={handleRebootDevice}
              disabled={loading || !isDeviceOnline()}
              variant="destructive"
              className="w-full justify-start"
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RotateCcw className="h-4 w-4 mr-2" />
              )}
              Reboot Device
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
