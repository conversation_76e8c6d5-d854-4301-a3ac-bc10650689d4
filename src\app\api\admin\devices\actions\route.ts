import { NextRequest, NextResponse } from "next/server";
import { externalDeviceAPI } from "@/lib/api/external-device-api";
import { verifyAdminAuth } from "@/lib/auth";

// POST /api/admin/devices/actions - Perform device actions
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { action, deviceSlno, devices } = body;

    if (!action) {
      return NextResponse.json(
        { error: "Action is required" },
        { status: 400 }
      );
    }

    let response;
    let message = "";

    switch (action) {
      case "allocate":
        // Handle device allocation - this will trigger the allocation modal
        return NextResponse.json({
          success: true,
          message: "Allocation modal should be opened",
          action: "openAllocationModal",
          deviceSlno,
        });

      case "deallocate":
        // Handle device deallocation - this will trigger the deallocation modal
        return NextResponse.json({
          success: true,
          message: "Deallocation modal should be opened",
          action: "openDeallocationModal",
          deviceSlno,
        });

      case "edit":
        // Handle device edit - this will trigger the edit modal
        return NextResponse.json({
          success: true,
          message: "Edit modal should be opened",
          action: "openEditModal",
          deviceSlno,
        });

      case "delete":
        if (!deviceSlno) {
          return NextResponse.json(
            { error: "Device serial number is required for delete" },
            { status: 400 }
          );
        }
        response = await externalDeviceAPI.deleteDevice([
          { DEVICESLNO: deviceSlno },
        ]);
        message = "Device deleted successfully";
        break;

      case "reboot":
        if (!deviceSlno) {
          return NextResponse.json(
            { error: "Device serial number is required for reboot" },
            { status: 400 }
          );
        }
        response = await externalDeviceAPI.rebootDevice(deviceSlno);
        message = "Device reboot initiated successfully";
        break;

      case "bulkReboot":
        if (!devices || !Array.isArray(devices) || devices.length === 0) {
          return NextResponse.json(
            { error: "Devices array is required for bulk reboot" },
            { status: 400 }
          );
        }
        // Reboot multiple devices
        const rebootPromises = devices.map((device) =>
          externalDeviceAPI.rebootDevice(device.deviceSlno)
        );
        await Promise.all(rebootPromises);
        message = `Bulk reboot initiated for ${devices.length} devices`;
        response = { status: "success", msg: message, data: null };
        break;

      case "refreshStatus":
        if (!deviceSlno) {
          return NextResponse.json(
            { error: "Device serial number is required for status refresh" },
            { status: 400 }
          );
        }
        response = await externalDeviceAPI.getDeviceOnlineStatus(deviceSlno);
        message = "Device status refreshed successfully";
        break;

      case "bulkRefreshStatus":
        if (!devices || !Array.isArray(devices) || devices.length === 0) {
          return NextResponse.json(
            { error: "Devices array is required for bulk status refresh" },
            { status: 400 }
          );
        }
        // Refresh status for multiple devices
        const statusPromises = devices.map((device) =>
          externalDeviceAPI.getDeviceOnlineStatus(device.deviceSlno)
        );
        await Promise.all(statusPromises);
        message = `Status refreshed for ${devices.length} devices`;
        response = { status: "success", msg: message, data: null };
        break;

      case "openDoor":
        if (!deviceSlno) {
          return NextResponse.json(
            { error: "Device serial number is required for door control" },
            { status: 400 }
          );
        }
        response = await externalDeviceAPI.openDoor(deviceSlno);
        message = "Door opened successfully";
        break;

      case "clearAllUsers":
        if (!deviceSlno) {
          return NextResponse.json(
            { error: "Device serial number is required for clearing users" },
            { status: 400 }
          );
        }
        response = await externalDeviceAPI.clearAllUser(deviceSlno);
        message = "All users cleared from device successfully";
        break;

      case "clearAllAttendance":
        if (!deviceSlno) {
          return NextResponse.json(
            {
              error: "Device serial number is required for clearing attendance",
            },
            { status: 400 }
          );
        }
        response = await externalDeviceAPI.clearAllAttendanceLog(deviceSlno);
        message = "All attendance logs cleared from device successfully";
        break;

      case "syncUsers":
        const { sourceDeviceSlno, destinationDeviceSlno } = body;
        if (!sourceDeviceSlno || !destinationDeviceSlno) {
          return NextResponse.json(
            {
              error:
                "Source and destination device serial numbers are required for sync",
            },
            { status: 400 }
          );
        }
        response = await externalDeviceAPI.syncDevice(
          sourceDeviceSlno,
          destinationDeviceSlno
        );
        message = "Device sync initiated successfully";
        break;

      case "getUserData":
        if (!deviceSlno) {
          return NextResponse.json(
            { error: "Device serial number is required for getting user data" },
            { status: 400 }
          );
        }
        response = await externalDeviceAPI.getCmdUserData([
          { DEVICESLNO: deviceSlno },
        ]);
        message = "User data fetch command sent successfully";
        break;

      case "getAttendanceData":
        const { fromDate, toDate } = body;
        if (!deviceSlno || !fromDate || !toDate) {
          return NextResponse.json(
            {
              error:
                "Device serial number, fromDate, and toDate are required for attendance data",
            },
            { status: 400 }
          );
        }
        response = await externalDeviceAPI.sendCmdAttendanceLog([
          {
            DEVICESLNO: deviceSlno,
            FROMDATE: fromDate,
            TODATE: toDate,
          },
        ]);
        message = "Attendance data fetch command sent successfully";
        break;

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

    if (response && response.status !== "200") {
      return NextResponse.json(
        { error: response.msg || `Failed to perform ${action}` },
        { status: 400 }
      );
    }

    return NextResponse.json({
      message,
      data: response?.data || null,
    });
  } catch (error) {
    console.error("Error performing device action:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
