#!/usr/bin/env node

/**
 * Production Backup Script
 * Creates backups of database and uploaded files
 */

const fs = require("fs");
const path = require("path");
const { exec } = require("child_process");
const { promisify } = require("util");

const execAsync = promisify(exec);

// Configuration
const BACKUP_DIR = path.join(process.cwd(), "backups");
const UPLOADS_DIR = path.join(process.cwd(), "public", "uploads");
const MAX_BACKUPS = 7; // Keep last 7 backups

// Ensure backup directory exists
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// Get current timestamp
const getTimestamp = () => {
  const now = new Date();
  return (
    now.toISOString().replace(/[:.]/g, "-").split("T")[0] +
    "_" +
    now.toTimeString().split(" ")[0].replace(/:/g, "-")
  );
};

// Database backup function
async function backupDatabase() {
  console.log("📊 Starting database backup...");

  const timestamp = getTimestamp();
  const backupFile = path.join(BACKUP_DIR, `database_${timestamp}.sql`);

  // Parse DATABASE_URL
  const dbUrl = process.env.DATABASE_URL;
  if (!dbUrl) {
    throw new Error("DATABASE_URL not found in environment variables");
  }

  // Extract database connection details
  const url = new URL(dbUrl);
  const dbName = url.pathname.slice(1);
  const username = url.username;
  const password = url.password;
  const host = url.hostname;
  const port = url.port || 3306;

  // Create mysqldump command
  const dumpCommand = `mysqldump -h ${host} -P ${port} -u ${username} -p${password} ${dbName} > "${backupFile}"`;

  try {
    await execAsync(dumpCommand);
    console.log(`✅ Database backup created: ${backupFile}`);
    return backupFile;
  } catch (error) {
    console.error("❌ Database backup failed:", error.message);
    throw error;
  }
}

// Files backup function
async function backupFiles() {
  console.log("📁 Starting files backup...");

  if (!fs.existsSync(UPLOADS_DIR)) {
    console.log("⚠️  Uploads directory not found, skipping files backup");
    return null;
  }

  const timestamp = getTimestamp();
  const backupFile = path.join(BACKUP_DIR, `uploads_${timestamp}.tar.gz`);

  // Create tar command
  const tarCommand = `tar -czf "${backupFile}" -C "${path.dirname(
    UPLOADS_DIR
  )}" "${path.basename(UPLOADS_DIR)}"`;

  try {
    await execAsync(tarCommand);
    console.log(`✅ Files backup created: ${backupFile}`);
    return backupFile;
  } catch (error) {
    console.error("❌ Files backup failed:", error.message);
    throw error;
  }
}

// Cleanup old backups
function cleanupOldBackups() {
  console.log("🧹 Cleaning up old backups...");

  const files = fs.readdirSync(BACKUP_DIR);
  const backupFiles = files
    .filter(
      (file) => file.startsWith("database_") || file.startsWith("uploads_")
    )
    .sort()
    .reverse();

  if (backupFiles.length > MAX_BACKUPS) {
    const filesToDelete = backupFiles.slice(MAX_BACKUPS);
    filesToDelete.forEach((file) => {
      const filePath = path.join(BACKUP_DIR, file);
      fs.unlinkSync(filePath);
      console.log(`🗑️  Deleted old backup: ${file}`);
    });
  }

  console.log(
    `📦 Keeping ${Math.min(
      backupFiles.length,
      MAX_BACKUPS
    )} most recent backups`
  );
}

// Main backup function
async function createBackup() {
  console.log("🚀 Smart Attendance Portal - Production Backup");
  console.log("=".repeat(50));
  console.log(`📅 Backup started at: ${new Date().toISOString()}`);

  try {
    // Load environment variables
    require("dotenv").config({ path: path.join(process.cwd(), ".env.local") });

    const results = {
      database: null,
      files: null,
      timestamp: getTimestamp(),
    };

    // Backup database
    try {
      results.database = await backupDatabase();
    } catch (error) {
      console.error("Database backup failed, continuing with files backup...");
    }

    // Backup files
    try {
      results.files = await backupFiles();
    } catch (error) {
      console.error("Files backup failed, continuing...");
    }

    // Cleanup old backups
    cleanupOldBackups();

    // Summary
    console.log("\n📋 Backup Summary:");
    console.log(`Database: ${results.database ? "✅ Success" : "❌ Failed"}`);
    console.log(
      `Files: ${
        results.files
          ? "✅ Success"
          : results.files === null
          ? "⚠️  Skipped"
          : "❌ Failed"
      }`
    );
    console.log(`Backup directory: ${BACKUP_DIR}`);

    if (results.database || results.files) {
      console.log("\n✅ Backup completed successfully!");
    } else {
      console.log("\n❌ Backup failed - no backups created");
      process.exit(1);
    }
  } catch (error) {
    console.error("\n❌ Backup process failed:", error.message);
    process.exit(1);
  }
}

// Run backup if called directly
if (require.main === module) {
  createBackup();
}

module.exports = { createBackup, backupDatabase, backupFiles };
