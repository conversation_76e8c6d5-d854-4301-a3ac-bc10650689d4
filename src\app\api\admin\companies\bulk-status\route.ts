import { NextRequest, NextResponse } from "next/server";
import { verifyJWT } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { bulkStatusUpdateSchema } from "@/lib/validations/company";

export async function PATCH(req: NextRequest) {
  try {
    // Authentication check
    const token = req.cookies.get("auth-token")?.value;
    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const user = verifyJWT(token);
    if (!user || user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }
    const body = await req.json();
    const validatedData = bulkStatusUpdateSchema.parse(body);

    // Update companies status
    const result = await prisma.company.updateMany({
      where: {
        id: {
          in: validatedData.companyIds,
        },
      },
      data: {
        status: validatedData.status,
      },
    });

    return NextResponse.json({
      success: true,
      message: `${result.count} companies updated successfully`,
      data: { updatedCount: result.count },
    });
  } catch (error) {
    console.error("Bulk status update error:", error);

    if (error instanceof Error && error.name === "ZodError") {
      return NextResponse.json(
        { error: "Invalid input data" },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
