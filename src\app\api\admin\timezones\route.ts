import { NextRequest, NextResponse } from 'next/server';
import { externalDeviceAPI } from '@/lib/api/external-device-api';
import { prisma } from '@/lib/prisma';
import { verifyAdminAuth } from '@/lib/auth';

// GET /api/admin/timezones - Get all timezones from database
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get timezones from database first
    let timezones = await prisma.timeZone.findMany({
      orderBy: { timezoneId: 'asc' }
    });

    // If no timezones in database, fetch from external API and store
    if (timezones.length === 0) {
      console.log('No timezones in database, fetching from external API...');

      const response = await externalDeviceAPI.selectTimeZone();

      if (response.status !== '200') {
        return NextResponse.json(
          { error: 'Failed to fetch timezones from external API' },
          { status: 500 }
        );
      }

      const externalTimezones = response.data || [];

      // Store timezones in database
      const upsertPromises = externalTimezones.map(timezone =>
        prisma.timeZone.upsert({
          where: { timezoneId: timezone.TIMEZONE_ID },
          update: {
            timezoneName: timezone.TIMEZONE_NAME,
            timezoneTime: timezone['TIMEZONE TIME'] || '',
          },
          create: {
            timezoneId: timezone.TIMEZONE_ID,
            timezoneName: timezone.TIMEZONE_NAME,
            timezoneTime: timezone['TIMEZONE TIME'] || '',
          }
        })
      );

      timezones = await Promise.all(upsertPromises);
      console.log(`Stored ${timezones.length} timezones in database`);
    }

    // Transform the data to a more usable format
    const formattedTimezones = timezones.map(tz => ({
      id: tz.timezoneId,
      name: tz.timezoneName,
      time: tz.timezoneTime,
    }));

    return NextResponse.json({
      success: true,
      data: formattedTimezones,
      total: formattedTimezones.length,
    });

  } catch (error) {
    console.error('Error fetching timezones:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/timezones - Force sync timezones from external API
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('Force syncing timezones from external API...');

    // Fetch timezones from external API
    const response = await externalDeviceAPI.selectTimeZone();

    if (response.status !== '200') {
      return NextResponse.json({
        error: `Failed to fetch timezones from external API: ${response.msg}`
      }, { status: 400 });
    }

    const externalTimezones = response.data || [];
    console.log(`Fetched ${externalTimezones.length} timezones from external API`);

    // Store timezones in database (upsert to handle duplicates)
    const upsertPromises = externalTimezones.map(timezone =>
      prisma.timeZone.upsert({
        where: { timezoneId: timezone.TIMEZONE_ID },
        update: {
          timezoneName: timezone.TIMEZONE_NAME,
          timezoneTime: timezone['TIMEZONE TIME'] || '',
        },
        create: {
          timezoneId: timezone.TIMEZONE_ID,
          timezoneName: timezone.TIMEZONE_NAME,
          timezoneTime: timezone['TIMEZONE TIME'] || '',
        }
      })
    );

    const results = await Promise.all(upsertPromises);
    console.log(`Successfully stored ${results.length} timezones in database`);

    return NextResponse.json({
      success: true,
      message: `Successfully synced ${results.length} timezones`,
      data: results.map(tz => ({
        id: tz.timezoneId,
        name: tz.timezoneName,
        time: tz.timezoneTime,
      }))
    });
  } catch (error) {
    console.error('Error syncing timezones:', error);
    return NextResponse.json({
      error: 'Failed to sync timezones'
    }, { status: 500 });
  }
}
