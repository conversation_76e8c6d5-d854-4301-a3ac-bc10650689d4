import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '@/lib/auth';
import { externalDeviceAPI } from '@/lib/api/external-device-api';

// POST /api/admin/devices/status - Refresh device online status
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { deviceSerialNumbers } = body;

    if (!deviceSerialNumbers || !Array.isArray(deviceSerialNumbers)) {
      return NextResponse.json(
        { error: 'deviceSerialNumbers array is required' },
        { status: 400 }
      );
    }

    // Check status for each device individually
    const statusPromises = deviceSerialNumbers.map(async (serialNo: string) => {
      try {
        const response = await externalDeviceAPI.getDeviceOnlineStatus(serialNo);

        // Determine status based on response
        let status = 'UNKNOWN';
        if (response.status === '200') {
          status = 'ONLINE';
        } else if (response.status === '300') {
          status = 'OFFLINE';
        }

        return {
          serialNumber: serialNo,
          status,
          message: response.msg,
          timestamp: Date.now()
        };
      } catch (error) {
        return {
          serialNumber: serialNo,
          status: 'UNKNOWN',
          message: 'Error checking status'
        };
      }
    });

    // Wait for all status checks to complete
    const statusResults = await Promise.all(statusPromises);

    // Transform the response to a more usable format
    const statusMap: Record<string, string> = {};
    statusResults.forEach(result => {
      statusMap[result.serialNumber] = result.status;
    });



    return NextResponse.json({
      success: true,
      message: 'Device status refreshed successfully',
      statusMap
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
