import { NextRequest, NextResponse } from 'next/server'
import { withAdminAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { prisma } from '@/lib/prisma'
import { externalDeviceAPI } from '@/lib/api/external-device-api'

interface SystemStatusItem {
  id: string
  service: string
  status: 'operational' | 'degraded' | 'outage'
  message: string
  lastChecked: string
  responseTime?: number
}

async function getSystemStatus(req: AuthenticatedRequest) {
  try {
    const statusItems: SystemStatusItem[] = []

    // Check database connectivity
    try {
      const startTime = Date.now()
      await prisma.$queryRaw`SELECT 1`
      const responseTime = Date.now() - startTime

      statusItems.push({
        id: 'database',
        service: 'Database',
        status: responseTime < 100 ? 'operational' : 'degraded',
        message: responseTime < 100 ? 'All systems operational' : 'Slower than usual response times',
        lastChecked: new Date().toISOString(),
        responseTime
      })
    } catch (error) {
      statusItems.push({
        id: 'database',
        service: 'Database',
        status: 'outage',
        message: 'Database connection failed',
        lastChecked: new Date().toISOString()
      })
    }

    // Check external device API connectivity
    try {
      const startTime = Date.now()
      const response = await externalDeviceAPI.selectTimeZone()
      const responseTime = Date.now() - startTime

      if (response.status === '200') {
        statusItems.push({
          id: 'external-api',
          service: 'Device API',
          status: responseTime < 2000 ? 'operational' : 'degraded',
          message: responseTime < 2000 ? 'API responding normally' : 'API responding slowly',
          lastChecked: new Date().toISOString(),
          responseTime
        })
      } else {
        statusItems.push({
          id: 'external-api',
          service: 'Device API',
          status: 'degraded',
          message: `API returned status: ${response.status}`,
          lastChecked: new Date().toISOString(),
          responseTime
        })
      }
    } catch (error) {
      statusItems.push({
        id: 'external-api',
        service: 'Device API',
        status: 'outage',
        message: 'External API unreachable',
        lastChecked: new Date().toISOString()
      })
    }

    // Check device connectivity (sample a few devices)
    try {
      const devices = await externalDeviceAPI.selectDeviceList()
      if (devices.status === '200' && devices.data && devices.data.length > 0) {
        // Sample up to 3 devices for status check
        const sampleDevices = devices.data.slice(0, 3)
        let onlineCount = 0
        let totalChecked = 0

        for (const device of sampleDevices) {
          try {
            const statusResponse = await externalDeviceAPI.getDeviceOnlineStatus(device['SERIAL NO'])
            totalChecked++
            if (statusResponse.status === '200') {
              onlineCount++
            }
          } catch (error) {
            totalChecked++
            // Device check failed, count as offline
          }
        }

        const onlinePercentage = totalChecked > 0 ? (onlineCount / totalChecked) * 100 : 0
        let status: 'operational' | 'degraded' | 'outage' = 'operational'
        let message = `${onlineCount}/${totalChecked} sample devices online`

        if (onlinePercentage < 50) {
          status = 'outage'
          message = `Low device connectivity: ${onlinePercentage.toFixed(0)}% online`
        } else if (onlinePercentage < 80) {
          status = 'degraded'
          message = `Some devices offline: ${onlinePercentage.toFixed(0)}% online`
        }

        statusItems.push({
          id: 'device-connectivity',
          service: 'Device Connectivity',
          status,
          message,
          lastChecked: new Date().toISOString()
        })
      } else {
        statusItems.push({
          id: 'device-connectivity',
          service: 'Device Connectivity',
          status: 'outage',
          message: 'No devices found or API error',
          lastChecked: new Date().toISOString()
        })
      }
    } catch (error) {
      statusItems.push({
        id: 'device-connectivity',
        service: 'Device Connectivity',
        status: 'outage',
        message: 'Unable to check device status',
        lastChecked: new Date().toISOString()
      })
    }

    // Overall system health
    const operationalCount = statusItems.filter(item => item.status === 'operational').length
    const totalServices = statusItems.length
    const healthPercentage = (operationalCount / totalServices) * 100

    let overallStatus: 'operational' | 'degraded' | 'outage' = 'operational'
    let overallMessage = 'All systems operational'

    if (healthPercentage < 50) {
      overallStatus = 'outage'
      overallMessage = 'Multiple system outages detected'
    } else if (healthPercentage < 100) {
      overallStatus = 'degraded'
      overallMessage = 'Some services experiencing issues'
    }

    return NextResponse.json({
      success: true,
      data: {
        overall: {
          status: overallStatus,
          message: overallMessage,
          healthPercentage: Math.round(healthPercentage),
          lastUpdated: new Date().toISOString()
        },
        services: statusItems
      }
    })

  } catch (error) {
    console.error('Get system status error:', error)

    return NextResponse.json(
      { error: 'Failed to fetch system status' },
      { status: 500 }
    )
  }
}

export const GET = withAdminAuth(getSystemStatus)
