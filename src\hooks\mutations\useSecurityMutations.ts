import { useMutation } from "@tanstack/react-query";
import axios from "axios";

// Types
interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface Setup2FAResponse {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

interface Verify2FARequest {
  secret: string;
  token: string;
}

interface Disable2FARequest {
  password: string;
}

// API functions
const changeAdminPassword = async (data: ChangePasswordRequest) => {
  const response = await axios.post("/api/admin/security/password", data);
  return response.data;
};

const changeCompanyPassword = async (data: ChangePasswordRequest) => {
  const response = await axios.post("/api/dashboard/security/password", data);
  return response.data;
};

const setup2FA = async () => {
  const response = await axios.post("/api/admin/security/2fa/setup");
  return response.data.data as Setup2FAResponse;
};

const verify2FA = async (data: Verify2FARequest) => {
  const response = await axios.post("/api/admin/security/2fa/verify", data);
  return response.data;
};

const disable2FA = async (data: Disable2FARequest) => {
  const response = await axios.post("/api/admin/security/2fa/disable", data);
  return response.data;
};

// Hooks
export function useChangeAdminPasswordMutation() {
  return useMutation({
    mutationFn: changeAdminPassword,
    onError: (error: unknown) => {
      console.error("Failed to change admin password:", error);
    },
  });
}

export function useChangeCompanyPasswordMutation() {
  return useMutation({
    mutationFn: changeCompanyPassword,
    onError: (error: unknown) => {
      console.error("Failed to change company password:", error);
    },
  });
}

export function useSetup2FAMutation() {
  return useMutation({
    mutationFn: setup2FA,
    onError: (error: unknown) => {
      console.error("Failed to setup 2FA:", error);
    },
  });
}

export function useVerify2FAMutation() {
  return useMutation({
    mutationFn: verify2FA,
    onError: (error: unknown) => {
      console.error("Failed to verify 2FA:", error);
    },
  });
}

export function useDisable2FAMutation() {
  return useMutation({
    mutationFn: disable2FA,
    onError: (error: unknown) => {
      console.error("Failed to disable 2FA:", error);
    },
  });
}
