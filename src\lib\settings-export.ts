// Settings export/import utilities

export interface SettingsExport {
  version: string;
  exportDate: string;
  exportedBy: string;
  settings: Record<string, unknown>;
  metadata: {
    appVersion: string;
    settingsType: "admin" | "company";
    companyId?: string;
  };
}

/**
 * Export settings to JSON file
 */
export function exportSettings(
  settings: Record<string, unknown>,
  type: "admin" | "company",
  userEmail: string,
  companyId?: string
): void {
  const exportData: SettingsExport = {
    version: "1.0",
    exportDate: new Date().toISOString(),
    exportedBy: userEmail,
    settings,
    metadata: {
      appVersion: process.env.NEXT_PUBLIC_APP_VERSION || "1.0.0",
      settingsType: type,
      companyId,
    },
  };

  const dataStr = JSON.stringify(exportData, null, 2);
  const dataBlob = new Blob([dataStr], { type: "application/json" });

  const url = URL.createObjectURL(dataBlob);
  const link = document.createElement("a");
  link.href = url;

  const timestamp = new Date().toISOString().split("T")[0];
  const filename =
    type === "admin"
      ? `admin-settings-${timestamp}.json`
      : `company-settings-${companyId}-${timestamp}.json`;

  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * Import settings from JSON file
 */
export function importSettings(file: File): Promise<SettingsExport> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        const content = event.target?.result as string;
        const importData: SettingsExport = JSON.parse(content);

        // Validate import data structure
        if (
          !importData.version ||
          !importData.settings ||
          !importData.metadata
        ) {
          throw new Error("Invalid settings file format");
        }

        // Check version compatibility
        if (importData.version !== "1.0") {
          throw new Error(
            `Unsupported settings version: ${importData.version}`
          );
        }

        resolve(importData);
      } catch (error) {
        reject(new Error(`Failed to parse settings file: ${error}`));
      }
    };

    reader.onerror = () => {
      reject(new Error("Failed to read settings file"));
    };

    reader.readAsText(file);
  });
}

/**
 * Validate imported settings against current schema
 */
export function validateImportedSettings(
  importData: SettingsExport,
  expectedType: "admin" | "company"
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check settings type
  if (importData.metadata.settingsType !== expectedType) {
    errors.push(
      `Settings type mismatch. Expected ${expectedType}, got ${importData.metadata.settingsType}`
    );
  }

  // Check required fields based on type
  if (expectedType === "admin") {
    const requiredFields = [
      "general",
      "security",
      "notifications",
      "devices",
      "backup",
    ];
    for (const field of requiredFields) {
      if (!importData.settings[field]) {
        errors.push(`Missing required admin settings section: ${field}`);
      }
    }
  } else {
    const requiredFields = [
      "general",
      "notifications",
      "attendance",
      "devices",
    ];
    for (const field of requiredFields) {
      if (!importData.settings[field]) {
        errors.push(`Missing required company settings section: ${field}`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Create settings backup
 */
export function createSettingsBackup(
  settings: Record<string, unknown>,
  type: "admin" | "company",
  userEmail: string,
  companyId?: string
): SettingsExport {
  return {
    version: "1.0",
    exportDate: new Date().toISOString(),
    exportedBy: userEmail,
    settings: JSON.parse(JSON.stringify(settings)), // Deep clone
    metadata: {
      appVersion: process.env.NEXT_PUBLIC_APP_VERSION || "1.0.0",
      settingsType: type,
      companyId,
    },
  };
}

/**
 * Compare two settings objects and return differences
 */
export function compareSettings(
  current: Record<string, unknown>,
  imported: Record<string, unknown>
): {
  added: string[];
  modified: string[];
  removed: string[];
} {
  const added: string[] = [];
  const modified: string[] = [];
  const removed: string[] = [];

  function compareObjects(
    currentObj: Record<string, unknown>,
    importedObj: Record<string, unknown>,
    path = ""
  ) {
    // Check for added/modified fields
    for (const key in importedObj) {
      const currentPath = path ? `${path}.${key}` : key;

      if (!(key in currentObj)) {
        added.push(currentPath);
      } else if (
        typeof importedObj[key] === "object" &&
        importedObj[key] !== null
      ) {
        if (typeof currentObj[key] === "object" && currentObj[key] !== null) {
          compareObjects(currentObj[key], importedObj[key], currentPath);
        } else {
          modified.push(currentPath);
        }
      } else if (currentObj[key] !== importedObj[key]) {
        modified.push(currentPath);
      }
    }

    // Check for removed fields
    for (const key in currentObj) {
      const currentPath = path ? `${path}.${key}` : key;

      if (!(key in importedObj)) {
        removed.push(currentPath);
      }
    }
  }

  compareObjects(current, imported);

  return { added, modified, removed };
}

/**
 * Generate settings summary for display
 */
export function generateSettingsSummary(settings: Record<string, unknown>): {
  sections: { name: string; count: number }[];
  totalSettings: number;
} {
  const sections: { name: string; count: number }[] = [];
  let totalSettings = 0;

  function countSettings(obj: Record<string, unknown>): number {
    let count = 0;
    for (const key in obj) {
      if (
        typeof obj[key] === "object" &&
        obj[key] !== null &&
        !Array.isArray(obj[key])
      ) {
        count += countSettings(obj[key]);
      } else {
        count++;
      }
    }
    return count;
  }

  for (const sectionName in settings) {
    const count = countSettings(settings[sectionName]);
    sections.push({
      name: sectionName.charAt(0).toUpperCase() + sectionName.slice(1),
      count,
    });
    totalSettings += count;
  }

  return { sections, totalSettings };
}
