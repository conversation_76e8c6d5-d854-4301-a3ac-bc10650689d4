import { z } from 'zod'

// Login validation schemas
export const loginSchema = z.object({
  email: z.string().email('Invalid email address').optional(),
  loginToken: z.string().min(1, 'Login token is required').optional(),
  password: z.string().min(1, 'Password is required'),
}).refine(
  (data) => data.email || data.loginToken,
  {
    message: 'Either email or login token is required',
    path: ['email'],
  }
)

export const adminLoginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  twoFactorToken: z.string().optional(),
})

export const companyLoginSchema = z.object({
  loginToken: z.string().min(1, 'Login token is required'),
})

// Password validation
export const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character')

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
  confirmPassword: z.string().min(1, 'Password confirmation is required'),
}).refine(
  (data) => data.newPassword === data.confirmPassword,
  {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  }
)

export type LoginInput = z.infer<typeof loginSchema>
export type AdminLoginInput = z.infer<typeof adminLoginSchema>
export type CompanyLoginInput = z.infer<typeof companyLoginSchema>
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>
