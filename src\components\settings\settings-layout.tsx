import React from "react";
import { Button } from "@/components/ui/button";
import { Save, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

interface SettingsTab {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  content: React.ReactNode;
}

interface SettingsLayoutProps {
  title: string;
  description: string;
  tabs: SettingsTab[];
  onSave?: () => void;
  isSaving?: boolean;
  hasChanges?: boolean;
  error?: string;
  className?: string;
  defaultTab?: string;
}

export function SettingsLayout({
  title,
  description,
  tabs,
  onSave,
  isSaving = false,
  hasChanges = false,
  error,
  className = "",
  defaultTab,
}: SettingsLayoutProps) {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
          <p className="text-gray-600">{description}</p>
        </div>

        {/* Save Button */}
        {onSave && (
          <Button
            onClick={onSave}
            disabled={isSaving || !hasChanges}
            className="min-w-[120px]"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? "Saving..." : "Save Changes"}
          </Button>
        )}
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Status Message */}
      {onSave && (
        <div className="text-sm text-gray-600">
          {hasChanges ? (
            <span className="text-amber-600">You have unsaved changes</span>
          ) : (
            "All changes saved"
          )}
        </div>
      )}

      {/* Tabbed Settings Content */}
      <Tabs defaultValue={defaultTab || tabs[0]?.id} className="space-y-6">
        <TabsList
          className={`grid w-full ${
            tabs.length <= 3
              ? "grid-cols-3"
              : tabs.length <= 4
              ? "grid-cols-4"
              : tabs.length <= 5
              ? "grid-cols-5"
              : "grid-cols-6"
          }`}
        >
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.id}
              value={tab.id}
              className="flex items-center"
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>

        {tabs.map((tab) => (
          <TabsContent key={tab.id} value={tab.id} className="space-y-6">
            {tab.content}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
