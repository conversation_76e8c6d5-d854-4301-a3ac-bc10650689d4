import { useQuery } from '@tanstack/react-query'
import { queryKeys } from '@/lib/queryKeys'
import axios from 'axios'

// Types for dashboard data
export interface AdminStats {
  totalCompanies: number
  activeCompanies: number
  expiredCompanies: number
  totalDevices: number
  allocatedDevices: number
  unallocatedDevices: number
}

export interface ActivityItem {
  id: string
  type: string
  title: string
  description: string
  timestamp: string
  status: 'success' | 'warning' | 'error' | 'info'
  deviceSerial?: string
  employeeId?: string
}

export interface SystemStatusItem {
  id: string
  service: string
  status: 'operational' | 'degraded' | 'outage'
  message: string
  lastChecked: string
  responseTime?: number
}

export interface SystemStatus {
  overall: {
    status: 'operational' | 'degraded' | 'outage'
    message: string
    healthPercentage: number
    lastUpdated: string
  }
  services: SystemStatusItem[]
}

export interface CompanyStats {
  totalEmployees: number
  presentToday: number
  totalDevices: number
  activeDevices: number
  attendanceRate: number
  lastSyncTime: string
}

// Fetch functions
const fetchAdminStats = async (): Promise<AdminStats> => {
  const response = await axios.get('/api/admin/dashboard/stats')
  return response.data.data
}

const fetchAdminActivity = async (): Promise<ActivityItem[]> => {
  const response = await axios.get('/api/admin/dashboard/activity')
  return response.data.data
}

const fetchAdminSystemStatus = async (): Promise<SystemStatus> => {
  const response = await axios.get('/api/admin/dashboard/system-status')
  return response.data.data
}

const fetchCompanyStats = async (): Promise<CompanyStats> => {
  const response = await axios.get('/api/dashboard/stats')
  return response.data.data
}

const fetchCompanyActivity = async (): Promise<ActivityItem[]> => {
  const response = await axios.get('/api/dashboard/activity')
  return response.data.data
}

// Hooks for admin dashboard
export function useAdminStatsQuery() {
  return useQuery({
    queryKey: queryKeys.dashboard.adminStats(),
    queryFn: fetchAdminStats,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 60 * 1000, // Refetch every minute
    refetchIntervalInBackground: false,
    retry: 2,
  })
}

export function useAdminActivityQuery() {
  return useQuery({
    queryKey: queryKeys.dashboard.adminActivity(),
    queryFn: fetchAdminActivity,
    staleTime: 15 * 1000, // 15 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
    refetchIntervalInBackground: false,
    retry: 2,
  })
}

export function useAdminSystemStatusQuery() {
  return useQuery({
    queryKey: queryKeys.dashboard.adminSystemStatus(),
    queryFn: fetchAdminSystemStatus,
    staleTime: 20 * 1000, // 20 seconds
    gcTime: 3 * 60 * 1000, // 3 minutes
    refetchInterval: 45 * 1000, // Refetch every 45 seconds
    refetchIntervalInBackground: false,
    retry: 2,
  })
}

// Hooks for company dashboard
export function useCompanyStatsQuery() {
  return useQuery({
    queryKey: queryKeys.dashboard.companyStats('current'), // Use 'current' as placeholder
    queryFn: fetchCompanyStats,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 60 * 1000, // Refetch every minute
    refetchIntervalInBackground: false,
    retry: 2,
  })
}

export function useCompanyActivityQuery() {
  return useQuery({
    queryKey: queryKeys.dashboard.companyActivity('current'), // Use 'current' as placeholder
    queryFn: fetchCompanyActivity,
    staleTime: 15 * 1000, // 15 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
    refetchIntervalInBackground: false,
    retry: 2,
  })
}
