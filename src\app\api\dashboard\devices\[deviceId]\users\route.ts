import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { withCompanyAuth, AuthenticatedRequest } from "@/lib/auth/middleware";
import { externalDeviceAPI } from "@/lib/api/external-device-api";
import { prisma } from "@/lib/prisma";

// Validation schemas
const getUserDataSchema = z.object({
  action: z.literal("getUserData"),
});

const clearAllUsersSchema = z.object({
  action: z.literal("clearAllUsers"),
});

const clearAllAdminsSchema = z.object({
  action: z.literal("clearAllAdmins"),
});

const deleteUserSchema = z.object({
  action: z.literal("deleteUser"),
  userId: z.string().min(1, "User ID is required"),
});

const addUserSchema = z.object({
  action: z.literal("addUser"),
  userId: z.string().min(1, "User ID is required"),
  userName: z.string().min(1, "User name is required"),
  faceData: z.string().optional(),
});

const toggleAdminSchema = z.object({
  action: z.enum(["makeAdmin", "removeAdmin"]),
  userId: z.string().min(1, "User ID is required"),
});

const requestSchema = z.union([
  getUserDataSchema,
  clearAllUsersSchema,
  clearAllAdminsSchema,
  deleteUserSchema,
  addUserSchema,
  toggleAdminSchema,
]);

// GET /api/dashboard/devices/[deviceId]/users - Get all users for a device (company)
async function getDeviceUsers(req: AuthenticatedRequest) {
  try {
    const user = req.user!;

    // Extract deviceId from URL path
    const url = new URL(req.url!);
    const pathParts = url.pathname.split("/");
    const deviceId = pathParts[pathParts.length - 2]; // Get deviceId from path
    const deviceSerial = deviceId;

    // Verify the device is allocated to this company
    const allocation = await prisma.allocation.findFirst({
      where: {
        deviceSerialNo: deviceSerial,
        companyId: user.companyId,
      },
    });

    if (!allocation) {
      return NextResponse.json(
        { error: "Device not found or not allocated to your company" },
        { status: 404 }
      );
    }

    console.log(
      `Company ${user.companyId} getting users for device: ${deviceSerial}`
    );

    // Step 1: Send command to device to prepare user data
    console.log("Sending GETCMDUSERDATA command...");
    const cmdResponse = await externalDeviceAPI.getCmdUserData([
      {
        DEVICESLNO: deviceSerial,
      },
    ]);

    if (cmdResponse.status !== "200") {
      return NextResponse.json(
        {
          error: `Failed to send user data command: ${cmdResponse.msg}`,
        },
        { status: 400 }
      );
    }

    console.log(
      "Command sent successfully, waiting for device to prepare data..."
    );

    // Step 2: Wait for device to prepare data (3 seconds as recommended)
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Step 3: Retrieve user data from device
    console.log("Retrieving user data...");
    const userResponse = await externalDeviceAPI.getUserData([
      {
        DEVICESLNO: deviceSerial,
      },
    ]);

    if (userResponse.status !== "200") {
      return NextResponse.json(
        {
          error: `Failed to retrieve user data: ${userResponse.msg}`,
        },
        { status: 400 }
      );
    }

    // Process and return user data
    const users = userResponse.data || [];
    console.log(`Retrieved ${users.length} users from device ${deviceSerial}`);

    return NextResponse.json({
      users,
      deviceSerial,
      totalUsers: users.length,
      message: "User data retrieved successfully",
    });
  } catch (error) {
    console.error("Error getting device users:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/dashboard/devices/[deviceId]/users - Manage device users (company)
async function manageDeviceUsers(req: AuthenticatedRequest) {
  try {
    const user = req.user!;

    // Extract deviceId from URL path
    const url = new URL(req.url!);
    const pathParts = url.pathname.split("/");
    const deviceId = pathParts[pathParts.length - 2]; // Get deviceId from path
    const deviceSerial = deviceId;

    // Verify the device is allocated to this company
    const allocation = await prisma.allocation.findFirst({
      where: {
        deviceSerialNo: deviceSerial,
        companyId: user.companyId,
      },
    });

    if (!allocation) {
      return NextResponse.json(
        { error: "Device not found or not allocated to your company" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const validatedData = requestSchema.parse(body);
    const { action } = validatedData;

    console.log(
      `Company ${user.companyId} performing action ${action} on device: ${deviceSerial}`
    );

    switch (action) {
      case "getUserData": {
        // Step 1: Send command to device to prepare user data
        console.log("Sending GETCMDUSERDATA command...");
        const cmdResponse = await externalDeviceAPI.getCmdUserData([
          {
            DEVICESLNO: deviceSerial,
          },
        ]);

        if (cmdResponse.status !== "200") {
          return NextResponse.json(
            {
              error: `Failed to send user data command: ${cmdResponse.msg}`,
            },
            { status: 400 }
          );
        }

        console.log(
          "Command sent successfully, waiting for device to prepare data..."
        );

        // Step 2: Wait for device to prepare data (3 seconds as recommended)
        await new Promise((resolve) => setTimeout(resolve, 3000));

        // Step 3: Retrieve user data from device
        console.log("Retrieving user data...");
        const userResponse = await externalDeviceAPI.getUserData([
          {
            DEVICESLNO: deviceSerial,
          },
        ]);

        if (userResponse.status !== "200") {
          return NextResponse.json(
            {
              error: `Failed to retrieve user data: ${userResponse.msg}`,
            },
            { status: 400 }
          );
        }

        const users = userResponse.data || [];
        console.log(
          `Retrieved ${users.length} users from device ${deviceSerial}`
        );

        return NextResponse.json({
          message: "User data retrieved successfully",
          users,
          totalUsers: users.length,
        });
      }

      case "clearAllUsers": {
        const response = await externalDeviceAPI.clearAllUser(deviceSerial);

        if (response.status === "200") {
          return NextResponse.json({
            message: "All users cleared successfully",
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to clear all users",
            },
            { status: 400 }
          );
        }
      }

      case "clearAllAdmins": {
        const response = await externalDeviceAPI.clearAdmin(deviceSerial);

        if (response.status === "200") {
          return NextResponse.json({
            message: "All admins cleared successfully",
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to clear all admins",
            },
            { status: 400 }
          );
        }
      }

      case "deleteUser": {
        const { userId } = validatedData;
        const response = await externalDeviceAPI.deleteUser(
          deviceSerial,
          userId
        );

        if (response.status === "200") {
          return NextResponse.json({
            message: `User ${userId} deleted successfully`,
            userId,
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to delete user",
            },
            { status: 400 }
          );
        }
      }

      case "addUser": {
        const { userId, userName, faceData } = validatedData;

        // Prepare user data
        const userData: {
          DEVICESLNO: string;
          USERID: string;
          USERNAME: string;
          ADMIN: string;
          FACEDATA?: string;
        } = {
          DEVICESLNO: deviceSerial,
          USERID: userId,
          USERNAME: userName,
          ADMIN: "0", // Regular user
        };

        // Add face data if provided
        if (faceData) {
          userData.FACEDATA = faceData;
        }

        // Add user to device using external API
        const response = await externalDeviceAPI.addUser(userData);

        if (response.status === "200") {
          return NextResponse.json({
            message: "User added successfully",
            userId,
            userName,
            hasFaceData: !!faceData,
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to add user",
            },
            { status: 400 }
          );
        }
      }

      case "makeAdmin": {
        const { userId } = validatedData;

        console.log(
          `Making user ${userId} an admin on device: ${deviceSerial}`
        );

        // Add admin using external API
        const response = await externalDeviceAPI.addAdmin([
          {
            DEVICESLNO: deviceSerial,
            USERID: userId,
          },
        ]);

        if (response.status === "200") {
          console.log(`Successfully made user ${userId} an admin`);

          return NextResponse.json({
            message: `User ${userId} is now an admin`,
            userId,
            deviceSerial,
            isAdmin: true,
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to make user admin",
            },
            { status: 400 }
          );
        }
      }

      case "removeAdmin": {
        const { userId } = validatedData;

        console.log(
          `Removing admin privileges from user ${userId} on device: ${deviceSerial}`
        );

        // Remove admin using external API
        const response = await externalDeviceAPI.deleteAdmin([
          {
            DEVICESLNO: deviceSerial,
            USERID: userId,
          },
        ]);

        if (response.status === "200") {
          console.log(
            `Successfully removed admin privileges from user ${userId}`
          );

          return NextResponse.json({
            message: `Admin privileges removed from user ${userId}`,
            userId,
            deviceSerial,
            isAdmin: false,
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to remove admin privileges",
            },
            { status: 400 }
          );
        }
      }

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error managing device users:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export const GET = withCompanyAuth(getDeviceUsers);
export const POST = withCompanyAuth(manageDeviceUsers);
