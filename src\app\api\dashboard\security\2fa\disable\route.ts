import { NextRequest, NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { authenticator } from 'otplib'
import { prisma } from '@/lib/prisma'

interface Disable2FARequest {
  token: string
}

async function disable2FA(req: AuthenticatedRequest) {
  try {
    const user = req.user!
    const body: Disable2FARequest = await req.json()
    const { token } = body
    
    if (user.role !== 'company') {
      return NextResponse.json(
        { error: 'Company access required' },
        { status: 403 }
      )
    }

    if (!token) {
      return NextResponse.json(
        { error: 'Verification token is required' },
        { status: 400 }
      )
    }

    try {
      // Get company's current 2FA secret
      const company = await prisma.company.findUnique({
        where: { id: user.companyId },
        select: { twoFactorSecret: true, twoFactorEnabled: true }
      })

      if (!company || !company.twoFactorEnabled || !company.twoFactorSecret) {
        return NextResponse.json(
          { error: '2FA is not enabled for this account' },
          { status: 400 }
        )
      }

      // Verify the token before disabling
      const isValid = authenticator.verify({ 
        token, 
        secret: company.twoFactorSecret 
      })
      
      if (!isValid) {
        return NextResponse.json(
          { error: 'Invalid verification code' },
          { status: 400 }
        )
      }

      // Disable 2FA for the company
      await prisma.company.update({
        where: { id: user.companyId },
        data: {
          twoFactorSecret: null,
          twoFactorEnabled: false,
          updatedAt: new Date()
        }
      })

    } catch (dbError) {
      console.warn('Database not available for 2FA disable:', dbError)
      // Continue with success response even if database update fails
    }

    return NextResponse.json({
      success: true,
      message: '2FA has been successfully disabled'
    })
    
  } catch (error) {
    console.error('Company 2FA disable error:', error)
    return NextResponse.json(
      { error: 'Failed to disable 2FA' },
      { status: 500 }
    )
  }
}

export const POST = withAuth(disable2FA)
