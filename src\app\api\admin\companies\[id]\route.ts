import { NextRequest, NextResponse } from "next/server";
import { verifyJWT } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { updateCompanySchema } from "@/lib/validations/company";
import { hashPassword } from "@/lib/auth";

// GET /api/admin/companies/[id] - Get single company
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication check
    const token = req.cookies.get("auth-token")?.value;
    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const user = verifyJWT(token);
    if (!user || user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }

    const resolvedParams = await params;

    const company = await prisma.company.findUnique({
      where: { id: resolvedParams.id },
      include: {
        allocations: {
          select: {
            deviceSerialNo: true,
          },
        },
      },
    });

    if (!company) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    // Calculate days remaining
    const now = new Date();
    const daysRemaining = Math.ceil(
      (company.expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );

    return NextResponse.json({
      ...company,
      daysRemaining,
      allocatedDevices: company.allocations.length,
    });
  } catch (error) {
    console.error("Error fetching company:", error);
    return NextResponse.json(
      { error: "Failed to fetch company" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/companies/[id] - Update company
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication check
    const token = req.cookies.get("auth-token")?.value;
    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const user = verifyJWT(token);
    if (!user || user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const body = await req.json();
    const validatedData = updateCompanySchema.parse(body);

    // Check if company exists
    const existingCompany = await prisma.company.findUnique({
      where: { id: resolvedParams.id },
    });

    if (!existingCompany) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    // Check for unique constraints if email or organizationId is being updated
    if (validatedData.email && validatedData.email !== existingCompany.email) {
      const emailExists = await prisma.company.findUnique({
        where: { email: validatedData.email },
      });
      if (emailExists) {
        return NextResponse.json(
          { error: "Email already exists" },
          { status: 400 }
        );
      }
    }

    if (
      validatedData.organizationId &&
      validatedData.organizationId !== existingCompany.organizationId
    ) {
      const orgIdExists = await prisma.company.findUnique({
        where: { organizationId: validatedData.organizationId },
      });
      if (orgIdExists) {
        return NextResponse.json(
          { error: "Organization ID already exists" },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      ...validatedData,
      externalApiEndpoint: validatedData.externalApiEndpoint,
      updatedAt: new Date(),
    };

    // Handle expiresAt if provided in the request body
    if (body.expiresAt) {
      updateData.expiresAt = new Date(body.expiresAt);
    }

    // Calculate new expiration date if validity is provided
    if (validatedData.validity) {
      updateData.expiresAt = new Date(
        Date.now() + validatedData.validity * 24 * 60 * 60 * 1000
      );
    }

    // Update company
    const updatedCompany = await prisma.company.update({
      where: { id: resolvedParams.id },
      data: updateData,
      include: {
        allocations: {
          select: {
            deviceSerialNo: true,
          },
        },
      },
    });

    // Calculate days remaining
    const now = new Date();
    const daysRemaining = Math.ceil(
      (updatedCompany.expiresAt.getTime() - now.getTime()) /
        (1000 * 60 * 60 * 24)
    );

    return NextResponse.json({
      ...updatedCompany,
      daysRemaining,
      allocatedDevices: updatedCompany.allocations.length,
    });
  } catch (error) {
    console.error("Error updating company:", error);
    return NextResponse.json(
      { error: "Failed to update company" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/companies/[id] - Delete company
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication check
    const token = req.cookies.get("auth-token")?.value;
    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const user = verifyJWT(token);
    if (!user || user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    console.log("Deleting company with ID:", resolvedParams.id);

    // Check if company exists
    const existingCompany = await prisma.company.findUnique({
      where: { id: resolvedParams.id },
      include: {
        allocations: true,
      },
    });

    if (!existingCompany) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    // Delete company (this will cascade delete allocations due to onDelete: Cascade)
    await prisma.company.delete({
      where: { id: resolvedParams.id },
    });

    return NextResponse.json({
      message: "Company deleted successfully",
      deallocatedDevices: existingCompany.allocations.length,
    });
  } catch (error) {
    console.error("Error deleting company:", error);
    return NextResponse.json(
      { error: "Failed to delete company" },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/companies/[id] - Update company status
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication check
    const token = req.cookies.get("auth-token")?.value;
    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const user = verifyJWT(token);
    if (!user || user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const body = await req.json();
    const { status } = body;

    if (!status || !["ACTIVE", "DEACTIVATED"].includes(status)) {
      return NextResponse.json(
        { error: "Invalid status. Must be ACTIVE or DEACTIVATED" },
        { status: 400 }
      );
    }

    // Check if company exists
    const existingCompany = await prisma.company.findUnique({
      where: { id: resolvedParams.id },
    });

    if (!existingCompany) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    // Update company status
    const updatedCompany = await prisma.company.update({
      where: { id: resolvedParams.id },
      data: {
        status,
        updatedAt: new Date(),
      },
      include: {
        allocations: {
          select: {
            deviceSerialNo: true,
          },
        },
      },
    });

    // Calculate days remaining
    const now = new Date();
    const daysRemaining = Math.ceil(
      (updatedCompany.expiresAt.getTime() - now.getTime()) /
        (1000 * 60 * 60 * 24)
    );

    return NextResponse.json({
      ...updatedCompany,
      daysRemaining,
      allocatedDevices: updatedCompany.allocations.length,
    });
  } catch (error) {
    console.error("Error updating company status:", error);
    return NextResponse.json(
      { error: "Failed to update company status" },
      { status: 500 }
    );
  }
}
