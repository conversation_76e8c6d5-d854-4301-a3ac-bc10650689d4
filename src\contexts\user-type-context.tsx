"use client";

import React, { createContext, useContext, useState, useEffect } from "react";

// User types from the database enum
export type UserType = "API_USER" | "SCHOOL_MANAGEMENT" | "EMPLOYEE_MANAGEMENT";

// Dynamic labels based on user type
export interface UserTypeLabels {
  // Navigation labels
  usersLabel: string;
  userLabel: string;
  attendanceLabel: string;
  reportsLabel: string;

  // Dashboard labels
  totalUsersLabel: string;
  presentTodayLabel: string;
  attendanceRateLabel: string;

  // Action labels
  addUserLabel: string;
  manageUsersLabel: string;
  viewReportsLabel: string;

  // Feature descriptions
  userManagementDesc: string;
  attendanceDesc: string;
  reportsDesc: string;
}

// Context interface
interface UserTypeContextType {
  userType: UserType;
  labels: UserTypeLabels;
  setUserType: (type: UserType) => void;
  isFeatureEnabled: (feature: string) => boolean;
}

// Default labels for API_USER
const getLabelsForUserType = (userType: UserType): UserTypeLabels => {
  switch (userType) {
    case "API_USER":
      return {
        usersLabel: "Users",
        userLabel: "User",
        attendanceLabel: "Attendance",
        reportsLabel: "Reports",
        totalUsersLabel: "Total Users",
        presentTodayLabel: "Present Today",
        attendanceRateLabel: "Attendance Rate",
        addUserLabel: "Add User",
        manageUsersLabel: "Manage Users",
        viewReportsLabel: "View Reports",
        userManagementDesc: "Add, edit, and manage users in your system",
        attendanceDesc: "Track and monitor user attendance records",
        reportsDesc: "Generate and analyze attendance reports",
      };

    case "SCHOOL_MANAGEMENT":
      return {
        usersLabel: "Students",
        userLabel: "Student",
        attendanceLabel: "Attendance",
        reportsLabel: "Academic Reports",
        totalUsersLabel: "Total Students",
        presentTodayLabel: "Present Today",
        attendanceRateLabel: "Attendance Rate",
        addUserLabel: "Add Student",
        manageUsersLabel: "Manage Students",
        viewReportsLabel: "View Academic Reports",
        userManagementDesc: "Add, edit, and manage students in your school",
        attendanceDesc: "Track and monitor student attendance records",
        reportsDesc: "Generate academic and attendance reports",
      };

    case "EMPLOYEE_MANAGEMENT":
      return {
        usersLabel: "Employees",
        userLabel: "Employee",
        attendanceLabel: "Attendance",
        reportsLabel: "HR Reports",
        totalUsersLabel: "Total Employees",
        presentTodayLabel: "Present Today",
        attendanceRateLabel: "Attendance Rate",
        addUserLabel: "Add Employee",
        manageUsersLabel: "Manage Employees",
        viewReportsLabel: "View HR Reports",
        userManagementDesc:
          "Add, edit, and manage employees in your organization",
        attendanceDesc: "Track and monitor employee attendance records",
        reportsDesc: "Generate HR and attendance reports",
      };

    default:
      return getLabelsForUserType("API_USER");
  }
};

// Feature access control based on user type (cumulative access)
const getFeatureAccess = (userType: UserType) => {
  // Base features available to all user types
  const baseFeatures = {
    userManagement: true,
    attendanceTracking: true,
    basicReports: true,
    deviceControl: true,
  };

  // API_USER features (core functionality)
  const apiUserFeatures = {
    ...baseFeatures,
    advancedDeviceControl: true,
    biometricRegistration: true,
    deviceSync: true,
    bulkOperations: true,
    apiAccess: true,
  };

  switch (userType) {
    case "API_USER":
      return apiUserFeatures;

    case "SCHOOL_MANAGEMENT":
      return {
        ...apiUserFeatures, // All API_USER features PLUS school-specific features
        academicReports: true,
        classManagement: true,
        parentNotifications: true,
        academicAnalytics: true,
        studentManagement: true,
      };

    case "EMPLOYEE_MANAGEMENT":
      return {
        ...apiUserFeatures, // All API_USER features PLUS HR-specific features
        hrReports: true,
        shiftManagement: true,
        payrollIntegration: true,
        hrAnalytics: true,
        employeeManagement: true,
      };

    default:
      return baseFeatures;
  }
};

// Create context
const UserTypeContext = createContext<UserTypeContextType | undefined>(
  undefined
);

// Provider component
interface UserTypeProviderProps {
  children: React.ReactNode;
  initialUserType?: UserType;
}

export function UserTypeProvider({
  children,
  initialUserType = "API_USER",
}: UserTypeProviderProps) {
  const [userType, setUserType] = useState<UserType>(initialUserType);
  const [labels, setLabels] = useState<UserTypeLabels>(
    getLabelsForUserType(initialUserType)
  );

  // Update labels when user type changes
  useEffect(() => {
    setLabels(getLabelsForUserType(userType));
  }, [userType]);

  // Check if a feature is enabled for the current user type
  const isFeatureEnabled = (feature: string): boolean => {
    const featureAccess = getFeatureAccess(userType);
    return featureAccess[feature as keyof typeof featureAccess] || false;
  };

  const value: UserTypeContextType = {
    userType,
    labels,
    setUserType,
    isFeatureEnabled,
  };

  return (
    <UserTypeContext.Provider value={value}>
      {children}
    </UserTypeContext.Provider>
  );
}

// Hook to use the context
export function useUserType() {
  const context = useContext(UserTypeContext);
  if (context === undefined) {
    throw new Error("useUserType must be used within a UserTypeProvider");
  }
  return context;
}

// Utility function to get user type from company data
export function getUserTypeFromCompany(
  company: Record<string, unknown>
): UserType {
  // This would typically come from the company's user_type field in the database
  return company?.user_type || "API_USER";
}

// Export types for use in other components
export type { UserTypeContextType };
