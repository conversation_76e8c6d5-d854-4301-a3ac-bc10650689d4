import { externalDeviceAPI } from '@/lib/api/external-device-api';
import { prisma } from '@/lib/prisma';

export class TimezoneService {
  private static instance: TimezoneService;
  private initialized = false;

  private constructor() {}

  public static getInstance(): TimezoneService {
    if (!TimezoneService.instance) {
      TimezoneService.instance = new TimezoneService();
    }
    return TimezoneService.instance;
  }

  /**
   * Initialize timezones by fetching from external API and storing in database
   * This should be called at application startup
   */
  public async initializeTimezones(): Promise<void> {
    if (this.initialized) {
      console.log('Timezones already initialized');
      return;
    }

    try {
      console.log('Initializing timezones...');

      // Check if timezones already exist in database
      const existingTimezones = await prisma.timeZone.count();
      
      if (existingTimezones > 0) {
        console.log(`Found ${existingTimezones} timezones in database, skipping initialization`);
        this.initialized = true;
        return;
      }

      // Fetch timezones from external API
      console.log('Fetching timezones from external API...');
      const response = await externalDeviceAPI.selectTimeZone();

      if (response.status !== '200') {
        console.error('Failed to fetch timezones from external API:', response.msg);
        return;
      }

      const timezones = response.data || [];
      console.log(`Fetched ${timezones.length} timezones from external API`);

      if (timezones.length === 0) {
        console.warn('No timezones received from external API');
        return;
      }

      // Store timezones in database
      const upsertPromises = timezones.map(timezone => 
        prisma.timeZone.upsert({
          where: { timezoneId: timezone.TIMEZONE_ID },
          update: {
            timezoneName: timezone.TIMEZONE_NAME,
            timezoneTime: timezone['TIMEZONE TIME'] || '',
          },
          create: {
            timezoneId: timezone.TIMEZONE_ID,
            timezoneName: timezone.TIMEZONE_NAME,
            timezoneTime: timezone['TIMEZONE TIME'] || '',
          }
        })
      );

      const results = await Promise.all(upsertPromises);
      console.log(`Successfully stored ${results.length} timezones in database`);

      // Ensure default timezone (ID: 57) exists
      await this.ensureDefaultTimezone();

      this.initialized = true;
      console.log('Timezone initialization completed');

    } catch (error) {
      console.error('Error initializing timezones:', error);
      // Don't throw error to prevent app startup failure
    }
  }

  /**
   * Ensure the default timezone (ID: 57) exists in the database
   */
  private async ensureDefaultTimezone(): Promise<void> {
    try {
      const defaultTimezone = await prisma.timeZone.findUnique({
        where: { timezoneId: 57 }
      });

      if (!defaultTimezone) {
        console.warn('Default timezone (ID: 57) not found, creating fallback...');
        await prisma.timeZone.create({
          data: {
            timezoneId: 57,
            timezoneName: 'Asia/Kolkata (UTC+05:30)',
            timezoneTime: '+05:30',
          }
        });
        console.log('Created fallback default timezone');
      }
    } catch (error) {
      console.error('Error ensuring default timezone:', error);
    }
  }

  /**
   * Get all timezones from database
   */
  public async getTimezones() {
    try {
      return await prisma.timeZone.findMany({
        orderBy: { timezoneId: 'asc' }
      });
    } catch (error) {
      console.error('Error fetching timezones:', error);
      return [];
    }
  }

  /**
   * Get timezone by ID
   */
  public async getTimezoneById(timezoneId: number) {
    try {
      return await prisma.timeZone.findUnique({
        where: { timezoneId }
      });
    } catch (error) {
      console.error('Error fetching timezone by ID:', error);
      return null;
    }
  }

  /**
   * Get default timezone ID from admin settings
   */
  public async getDefaultTimezoneId(): Promise<number> {
    try {
      const settings = await prisma.adminSettings.findFirst();
      return settings?.defaultTimeZoneId || 57;
    } catch (error) {
      console.error('Error fetching default timezone ID:', error);
      return 57;
    }
  }

  /**
   * Update default timezone in admin settings
   */
  public async updateDefaultTimezone(timezoneId: number): Promise<void> {
    try {
      await prisma.adminSettings.upsert({
        where: { id: 'default' },
        update: { defaultTimeZoneId: timezoneId },
        create: {
          id: 'default',
          defaultTimeZoneId: timezoneId,
        }
      });
      console.log(`Updated default timezone to ID: ${timezoneId}`);
    } catch (error) {
      console.error('Error updating default timezone:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const timezoneService = TimezoneService.getInstance();
