import { NextRequest, NextResponse } from 'next/server'
import { withAdminAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { authenticator } from 'otplib'
import QRCode from 'qrcode'

interface Setup2FAResponse {
  secret: string
  qrCodeUrl: string
  backupCodes: string[]
}

async function setup2FA(req: AuthenticatedRequest) {
  try {
    const user = req.user!
    
    // Generate a secret for the user
    const secret = authenticator.generateSecret()
    
    // Create the service name and account name for the QR code
    const serviceName = 'Smart Attendance Portal'
    const accountName = user.email
    
    // Generate the otpauth URL
    const otpauthUrl = authenticator.keyuri(accountName, serviceName, secret)
    
    // Generate QR code as data URL
    const qrCodeDataUrl = await QRCode.toDataURL(otpauthUrl)
    
    // Generate backup codes (8 codes, 8 characters each)
    const backupCodes = Array.from({ length: 8 }, () => {
      return Math.random().toString(36).substring(2, 10).toUpperCase()
    })
    
    // In a real implementation, you would:
    // 1. Store the secret temporarily (not activated until verified)
    // 2. Store the backup codes (hashed)
    // 3. Associate them with the user account
    
    // For now, we'll return the setup data
    const response: Setup2FAResponse = {
      secret,
      qrCodeUrl: qrCodeDataUrl,
      backupCodes
    }
    
    return NextResponse.json({
      success: true,
      data: response
    })
    
  } catch (error) {
    console.error('2FA setup error:', error)
    return NextResponse.json(
      { error: 'Failed to setup 2FA' },
      { status: 500 }
    )
  }
}

export const POST = withAdminAuth(setup2FA)
