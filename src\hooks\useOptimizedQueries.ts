import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';

// Optimized query configurations for better performance
export const QUERY_KEYS = {
  COMPANY_PROFILE: 'company-profile',
  COMPANY_STATS: 'company-stats',
  COMPANY_DEVICES: 'company-devices',
  COMPANY_ACTIVITY: 'company-activity',
  DEVICE_STATUS: 'device-status',
} as const;

// Default query options for better performance
export const DEFAULT_QUERY_OPTIONS = {
  staleTime: 30 * 1000, // 30 seconds
  gcTime: 5 * 60 * 1000, // 5 minutes (formerly cacheTime)
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  retry: 2,
  retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
};

// Fast refresh options for critical data
export const FAST_REFRESH_OPTIONS = {
  ...DEFAULT_QUERY_OPTIONS,
  staleTime: 10 * 1000, // 10 seconds
  refetchInterval: 30 * 1000, // 30 seconds
};

// Slow refresh options for less critical data
export const SLOW_REFRESH_OPTIONS = {
  ...DEFAULT_QUERY_OPTIONS,
  staleTime: 2 * 60 * 1000, // 2 minutes
  refetchInterval: 5 * 60 * 1000, // 5 minutes
};

// Company profile query with optimized caching
export function useCompanyProfileQuery() {
  return useQuery({
    queryKey: [QUERY_KEYS.COMPANY_PROFILE],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/profile');
      if (!response.ok) {
        throw new Error('Failed to fetch profile');
      }
      const data = await response.json();
      return data.data;
    },
    ...SLOW_REFRESH_OPTIONS, // Profile data changes infrequently
  });
}

// Company stats query with fast refresh
export function useCompanyStatsQuery() {
  return useQuery({
    queryKey: [QUERY_KEYS.COMPANY_STATS],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/stats');
      if (!response.ok) {
        throw new Error('Failed to fetch stats');
      }
      const data = await response.json();
      return data.data;
    },
    ...FAST_REFRESH_OPTIONS, // Stats need frequent updates
  });
}

// Company devices query with medium refresh
export function useCompanyDevicesQuery(params?: {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: string;
}) {
  const queryParams = new URLSearchParams();
  if (params?.page) queryParams.set('page', params.page.toString());
  if (params?.pageSize) queryParams.set('pageSize', params.pageSize.toString());
  if (params?.search) queryParams.set('search', params.search);
  if (params?.status) queryParams.set('status', params.status);
  if (params?.sortBy) queryParams.set('sortBy', params.sortBy);
  if (params?.sortOrder) queryParams.set('sortOrder', params.sortOrder);

  return useQuery({
    queryKey: [QUERY_KEYS.COMPANY_DEVICES, params],
    queryFn: async () => {
      const url = `/api/dashboard/devices${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to fetch devices');
      }
      return response.json();
    },
    ...DEFAULT_QUERY_OPTIONS,
    staleTime: 20 * 1000, // 20 seconds for device data
  });
}

// Company activity query with fast refresh
export function useCompanyActivityQuery() {
  return useQuery({
    queryKey: [QUERY_KEYS.COMPANY_ACTIVITY],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/activity');
      if (!response.ok) {
        throw new Error('Failed to fetch activity');
      }
      const data = await response.json();
      return data.data;
    },
    ...FAST_REFRESH_OPTIONS, // Activity needs frequent updates
  });
}

// Device status query with very fast refresh
export function useDeviceStatusQuery(deviceId: string, enabled = true) {
  return useQuery({
    queryKey: [QUERY_KEYS.DEVICE_STATUS, deviceId],
    queryFn: async () => {
      const response = await fetch(`/api/admin/devices/${deviceId}/status`);
      if (!response.ok) {
        throw new Error('Failed to fetch device status');
      }
      return response.json();
    },
    enabled,
    staleTime: 5 * 1000, // 5 seconds
    refetchInterval: 15 * 1000, // 15 seconds
    retry: 1, // Quick retry for status checks
  });
}

// Optimized query client hook for manual cache management
export function useOptimizedQueryClient() {
  const queryClient = useQueryClient();

  const invalidateProfile = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COMPANY_PROFILE] });
  }, [queryClient]);

  const invalidateStats = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COMPANY_STATS] });
  }, [queryClient]);

  const invalidateDevices = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COMPANY_DEVICES] });
  }, [queryClient]);

  const invalidateActivity = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COMPANY_ACTIVITY] });
  }, [queryClient]);

  const invalidateAll = useCallback(() => {
    queryClient.invalidateQueries();
  }, [queryClient]);

  const prefetchProfile = useCallback(() => {
    queryClient.prefetchQuery({
      queryKey: [QUERY_KEYS.COMPANY_PROFILE],
      queryFn: async () => {
        const response = await fetch('/api/dashboard/profile');
        if (!response.ok) throw new Error('Failed to fetch profile');
        const data = await response.json();
        return data.data;
      },
      ...SLOW_REFRESH_OPTIONS,
    });
  }, [queryClient]);

  return {
    queryClient,
    invalidateProfile,
    invalidateStats,
    invalidateDevices,
    invalidateActivity,
    invalidateAll,
    prefetchProfile,
  };
}
