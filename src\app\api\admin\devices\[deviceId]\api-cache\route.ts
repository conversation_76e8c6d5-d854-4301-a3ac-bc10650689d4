import { NextRequest, NextResponse } from "next/server";
import { verifyAdminAuth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET /api/admin/devices/[deviceId]/api-cache - Get cached external API response for a device
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const deviceSerial = resolvedParams.deviceId;

    // Get cached API response from database
    const cachedData = await prisma.deviceApiCache.findUnique({
      where: { deviceSerialNumber: deviceSerial },
    });

    if (!cachedData) {
      return NextResponse.json(
        { error: "No cached data found for this device" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        deviceSerialNumber: cachedData.deviceSerialNumber,
        apiResponse: cachedData.apiResponse,
        lastUpdated: cachedData.lastApiUpdate,
        extractedData: {
          deviceName: cachedData.deviceName,
          modelNo: cachedData.modelNo,
          modelName: cachedData.modelName,
          location: cachedData.location,
          userCount: cachedData.userCount,
          logCount: cachedData.logCount,
          timeZone: cachedData.timeZone,
          endpointUrl: cachedData.endpointUrl,
          features: {
            face: cachedData.isface,
            finger: cachedData.isfinger,
            card: cachedData.iscard,
            password: cachedData.ispassword,
          },
          biometricCounts: {
            fingerprints: cachedData.fingerprintCount,
            faces: cachedData.faceCount,
            cards: cachedData.cardCount,
            passwords: cachedData.passwordCount,
          },
        },
      },
    });
  } catch (error) {
    console.error("Error fetching cached device data:", error);
    return NextResponse.json(
      { error: "Failed to fetch cached device data" },
      { status: 500 }
    );
  }
}
