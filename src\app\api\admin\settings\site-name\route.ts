import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/admin/settings/site-name - Get site name (public endpoint for layout)
export async function GET(request: NextRequest) {
  try {
    // Get admin settings from database
    const adminSettings = await prisma.adminSettings.findFirst();
    
    // Return site name or default
    const siteName = adminSettings?.siteName || 'SRITechnologies';
    
    return NextResponse.json({
      siteName
    });

  } catch (error) {
    console.error('Error fetching site name:', error);
    // Return default on error
    return NextResponse.json({
      siteName: 'SRITechnologies'
    });
  }
}
