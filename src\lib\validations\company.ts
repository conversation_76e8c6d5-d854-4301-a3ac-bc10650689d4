import { z } from "zod";
import { UserType } from "@prisma/client";

export const createCompanySchema = z.object({
  name: z
    .string()
    .min(1, "Company name is required")
    .max(100, "Company name is too long"),
  organizationId: z
    .string()
    .min(1, "Organization ID is required")
    .max(50, "Organization ID is too long"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  userType: z.nativeEnum(UserType, {
    message: "Invalid user type",
  }),
  validity: z
    .number()
    .min(1, "Validity must be at least 1 day")
    .max(3650, "Validity cannot exceed 10 years"),
  description: z.string().max(500, "Description is too long").optional(),
  externalApiEndpoint: z.string().url("Invalid URL").optional(),
});

export const updateCompanySchema = z.object({
  name: z
    .string()
    .min(1, "Company name is required")
    .max(100, "Company name is too long")
    .optional(),
  organizationId: z
    .string()
    .min(1, "Organization ID is required")
    .max(50, "Organization ID is too long")
    .optional(),
  email: z.string().email("Invalid email address").optional(),
  userType: z
    .nativeEnum(UserType, {
      message: "Invalid user type",
    })
    .optional(),
  status: z.enum(["ACTIVE", "DEACTIVATED"]).optional(),
  validity: z
    .number()
    .min(1, "Validity must be at least 1 day")
    .max(3650, "Validity cannot exceed 10 years")
    .optional(),
  description: z.string().max(500, "Description is too long").optional(),
  externalApiEndpoint: z.string().url("Invalid URL").optional(),
});

export const extendValiditySchema = z.object({
  companyIds: z
    .array(z.string().min(1, "Invalid company ID"))
    .min(1, "At least one company must be selected"),
  days: z
    .number()
    .min(1, "Extension must be at least 1 day")
    .max(3650, "Extension cannot exceed 10 years"),
});

export const bulkStatusUpdateSchema = z.object({
  companyIds: z
    .array(z.string().min(1, "Invalid company ID"))
    .min(1, "At least one company must be selected"),
  status: z.enum(["ACTIVE", "DEACTIVATED"], {
    message: "Invalid status",
  }),
});

export const companySearchSchema = z.object({
  query: z.string().optional(),
  status: z.enum(["ACTIVE", "DEACTIVATED"]).optional(),
  userType: z.nativeEnum(UserType).optional(),
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  sortBy: z
    .enum(["name", "email", "createdAt", "expiresAt"])
    .default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export type CreateCompanyInput = z.infer<typeof createCompanySchema>;
export type UpdateCompanyInput = z.infer<typeof updateCompanySchema>;
export type ExtendValidityInput = z.infer<typeof extendValiditySchema>;
export type BulkStatusUpdateInput = z.infer<typeof bulkStatusUpdateSchema>;
export type CompanySearchInput = z.infer<typeof companySearchSchema>;
