const bcrypt = require('bcryptjs')

async function createAdminHash() {
  const password = 'admin123'
  const saltRounds = 12
  const hash = await bcrypt.hash(password, saltRounds)
  
  console.log('Admin credentials:')
  console.log('Email: <EMAIL>')
  console.log('Password:', password)
  console.log('Hash:', hash)
  
  // Test verification
  const isValid = await bcrypt.compare(password, hash)
  console.log('Hash verification:', isValid)
}

createAdminHash().catch(console.error)
