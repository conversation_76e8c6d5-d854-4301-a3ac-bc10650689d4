"use client";

import { useEffect, useRef } from "react";
import { useBranding } from "@/hooks/useBranding";

export function DynamicFavicon() {
  const { faviconUrl } = useBranding();
  const addedLinksRef = useRef<HTMLLinkElement[]>([]);

  useEffect(() => {
    const cleanup = () => {
      addedLinksRef.current.forEach((link) => {
        try {
          if (link.parentNode) {
            link.parentNode.removeChild(link);
          }
        } catch (error) {
          // Ignore errors if element is already removed
        }
      });
      addedLinksRef.current = [];
    };

    if (faviconUrl) {
      const head = document.getElementsByTagName("head")[0];
      if (!head) return cleanup;

      // Only remove links we previously added, not all favicon links
      cleanup();

      // Add new favicon link
      const link = document.createElement("link");
      link.rel = "icon";
      link.type = faviconUrl.endsWith(".ico") ? "image/x-icon" : "image/png";
      link.href = faviconUrl;
      link.setAttribute("data-dynamic-favicon", "true");
      head.appendChild(link);
      addedLinksRef.current.push(link);

      // Add shortcut icon for older browsers
      const shortcutLink = document.createElement("link");
      shortcutLink.rel = "shortcut icon";
      shortcutLink.href = faviconUrl;
      shortcutLink.setAttribute("data-dynamic-favicon", "true");
      head.appendChild(shortcutLink);
      addedLinksRef.current.push(shortcutLink);

      // Add apple touch icon
      const appleLink = document.createElement("link");
      appleLink.rel = "apple-touch-icon";
      appleLink.href = faviconUrl;
      appleLink.setAttribute("data-dynamic-favicon", "true");
      head.appendChild(appleLink);
      addedLinksRef.current.push(appleLink);
    }

    // Return cleanup function for useEffect
    return cleanup;
  }, [faviconUrl]);

  return null; // This component doesn't render anything
}
