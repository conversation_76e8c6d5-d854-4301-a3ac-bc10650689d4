import { NextRequest, NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { prisma } from '@/lib/prisma'
import { externalDeviceAPI } from '@/lib/api/external-device-api'

// Transform external API device data to our internal format (same as admin)
function transformDeviceData(externalDevice: Record<string, any>) {
  // Handle both field name formats from the API
  const machineName = externalDevice['MACHINE_NAME'] || externalDevice['MACHINE NAME'];
  const serialNo = externalDevice['SERIAL_NO'] || externalDevice['SERIAL NO'];
  const modelNo = externalDevice['MODEL_NO'] || externalDevice['MODEL NO'];
  const modelName = externalDevice['MODEL_NAME'] || externalDevice['MODEL NAME'];
  const location = externalDevice['MACHINE_LOCATION'] || externalDevice['MACHINE LOCATION'];
  const endpointUrl = externalDevice['ENDPOINT_URL'] || externalDevice['ENDPOINT URL'];
  const lastUpdate = externalDevice['LAST_UPDATE_DATE'];
  const timeZone = externalDevice['TIME_ZONE'] || externalDevice['TIME ZONE'];
  const logCount = externalDevice['LOG_COUNT'] || externalDevice['LOG COUNT'];
  const userCount = externalDevice['USER_COUNT'] || externalDevice['USER COUNT'];

  return {
    id: serialNo, // Use serial number as ID
    name: machineName || 'Unnamed Device',
    serialNumber: serialNo,
    model: modelNo || 'Unknown',
    modelName: modelName || 'Unknown',
    location: location || 'Not specified',
    ipAddress: (() => {
      try {
        if (typeof endpointUrl === 'string' && endpointUrl.includes('://') && !endpointUrl.includes(':null')) {
          return new URL(endpointUrl).hostname;
        }
        return 'Unknown';
      } catch {
        return 'Unknown';
      }
    })(),
    port: (() => {
      try {
        if (typeof endpointUrl === 'string' && endpointUrl.includes(':') && !endpointUrl.includes(':null')) {
          const portStr = endpointUrl.split(':').pop() || '4370';
          const port = parseInt(portStr);
          return isNaN(port) ? 4370 : port;
        }
        return 4370;
      } catch {
        return 4370;
      }
    })(),
    status: 'UNKNOWN', // Will be updated with real-time status from GETDEVICEONLINESTATUS
    lastSeen: typeof lastUpdate === 'string' && lastUpdate !== '{}' ? lastUpdate : null,
    firmwareVersion: 'Unknown', // Not provided by external API
    totalUsers: typeof userCount === 'number' ? userCount : 0,
    timeZone: typeof timeZone === 'number' ? timeZone : 57,
    logCount: typeof logCount === 'number' ? logCount : 0,
    features: {
      faceRecognition: externalDevice['ISFACE'] === 1,
      fingerprintScanner: externalDevice['ISFINGER'] === 1,
      cardReader: externalDevice['ISCARD'] === 1,
      temperatureCheck: false, // Not provided by external API
    },
    biometricCounts: {
      fingerprints: externalDevice['FINGERPRINTCOUNT'] || 0,
      faces: externalDevice['FACECOUNT'] || 0,
      cards: externalDevice['CARDCOUNT'] || 0,
      passwords: externalDevice['PASSWORDCOUNT'] || 0,
    },
    endpointUrl: endpointUrl || '',
    rawApiData: externalDevice, // Store raw data for debugging
  };
}

// GET /api/dashboard/devices - Get company allocated devices (same data as admin, filtered)
async function getCompanyDevices(req: AuthenticatedRequest) {
  try {
    const user = req.user!

    if (user.role !== 'company') {
      return NextResponse.json(
        { error: 'Company access required' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';
    const sortBy = searchParams.get('sortBy') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    try {
      // Get company allocated devices from database
      const company = await prisma.company.findUnique({
        where: { id: user.companyId },
        include: {
          allocations: {
            select: {
              deviceSerialNo: true,
            }
          }
        }
      });

      if (!company) {
        return NextResponse.json(
          { error: 'Company not found' },
          { status: 404 }
        );
      }

      const allocatedSerials = company.allocations.map(a => a.deviceSerialNo);
      console.log(`Company ${user.companyId} has ${allocatedSerials.length} allocated devices:`, allocatedSerials);

      if (allocatedSerials.length === 0) {
        return NextResponse.json({
          devices: [],
          pagination: {
            page,
            pageSize,
            total: 0,
            totalPages: 0,
          },
        });
      }

      // Fetch all devices from external API (same as admin)
      const response = await externalDeviceAPI.selectDeviceList();

      if (response.status !== '200') {
        return NextResponse.json(
          { error: `External API Error: ${response.msg || 'Unknown error'}` },
          { status: 500 }
        );
      }

      if (!response.data || !Array.isArray(response.data)) {
        return NextResponse.json(
          { error: 'Invalid response data from external API' },
          { status: 500 }
        );
      }

      console.log(`External API returned ${response.data.length} total devices`);



      // Transform and filter for allocated devices only (same logic as admin)
      let devices = response.data
        .map(transformDeviceData)
        .filter(device => allocatedSerials.includes(device.serialNumber));

      // Apply search filter (same as admin)
      if (search) {
        devices = devices.filter(device =>
          device.name.toLowerCase().includes(search.toLowerCase()) ||
          device.serialNumber.toLowerCase().includes(search.toLowerCase()) ||
          device.location.toLowerCase().includes(search.toLowerCase()) ||
          device.model.toLowerCase().includes(search.toLowerCase())
        );
      }

      // Apply status filter (same as admin)
      if (status !== 'all') {
        devices = devices.filter(device =>
          device.status.toLowerCase() === status.toLowerCase()
        );
      }

      // Apply sorting (same as admin)
      const sortedDevices = devices.sort((a, b) => {
        let aValue: string, bValue: string;

        switch (sortBy) {
          case 'serialNumber':
            aValue = a.serialNumber.toLowerCase();
            bValue = b.serialNumber.toLowerCase();
            break;
          case 'location':
            aValue = a.location.toLowerCase();
            bValue = b.location.toLowerCase();
            break;
          case 'model':
            aValue = a.model.toLowerCase();
            bValue = b.model.toLowerCase();
            break;
          case 'totalUsers':
            return sortOrder === 'asc' ? a.totalUsers - b.totalUsers : b.totalUsers - a.totalUsers;
          case 'status':
            aValue = a.status.toLowerCase();
            bValue = b.status.toLowerCase();
            break;
          default:
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
        }

        if (aValue < bValue) {
          return sortOrder === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortOrder === 'asc' ? 1 : -1;
        }
        return 0;
      });

      // Apply pagination (same as admin)
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedDevices = sortedDevices.slice(startIndex, endIndex);

      const apiResponse = {
        devices: paginatedDevices,
        pagination: {
          page,
          pageSize,
          total: sortedDevices.length,
          totalPages: Math.ceil(sortedDevices.length / pageSize),
        },
      };



      return NextResponse.json(apiResponse);

    } catch (error) {
      console.error('Error fetching devices:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in getCompanyDevices:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export const GET = withAuth(getCompanyDevices);
