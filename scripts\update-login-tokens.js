const { PrismaClient } = require("@prisma/client");
const { nanoid } = require("nanoid");

const prisma = new PrismaClient();

// Generate a login token (same as in the auth module)
function generateLoginToken() {
  return nanoid(32);
}

async function updateLoginTokens() {
  try {
    console.log("Updating login tokens for existing companies...");

    // Get all companies that don't have a login token but have a login token hash
    const companies = await prisma.company.findMany({
      where: {
        loginTokenHash: {
          not: null,
        },
        loginToken: null,
      },
    });

    console.log(`Found ${companies.length} companies to update`);

    for (const company of companies) {
      // Generate a new login token
      const newLoginToken = generateLoginToken();

      // Update the company with the new token (we'll keep the old hash for now)
      await prisma.company.update({
        where: { id: company.id },
        data: {
          loginToken: newLoginToken,
        },
      });

      console.log(
        `Updated company: ${company.name} (${company.organizationId})`
      );
      console.log(`New login token: ${newLoginToken}`);
    }

    console.log("Login token update completed!");
  } catch (error) {
    console.error("Error updating login tokens:", error);
  } finally {
    await prisma.$disconnect();
  }
}

updateLoginTokens();
