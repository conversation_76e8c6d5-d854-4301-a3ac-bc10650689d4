"use client";

import { useState, useEffect, useCallback, useRef, memo } from "react";
import { Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface SearchInputProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  debounceMs?: number;
  className?: string;
  initialValue?: string;
  showClearButton?: boolean;
}

const SearchInput = memo(function SearchInput({
  placeholder = "Search...",
  onSearch,
  debounceMs = 500,
  className = "",
  initialValue = "",
  showClearButton = true,
}: SearchInputProps) {
  const [searchQuery, setSearchQuery] = useState(initialValue);
  const [debouncedQuery, setDebouncedQuery] = useState(initialValue);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const onSearchRef = useRef(onSearch);

  // Update the ref when onSearch changes to avoid stale closures
  useEffect(() => {
    onSearchRef.current = onSearch;
  }, [onSearch]);

  // Debounce the search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchQuery, debounceMs]);

  // Call parent onSearch when debounced query changes
  useEffect(() => {
    onSearchRef.current(debouncedQuery);
  }, [debouncedQuery]);

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  }, []);

  // Handle clear button click
  const handleClear = useCallback(() => {
    setSearchQuery("");
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // Handle key press (Enter to search immediately, Escape to clear)
  const handleKeyPress = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      // Trigger immediate search on Enter
      setDebouncedQuery(searchQuery);
    } else if (e.key === "Escape") {
      // Clear search on Escape
      handleClear();
    }
  }, [searchQuery, handleClear]);

  return (
    <div className={`relative ${className}`}>
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4 pointer-events-none" />
      <Input
        ref={searchInputRef}
        type="text"
        placeholder={placeholder}
        value={searchQuery}
        onChange={handleInputChange}
        onKeyDown={handleKeyPress}
        className="pl-10 pr-10"
        autoComplete="off"
        spellCheck={false}
      />
      {showClearButton && searchQuery && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleClear}
          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted"
          aria-label="Clear search"
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
});

SearchInput.displayName = "SearchInput";

export { SearchInput };
export type { SearchInputProps };
