import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/admin/settings/branding - Get branding data (public endpoint for layout)
export async function GET(request: NextRequest) {
  try {
    // Get admin settings from database
    const adminSettings = await prisma.adminSettings.findFirst();
    
    // Return branding data or defaults
    const branding = {
      logoUrl: adminSettings?.logoUrl || '',
      faviconUrl: adminSettings?.faviconUrl || '',
      siteName: adminSettings?.siteName || 'SRITechnologies'
    };
    
    return NextResponse.json(branding);

  } catch (error) {
    console.error('Error fetching branding data:', error);
    // Return defaults on error
    return NextResponse.json({
      logoUrl: '',
      faviconUrl: '',
      siteName: 'SRITechnologies'
    });
  }
}
