"use client";

import { useState, useCallback, useEffect } from "react";
import { toast } from "react-toastify";
import { useCompanyStore } from "@/stores/useCompanyStore";
import { useCompaniesWithTokensQuery, useCreateCompanyMutation, useUpdateCompanyMutation, useDeleteCompanyMutation } from "@/hooks/queries/useCompaniesQuery";

export function useCompanyManagement() {
  // Zustand store state
  const {
    filters,
    pagination,
    selectedCompanies,
    setSearch,
    setStatus,
    setUserType,
    setSorting,
    setPage,
    setPageSize,
    setPagination,
    setSelectedCompanies,
    toggleCompanySelection,
    selectAllCompanies,
    clearSelection,
    resetFilters,
  } = useCompanyStore();

  // Local UI state
  const [showRegistrationForm, setShowRegistrationForm] = useState(false);

  // TanStack Query hooks
  const {
    data: queryData,
    isLoading,
    error,
    refetch
  } = useCompaniesWithTokensQuery(filters, pagination);

  const createCompanyMutation = useCreateCompanyMutation();
  const updateCompanyMutation = useUpdateCompanyMutation();
  const deleteCompanyMutation = useDeleteCompanyMutation();

  // Update pagination when query data changes
  useEffect(() => {
    if (queryData?.pagination) {
      setPagination(queryData.pagination);
    }
  }, [queryData?.pagination, setPagination]);

  // Handle errors in useEffect to avoid state updates during render
  useEffect(() => {
    if (error) {
      toast.error(error.message || "Failed to fetch companies");
    }
  }, [error]);

  // Handlers
  const handlePageChange = useCallback((page: number) => {
    setPage(page);
  }, [setPage]);

  const handleSearch = useCallback((search: string) => {
    setSearch(search);
  }, [setSearch]);

  const handleSort = useCallback((column: string) => {
    setSorting(column);
  }, [setSorting]);

  const handleBulkStatusUpdate = useCallback(async (status: 'ACTIVE' | 'DEACTIVATED') => {
    if (selectedCompanies.length === 0) {
      toast.error('Please select companies to update');
      return;
    }

    try {
      const response = await fetch("/api/admin/companies/bulk-status", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          companyIds: selectedCompanies.map((c) => c.id),
          status,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(result.message || `Companies ${status.toLowerCase()} successfully`);
        refetch(); // Refresh data using TanStack Query
        clearSelection(); // Clear selection after action
      } else {
        const error = await response.json();
        toast.error(error.error || `Failed to update company status`);
      }
    } catch {
      toast.error(`Failed to update company status`);
    }
  }, [selectedCompanies, refetch, clearSelection]);

  const handleExtendValidity = useCallback(async (extensionData: {
    type: 'days' | 'months' | 'years' | 'custom';
    value?: number;
    customDate?: string;
  }) => {
    if (selectedCompanies.length === 0) {
      toast.error('Please select companies to extend validity');
      return;
    }

    try {
      const response = await fetch("/api/admin/companies/extend-validity", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          companyIds: selectedCompanies.map((c) => c.id),
          ...extensionData,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(result.message || `Company validity extended successfully`);
        refetch(); // Refresh data using TanStack Query
        clearSelection(); // Clear selection after action
      } else {
        const error = await response.json();
        toast.error(error.error || `Failed to extend company validity`);
      }
    } catch {
      toast.error(`Failed to extend company validity`);
    }
  }, [selectedCompanies, refetch, clearSelection]);

  const handleLoginAsCompany = useCallback(async (companyId: string) => {
    try {
      const response = await fetch("/api/auth/admin-login-as-company", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ companyId }),
      });

      if (response.ok) {
        const result = await response.json();

        // Set admin return session flag if this is admin impersonation
        if (result.isAdminImpersonation) {
          sessionStorage.setItem('adminReturnSession', 'true');
        }

        toast.success("Logged in as company successfully");
        // Redirect to company dashboard
        window.location.href = "/dashboard";
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to login as company");
      }
    } catch {
      toast.error("Failed to login as company");
    }
  }, []);

  return {
    // State from TanStack Query
    companies: queryData?.data || [],
    pagination,
    loading: isLoading,
    error,

    // State from Zustand store
    filters,
    selectedCompanies,

    // Local UI state
    showRegistrationForm,

    // Store actions
    setSearch,
    setStatus,
    setUserType,
    setSorting,
    setPage,
    setPageSize,
    setSelectedCompanies,
    toggleCompanySelection,
    selectAllCompanies,
    clearSelection,
    resetFilters,

    // Local setters
    setShowRegistrationForm,

    // Handlers
    handlePageChange,
    handleSearch,
    handleSort,
    handleBulkStatusUpdate,
    handleExtendValidity,
    handleLoginAsCompany,

    // TanStack Query actions
    refetch,
    createCompany: createCompanyMutation.mutate,
    updateCompany: updateCompanyMutation.mutate,
    deleteCompany: deleteCompanyMutation.mutate,

    // Mutation states
    isCreating: createCompanyMutation.isPending,
    isUpdating: updateCompanyMutation.isPending,
    isDeleting: deleteCompanyMutation.isPending,
  };
}
