"use client";

import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

interface Device {
  id: string;
  name: string;
  serialNumber: string;
  model: string;
  location: string;
  status: string;
  totalUsers: number;
  allocatedCompany?: string | null;
  lastSync?: string;
  battery?: number;
  employees?: number;
}

interface DeviceStatus {
  deviceId: string;
  status: "online" | "offline";
  lastChecked: Date;
}

interface Filters {
  search: string;
  status: string;
  location: string;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
}

export function useUserDeviceManagement() {
  const [filters, setFilters] = useState<Filters>({
    search: "",
    status: "",
    location: "",
  });

  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    total: 0,
  });

  const [selectedDevices, setSelectedDevices] = useState<string[]>([]);
  const [deviceStatuses, setDeviceStatuses] = useState<
    Record<string, DeviceStatus>
  >({});
  const [statusLoading, setStatusLoading] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  // Fetch company devices
  const {
    data: devicesData,
    isLoading: loading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["user-devices", filters, pagination.page],
    queryFn: async () => {
      const response = await fetch("/api/dashboard/devices");
      if (!response.ok) {
        throw new Error("Failed to fetch devices");
      }
      const result = await response.json();
      return result.data || [];
    },
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
    retry: 2,
  });

  // Transform API data to match admin interface
  const devices: Device[] = (devicesData || []).map(
    (device: Record<string, unknown>) => ({
      id: device.id,
      name: device.name,
      serialNumber: device.serialNumber || device.id,
      model: device.model || "Unknown",
      modelName: device.modelName || "Unknown",
      location: device.location,
      ipAddress: device.ipAddress || "Unknown",
      port: device.port || 4370,
      status: device.status,
      lastSeen: device.lastSeen,
      firmwareVersion: device.firmwareVersion || "Unknown",
      totalUsers: device.totalUsers || device.employees || 0,
      timeZone: device.timeZone || 57,
      logCount: device.logCount || 0,
      features: device.features || {
        faceRecognition: false,
        fingerprintScanner: false,
        cardReader: false,
        temperatureCheck: false,
      },
      biometricCounts: device.biometricCounts || {
        fingerprints: 0,
        faces: 0,
        cards: 0,
        passwords: 0,
      },
      createdAt: device.createdAt,
      updatedAt: device.updatedAt,
      allocatedCompany: device.allocatedCompany || "Current Company",
      lastSync: device.lastSync,
      battery: device.battery,
      employees: device.employees || device.totalUsers || 0,
    })
  );

  // Apply filters
  const filteredDevices = devices.filter((device) => {
    const matchesSearch =
      device.name.toLowerCase().includes(filters.search.toLowerCase()) ||
      device.serialNumber
        .toLowerCase()
        .includes(filters.search.toLowerCase()) ||
      device.location.toLowerCase().includes(filters.search.toLowerCase());

    const matchesStatus = !filters.status || device.status === filters.status;
    const matchesLocation =
      !filters.location || device.location === filters.location;

    return matchesSearch && matchesStatus && matchesLocation;
  });

  // Update pagination total
  useEffect(() => {
    setPagination((prev) => ({
      ...prev,
      total: filteredDevices.length,
    }));
  }, [filteredDevices.length]);

  // Get paginated devices
  const startIndex = (pagination.page - 1) * pagination.limit;
  const paginatedDevices = filteredDevices.slice(
    startIndex,
    startIndex + pagination.limit
  );

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  // Handle filter change
  const handleFilterChange = (newFilters: Partial<Filters>) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
    setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page
  };

  // Refresh device status for all devices
  const handleRefreshAllDevicesStatus = async () => {
    if (devices.length === 0) {
      toast.info("No devices to refresh");
      return;
    }

    setStatusLoading(true);
    try {
      const statusPromises = devices.map(async (device) => {
        try {
          const response = await fetch(
            `/api/dashboard/devices/${device.serialNumber}/status`
          );
          if (response.ok) {
            const result = await response.json();
            return {
              deviceId: device.id,
              status: result.status === "ONLINE" ? "online" : "offline",
              lastChecked: new Date(),
            };
          }
        } catch (error) {
          console.warn(`Failed to get status for device ${device.id}:`, error);
        }
        return {
          deviceId: device.id,
          status: "offline" as const,
          lastChecked: new Date(),
        };
      });

      const statuses = await Promise.all(statusPromises);
      const statusMap = statuses.reduce((acc, status) => {
        acc[status.deviceId] = status as DeviceStatus;
        return acc;
      }, {} as Record<string, DeviceStatus>);

      setDeviceStatuses(statusMap);
      toast.success(`Refreshed status for ${devices.length} devices`);

      // Refetch devices to get updated data
      refetch();
    } catch (error) {
      console.error("Error refreshing device statuses:", error);
      toast.error("Failed to refresh device statuses");
    } finally {
      setStatusLoading(false);
    }
  };

  // Handle device sync (limited functionality for users)
  const handleDeviceSync = async () => {
    try {
      toast.info("Syncing device data...");
      // For users, this just refreshes the device data
      await refetch();
      toast.success("Device data synced successfully");
    } catch (error) {
      console.error("Error syncing device:", error);
      toast.error("Failed to sync device");
    }
  };

  // Hydration effect
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Handle error
  useEffect(() => {
    if (error) {
      toast.error("Failed to fetch devices");
    }
  }, [error]);

  return {
    // Data
    devices: paginatedDevices,
    allDevices: devices,
    loading,
    error,

    // Filters and pagination
    filters,
    pagination,
    handleFilterChange,
    handlePageChange,

    // Selection
    selectedDevices,
    setSelectedDevices,

    // Status management
    deviceStatuses,
    statusLoading,
    handleRefreshAllDevicesStatus,

    // Actions (limited for users)
    handleDeviceSync,
    refetch,

    // UI state
    isHydrated,
  };
}
