"use client";

import { usePara<PERSON>, useRouter } from "next/navigation";
import { DashboardLayout } from "@/components/shared/dashboard-layout";
import { DeviceActionsContent } from "@/components/features/device-actions-content";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default function DeviceActionsPage() {
  const params = useParams();
  const router = useRouter();
  const deviceId = params.deviceId as string;

  const handleBack = () => {
    router.push("/admin/devices");
  };

  return (
    <DashboardLayout userRole="admin" userEmail="<EMAIL>">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Device Actions</h1>
            <p className="text-gray-600">
              Manage device settings and operations for device {deviceId}
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className="self-end-safe flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Devices
          </Button>
        </div>

        {/* Device Actions Content */}
        <DeviceActionsContent deviceId={deviceId} />
      </div>
    </DashboardLayout>
  );
}
