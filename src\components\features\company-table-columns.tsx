"use client";

import { useMemo } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Column } from "@/components/shared/data-table";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Calendar,
  Building2,
  LogIn,
} from "lucide-react";
import { CompanyWithAllocations } from "@/types";

interface UseCompanyColumnsProps {
  onCompanyAction: (action: string, companyId?: string) => void;
  onLoginAsCompany: (companyId: string) => void;
}

export function useCompanyColumns({
  onCompanyAction,
  onLoginAsCompany,
}: UseCompanyColumnsProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700">
            Active
          </Badge>
        );
      case "INACTIVE":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700">
            Inactive
          </Badge>
        );
      case "PENDING":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
            Pending
          </Badge>
        );
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const columns: Column<CompanyWithAllocations>[] = useMemo(
    () => [
      {
        key: "companyInfo",
        label: "Company Info",
        sortable: true,
        render: (_, company) => (
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <Building2 className="h-8 w-8 text-gray-400" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900">
                {company.name}
              </div>
              <div className="text-xs text-gray-500">{company.email}</div>
              <div className="text-xs text-gray-500">
                {company.phone || "No phone"}
              </div>
            </div>
          </div>
        ),
      },
      {
        key: "status",
        label: "Status",
        sortable: true,
        render: (_, company) => getStatusBadge(company.status),
      },
      {
        key: "address",
        label: "Address",
        render: (_, company) => (
          <div className="text-sm text-gray-900">
            {company.address ? (
              <div>{company.address}</div>
            ) : (
              <span className="text-gray-500">No address</span>
            )}
          </div>
        ),
      },
      {
        key: "allocatedDevices",
        label: "Allocated Devices",
        sortable: true,
        render: (_, company) => (
          <div className="text-sm text-gray-900">
            <div className="font-medium">
              {company.allocations?.length || 0} devices
            </div>
            {company.allocations && company.allocations.length > 0 && (
              <div className="text-xs text-gray-500">
                {company.allocations
                  .slice(0, 2)
                  .map((allocation) => allocation.device.deviceName)
                  .join(", ")}
                {company.allocations.length > 2 &&
                  ` +${company.allocations.length - 2} more`}
              </div>
            )}
          </div>
        ),
      },
      {
        key: "registrationDate",
        label: "Registration Date",
        sortable: true,
        render: (_, company) => {
          if (!company.createdAt) {
            return <div className="text-sm text-gray-500">Unknown</div>;
          }
          try {
            const date = new Date(company.createdAt);
            return (
              <div className="text-sm text-gray-900">
                {date.toLocaleDateString()}
                <div className="text-xs text-gray-500">
                  {date.toLocaleTimeString()}
                </div>
              </div>
            );
          } catch {
            return <div className="text-sm text-gray-500">Invalid date</div>;
          }
        },
      },
      {
        key: "actions",
        label: "Actions",
        render: (_, company) => (
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onLoginAsCompany(company.id)}
            >
              <LogIn className="h-4 w-4 mr-1" />
              Login As
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => onCompanyAction("edit", company.id)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Company
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onCompanyAction("viewDetails", company.id)}
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => onCompanyAction("delete", company.id)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Company
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        ),
      },
    ],
    [onCompanyAction, onLoginAsCompany]
  );

  return columns;
}
