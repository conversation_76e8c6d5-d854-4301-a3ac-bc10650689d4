"use client";

import { DashboardLayout } from "@/components/shared/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Building2,
  Smartphone,
  Users,
  AlertTriangle,
  TrendingUp,
  Activity,
} from "lucide-react";
import {
  useAdminStatsQuery,
  useAdminActivityQuery,
  useAdminSystemStatusQuery,
} from "@/hooks/queries/useDashboardQueries";
import { useAdminProfileQuery } from "@/hooks/queries/useProfileQueries";
import { RecentActivity } from "@/components/features/dashboard/recent-activity";
import { SystemStatusCard } from "@/components/features/dashboard/system-status";

export default function AdminDashboard() {
  // Use real-time data hooks
  const {
    data: stats,
    isLoading: statsLoading,
    error: statsError,
  } = useAdminStatsQuery();
  const {
    data: activities,
    isLoading: activitiesLoading,
    error: activitiesError,
  } = useAdminActivityQuery();
  const {
    data: systemStatus,
    isLoading: statusLoading,
    error: statusError,
  } = useAdminSystemStatusQuery();
  const { data: profile, isLoading: profileLoading } = useAdminProfileQuery();

  const statCards = [
    {
      title: "Total Companies",
      value: stats?.totalCompanies || 0,
      description: "Registered companies",
      icon: Building2,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Active Companies",
      value: stats?.activeCompanies || 0,
      description: "Currently active",
      icon: Users,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Expired Companies",
      value: stats?.expiredCompanies || 0,
      description: "Need renewal",
      icon: AlertTriangle,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
    {
      title: "Total Devices",
      value: stats?.totalDevices || 0,
      description: "All devices",
      icon: Smartphone,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Allocated Devices",
      value: stats?.allocatedDevices || 0,
      description: "Currently in use",
      icon: Activity,
      color: "text-indigo-600",
      bgColor: "bg-indigo-100",
    },
    {
      title: "Available Devices",
      value: stats?.unallocatedDevices || 0,
      description: "Ready for allocation",
      icon: TrendingUp,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
  ];

  if (statsLoading) {
    return (
      <DashboardLayout
        userRole="admin"
        userEmail={profile?.email || "<EMAIL>"}
        userName={profile?.name}
      >
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">Welcome to the admin portal</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                  <div className="h-4 w-4 bg-gray-200 rounded"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-20"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      userRole="admin"
      userEmail={profile?.email || "<EMAIL>"}
      userName={profile?.name}
    >
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Welcome to the admin portal</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {statCards.map((card, index) => {
            const Icon = card.icon;
            return (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">
                    {card.title}
                  </CardTitle>
                  <div className={`p-2 rounded-lg ${card.bgColor}`}>
                    <Icon className={`h-4 w-4 ${card.color}`} />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {card.value.toLocaleString()}
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    {card.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Real-time Activity and Status */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <RecentActivity
            activities={activities || []}
            loading={activitiesLoading}
            error={activitiesError?.message}
            title="Recent Activity"
            description="Latest system activities"
          />

          <SystemStatusCard
            systemStatus={systemStatus}
            loading={statusLoading}
            error={statusError?.message}
            title="System Status"
            description="Current system health"
          />
        </div>
      </div>
    </DashboardLayout>
  );
}
