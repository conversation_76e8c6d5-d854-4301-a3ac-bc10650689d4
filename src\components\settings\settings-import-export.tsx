import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Download, Upload, FileText, AlertTriangle, CheckCircle, Info } from "lucide-react";
import { toast } from "react-toastify";
import {
  exportSettings,
  importSettings,
  validateImportedSettings,
  compareSettings,
  generateSettingsSummary,
  type SettingsExport,
} from "@/lib/settings-export";

interface SettingsImportExportProps {
  settings: any;
  type: "admin" | "company";
  userEmail: string;
  companyId?: string;
  onImport: (settings: any) => void;
  className?: string;
}

export function SettingsImportExport({
  settings,
  type,
  userEmail,
  companyId,
  onImport,
  className = "",
}: SettingsImportExportProps) {
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [importData, setImportData] = useState<SettingsExport | null>(null);
  const [importErrors, setImportErrors] = useState<string[]>([]);
  const [isImporting, setIsImporting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleExport = () => {
    try {
      exportSettings(settings, type, userEmail, companyId);
      toast.success("Settings exported successfully");
    } catch (error) {
      toast.error("Failed to export settings");
      console.error("Export error:", error);
    }
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const data = await importSettings(file);
      const validation = validateImportedSettings(data, type);
      
      if (!validation.isValid) {
        setImportErrors(validation.errors);
        setImportData(null);
      } else {
        setImportErrors([]);
        setImportData(data);
      }
    } catch (error) {
      setImportErrors([error instanceof Error ? error.message : "Unknown error"]);
      setImportData(null);
    }
  };

  const handleImport = async () => {
    if (!importData) return;

    setIsImporting(true);
    try {
      onImport(importData.settings);
      setIsImportDialogOpen(false);
      setImportData(null);
      toast.success("Settings imported successfully");
    } catch (error) {
      toast.error("Failed to import settings");
      console.error("Import error:", error);
    } finally {
      setIsImporting(false);
    }
  };

  const resetImport = () => {
    setImportData(null);
    setImportErrors([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const currentSummary = generateSettingsSummary(settings, type);
  const importSummary = importData ? generateSettingsSummary(importData.settings, type) : null;
  const differences = importData ? compareSettings(settings, importData.settings) : null;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          Import / Export Settings
        </CardTitle>
        <CardDescription>
          Backup your settings or import settings from another system
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Settings Summary */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Current Settings</h4>
          <div className="flex flex-wrap gap-2">
            {currentSummary.sections.map((section) => (
              <Badge key={section.name} variant="secondary">
                {section.name}: {section.count}
              </Badge>
            ))}
            <Badge variant="outline">Total: {currentSummary.totalSettings}</Badge>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button onClick={handleExport} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Settings
          </Button>

          <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" onClick={resetImport}>
                <Upload className="h-4 w-4 mr-2" />
                Import Settings
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Import Settings</DialogTitle>
                <DialogDescription>
                  Select a settings file to import. This will replace your current settings.
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                {/* File Input */}
                <div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".json"
                    onChange={handleFileSelect}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                </div>

                {/* Import Errors */}
                {importErrors.length > 0 && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-1">
                        <p className="font-medium">Import validation failed:</p>
                        <ul className="list-disc list-inside space-y-1">
                          {importErrors.map((error, index) => (
                            <li key={index} className="text-sm">{error}</li>
                          ))}
                        </ul>
                      </div>
                    </AlertDescription>
                  </Alert>
                )}

                {/* Import Preview */}
                {importData && importSummary && differences && (
                  <div className="space-y-4">
                    <Alert>
                      <CheckCircle className="h-4 w-4" />
                      <AlertDescription>
                        Settings file is valid and ready to import.
                      </AlertDescription>
                    </Alert>

                    {/* Import Summary */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Import Summary</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p><strong>Exported:</strong> {new Date(importData.exportDate).toLocaleDateString()}</p>
                          <p><strong>By:</strong> {importData.exportedBy}</p>
                        </div>
                        <div>
                          <p><strong>Type:</strong> {importData.metadata.settingsType}</p>
                          <p><strong>Version:</strong> {importData.version}</p>
                        </div>
                      </div>
                    </div>

                    {/* Changes Preview */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Changes Preview</h4>
                      <div className="space-y-2">
                        {differences.modified.length > 0 && (
                          <div>
                            <Badge variant="secondary" className="mb-2">
                              {differences.modified.length} Modified
                            </Badge>
                            <div className="text-xs text-gray-600 max-h-20 overflow-y-auto">
                              {differences.modified.slice(0, 5).map((field) => (
                                <div key={field}>• {field}</div>
                              ))}
                              {differences.modified.length > 5 && (
                                <div>... and {differences.modified.length - 5} more</div>
                              )}
                            </div>
                          </div>
                        )}

                        {differences.added.length > 0 && (
                          <div>
                            <Badge variant="default" className="mb-2">
                              {differences.added.length} Added
                            </Badge>
                            <div className="text-xs text-gray-600 max-h-20 overflow-y-auto">
                              {differences.added.slice(0, 5).map((field) => (
                                <div key={field}>• {field}</div>
                              ))}
                              {differences.added.length > 5 && (
                                <div>... and {differences.added.length - 5} more</div>
                              )}
                            </div>
                          </div>
                        )}

                        {differences.removed.length > 0 && (
                          <div>
                            <Badge variant="destructive" className="mb-2">
                              {differences.removed.length} Removed
                            </Badge>
                            <div className="text-xs text-gray-600 max-h-20 overflow-y-auto">
                              {differences.removed.slice(0, 5).map((field) => (
                                <div key={field}>• {field}</div>
                              ))}
                              {differences.removed.length > 5 && (
                                <div>... and {differences.removed.length - 5} more</div>
                              )}
                            </div>
                          </div>
                        )}

                        {differences.modified.length === 0 && differences.added.length === 0 && differences.removed.length === 0 && (
                          <Alert>
                            <Info className="h-4 w-4" />
                            <AlertDescription>
                              No changes detected. The imported settings are identical to your current settings.
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    </div>

                    {/* Import Button */}
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleImport} disabled={isImporting}>
                        {isImporting ? "Importing..." : "Import Settings"}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardContent>
    </Card>
  );
}
