import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`
    
    // Check environment variables
    const requiredEnvVars = [
      'DATABASE_URL',
      'NEXTAUTH_SECRET',
      'JWT_SECRET'
    ]
    
    const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName])
    
    if (missingEnvVars.length > 0) {
      return NextResponse.json({
        status: 'error',
        message: 'Missing environment variables',
        missing: missingEnvVars
      }, { status: 500 })
    }
    
    // Check uploads directory
    const fs = require('fs')
    const path = require('path')
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads')
    
    if (!fs.existsSync(uploadsDir)) {
      return NextResponse.json({
        status: 'warning',
        message: 'Uploads directory does not exist',
        database: 'connected',
        environment: 'configured'
      }, { status: 200 })
    }
    
    return NextResponse.json({
      status: 'healthy',
      message: 'All systems operational',
      database: 'connected',
      environment: 'configured',
      uploads: 'ready',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0'
    })
    
  } catch (error) {
    console.error('Health check failed:', error)
    
    return NextResponse.json({
      status: 'error',
      message: 'Health check failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
