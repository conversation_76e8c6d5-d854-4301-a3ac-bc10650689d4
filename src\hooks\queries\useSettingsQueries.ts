import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "@/lib/queryKeys";
import axios from "axios";

// Types for admin settings
export interface AdminSettings {
  general: {
    siteName: string;
    siteDescription: string;
    defaultTimeZone: string;
    dateFormat: string;
    language: string;
    maintenanceMode: boolean;
    logoUrl: string;
    faviconUrl: string;
  };
  security: {
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordMinLength: number;
    requireTwoFactor: boolean;
    allowPasswordReset: boolean;
    ipWhitelist: string[];
  };
  notifications: {
    emailNotifications: boolean;
    deviceOfflineAlerts: boolean;
    companyExpiryAlerts: boolean;
    systemMaintenanceAlerts: boolean;
    adminEmail: string;
    alertThresholds: {
      deviceOfflineMinutes: number;
      companyExpiryDays: number;
    };
  };
  devices: {
    autoSyncInterval: number;
    maxDevicesPerCompany: number;
    defaultDeviceTimeout: number;
    allowBulkOperations: boolean;
    requireDeviceApproval: boolean;
  };
  backup: {
    autoBackup: boolean;
    backupFrequency: string;
    retentionDays: number;
    backupLocation: string;
  };
}

// Types for company settings
export interface CompanySettings {
  general: {
    companyName: string;
    timezone: string;
    dateFormat: string;
    workingHours: {
      start: string;
      end: string;
    };
    workingDays: string[];
  };
  notifications: {
    emailAlerts: boolean;
    attendanceReminders: boolean;
    lateArrivalAlerts: boolean;
    absenteeAlerts: boolean;
    deviceOfflineAlerts: boolean;
    weeklyReports: boolean;
    monthlyReports: boolean;
  };
  attendance: {
    graceTime: number;
    autoClockOut: boolean;
    autoClockOutTime: string;
    allowManualEntry: boolean;
    requireApproval: boolean;
    trackBreaks: boolean;
    maxWorkingHours: number;
  };
  devices: {
    allowMultipleCheckIns: boolean;
    deviceSyncInterval: number;
    offlineDataRetention: number;
    faceRecognitionThreshold: number;
    fingerprintThreshold: number;
  };
}

// Fetch functions
const fetchAdminSettings = async (): Promise<AdminSettings> => {
  try {
    const response = await axios.get("/api/admin/settings");

    if (!response.data) {
      throw new Error("No data received from admin settings API");
    }

    return response.data;
  } catch (error) {
    console.error("Error fetching admin settings:", error);
    throw error;
  }
};

const fetchCompanySettings = async (): Promise<CompanySettings> => {
  try {
    const response = await axios.get("/api/dashboard/settings");

    if (!response.data || !response.data.data) {
      throw new Error("No data received from company settings API");
    }

    return response.data.data;
  } catch (error) {
    console.error("Error fetching company settings:", error);
    throw error;
  }
};

const updateAdminSettings = async (
  settings: Partial<AdminSettings>
): Promise<AdminSettings> => {
  const response = await axios.put("/api/admin/settings", settings);
  return response.data;
};

const updateCompanySettings = async (
  settings: CompanySettings
): Promise<CompanySettings> => {
  try {
    const response = await axios.put("/api/dashboard/settings", settings);

    if (!response.data || !response.data.success) {
      throw new Error("Failed to update company settings");
    }

    // Return the updated settings (the API doesn't return the data, so return what we sent)
    return settings;
  } catch (error) {
    console.error("Error updating company settings:", error);
    throw error;
  }
};

// Hooks for admin settings
export function useAdminSettingsQuery() {
  return useQuery({
    queryKey: queryKeys.settings.admin(),
    queryFn: fetchAdminSettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  });
}

export function useUpdateAdminSettingsMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateAdminSettings,
    onSuccess: (data) => {
      // Update the settings cache
      queryClient.setQueryData(queryKeys.settings.admin(), data);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.settings.all });
    },
    onError: (error) => {
      console.error("Failed to update admin settings:", error);
    },
  });
}

// Hooks for company settings
export function useCompanySettingsQuery() {
  return useQuery({
    queryKey: queryKeys.settings.company("current"),
    queryFn: fetchCompanySettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  });
}

export function useUpdateCompanySettingsMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateCompanySettings,
    onSuccess: (data) => {
      // Update the settings cache
      queryClient.setQueryData(queryKeys.settings.company("current"), data);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.settings.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.all });
    },
    onError: (error) => {
      console.error("Failed to update company settings:", error);
    },
  });
}
