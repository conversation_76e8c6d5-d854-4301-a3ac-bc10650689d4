import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useMemo } from 'react';
import { queryKeys } from '@/lib/queryKeys';
import { CompanyFilters, CompanyPagination, Company } from '@/stores/useCompanyStore';
import { CompanyWithAllocations } from '@/types';
import axios from 'axios';

interface CompaniesResponse {
  success: boolean;
  data: CompanyWithAllocations[];
  pagination: CompanyPagination;
}

interface CompanyQueryParams {
  page: number;
  pageSize: number;
  search?: string;
  status?: string;
  userType?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Fetch companies function
async function fetchCompanies(params: CompanyQueryParams): Promise<CompaniesResponse> {
  const searchParams = new URLSearchParams({
    page: params.page.toString(),
    pageSize: params.pageSize.toString(),
  });

  if (params.search) searchParams.append('query', params.search);
  if (params.status) searchParams.append('status', params.status);
  if (params.userType) searchParams.append('userType', params.userType);
  if (params.sortBy) searchParams.append('sortBy', params.sortBy);
  if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

  const response = await fetch(`/api/admin/companies?${searchParams}`);
  
  if (!response.ok) {
    if (response.status === 503) {
      const error = await response.json();
      throw new Error(error.error || 'Database connection failed');
    }
    throw new Error('Failed to fetch companies');
  }

  return response.json();
}

// Fetch companies with tokens function
async function fetchCompaniesWithTokens(params: CompanyQueryParams): Promise<CompaniesResponse> {
  const searchParams = new URLSearchParams({
    page: params.page.toString(),
    pageSize: params.pageSize.toString(),
  });

  if (params.search) searchParams.append('query', params.search);
  if (params.status) searchParams.append('status', params.status);
  if (params.userType) searchParams.append('userType', params.userType);
  if (params.sortBy) searchParams.append('sortBy', params.sortBy);
  if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

  const response = await fetch(`/api/admin/companies/with-tokens?${searchParams}`);
  
  if (!response.ok) {
    if (response.status === 503) {
      const error = await response.json();
      throw new Error(error.error || 'Database connection failed');
    }
    throw new Error('Failed to fetch companies with tokens');
  }

  return response.json();
}

// Hook for fetching companies
export function useCompaniesQuery(filters: CompanyFilters, pagination: CompanyPagination) {
  const params: CompanyQueryParams = {
    page: pagination.page,
    pageSize: pagination.pageSize,
    search: filters.search || undefined,
    status: filters.status,
    userType: filters.userType,
    sortBy: filters.sortBy,
    sortOrder: filters.sortOrder,
  };

  return useQuery({
    queryKey: queryKeys.companies.list(params as unknown as Record<string, unknown>),
    queryFn: () => fetchCompanies(params),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for fetching companies with tokens (admin only)
export function useCompaniesWithTokensQuery(filters: CompanyFilters, pagination: CompanyPagination) {
  const params: CompanyQueryParams = useMemo(() => ({
    page: pagination.page,
    pageSize: pagination.pageSize,
    search: filters.search || undefined,
    status: filters.status,
    userType: filters.userType,
    sortBy: filters.sortBy,
    sortOrder: filters.sortOrder,
  }), [
    pagination.page,
    pagination.pageSize,
    filters.search,
    filters.status,
    filters.userType,
    filters.sortBy,
    filters.sortOrder,
  ]);

  return useQuery({
    queryKey: queryKeys.companies.list({ ...params, withTokens: true } as unknown as Record<string, unknown>),
    queryFn: () => fetchCompaniesWithTokens(params),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for fetching a single company
export function useCompanyQuery(id: string) {
  return useQuery({
    queryKey: queryKeys.companies.detail(id),
    queryFn: async () => {
      const response = await fetch(`/api/admin/companies/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch company');
      }
      return response.json();
    },
    staleTime: 60 * 1000, // 1 minute
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Mutation hooks for company operations
export function useCreateCompanyMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (companyData: Partial<Company>) => {
      const response = await fetch('/api/admin/companies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(companyData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create company');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch companies list
      queryClient.invalidateQueries({ queryKey: queryKeys.companies.lists() });
    },
  });
}



export function useDeleteCompanyMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/admin/companies/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete company');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate companies list
      queryClient.invalidateQueries({ queryKey: queryKeys.companies.lists() });
    },
  });
}

// Update company mutation
interface UpdateCompanyData {
  id: string
  name: string
  email: string
  userType: string
  status: string
  expiresAt: string
  description?: string
}

const updateCompany = async (data: UpdateCompanyData): Promise<{ success: boolean; data: Company; message: string }> => {
  const response = await axios.put(`/api/admin/companies/${data.id}`, data)
  return response.data
}

export function useUpdateCompanyMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateCompany,
    onSuccess: () => {
      // Invalidate and refetch companies list
      queryClient.invalidateQueries({ queryKey: queryKeys.companies.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.all })
    },
    onError: (error) => {
      console.error('Failed to update company:', error)
    }
  })
}

// Extend validity mutation
interface ExtendValidityData {
  companyIds: string[]
  type: 'days' | 'months' | 'years' | 'custom'
  value?: number
  customDate?: string
}

const extendValidity = async (data: ExtendValidityData) => {
  const response = await axios.patch('/api/admin/companies/extend-validity', data)
  return response.data
}

export function useExtendValidityMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: extendValidity,
    onSuccess: () => {
      // Invalidate and refetch companies list
      queryClient.invalidateQueries({ queryKey: queryKeys.companies.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.all })
    },
    onError: (error) => {
      console.error('Failed to extend validity:', error)
    }
  })
}
