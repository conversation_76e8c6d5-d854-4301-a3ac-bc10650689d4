"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetTrigger,
  SheetTitle,
} from "@/components/ui/sheet";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import {
  Menu,
  LayoutDashboard,
  Building2,
  Smartphone,
  User,
  Settings,
  LogOut,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "react-toastify";
import { useUserType } from "@/contexts/user-type-context";
import { DynamicLogoWithFallback } from "@/components/shared/dynamic-logo";
import { AdminReturnBanner } from "@/components/shared/admin-return-banner";
import { useSiteName } from "@/hooks/useSiteName";
import { useBranding } from "@/hooks/useBranding";
import {
  useAdminProfileQuery,
  useCompanyProfileQuery,
} from "@/hooks/queries/useProfileQueries";

interface DashboardLayoutProps {
  children: React.ReactNode;
  userRole: "admin" | "company";
  userEmail: string;
  userName?: string;
}

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  adminOnly?: boolean;
  companyOnly?: boolean;
  dynamicTitle?: boolean; // For items that should use dynamic labels
}

// Function to get navigation items based on user role and type
const getNavItems = (
  userRole: "admin" | "company",
  labels?: any
): NavItem[] => {
  const adminItems: NavItem[] = [
    {
      title: "Dashboard",
      href: "/admin/dashboard",
      icon: LayoutDashboard,
      adminOnly: true,
    },
    {
      title: "Companies",
      href: "/admin/companies",
      icon: Building2,
      adminOnly: true,
    },
    {
      title: "Devices",
      href: "/admin/devices",
      icon: Smartphone,
      adminOnly: true,
    },
    {
      title: "Profile",
      href: "/admin/profile",
      icon: User,
      adminOnly: true,
    },
    {
      title: "Settings",
      href: "/admin/settings",
      icon: Settings,
      adminOnly: true,
    },
  ];

  const companyItems: NavItem[] = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
      companyOnly: true,
    },
    {
      title: "Devices",
      href: "/dashboard/devices",
      icon: Smartphone,
      companyOnly: true,
    },
    {
      title: "Profile",
      href: "/dashboard/profile",
      icon: User,
      companyOnly: true,
    },
    {
      title: "Settings",
      href: "/dashboard/settings",
      icon: Settings,
      companyOnly: true,
    },
  ];

  return userRole === "admin" ? adminItems : companyItems;
};

export function DashboardLayout({
  children,
  userRole,
  userEmail,
  userName,
}: DashboardLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { siteName } = useSiteName();
  const { logoUrl } = useBranding();

  // Fetch profile data based on user role
  const { data: adminProfile } = useAdminProfileQuery();
  const { data: companyProfile } = useCompanyProfileQuery();

  // Get the appropriate profile data
  const userAvatar =
    userRole === "admin" ? adminProfile?.avatar : companyProfile?.avatar;

  // Always call the hook to avoid conditional hook calls
  let contextLabels = null;
  try {
    const userTypeData = useUserType();
    contextLabels = userTypeData?.labels;
  } catch {
    // Context not available, use defaults
    contextLabels = null;
  }

  // Use user type context for company users, fallback for admin
  const labels = userRole === "company" ? contextLabels : null;

  const navItems = getNavItems(userRole, labels);
  const filteredNavItems = navItems.filter((item) => {
    if (userRole === "admin") return !item.companyOnly;
    if (userRole === "company") return !item.adminOnly;
    return false;
  });

  const handleLogout = async () => {
    try {
      const response = await fetch("/api/auth/logout", {
        method: "POST",
      });

      if (response.ok) {
        toast.success("Logged out successfully");
        router.push("/login");
      } else {
        toast.error("Logout failed");
      }
    } catch (error) {
      toast.error("An error occurred during logout");
    }
  };

  const SidebarContent = ({ mobile = false }: { mobile?: boolean }) => (
    <div className="flex h-full flex-col">
      {/* Logo */}
      <div className="flex h-16 items-center border-b px-6">
        <Link href="/" className="flex items-center space-x-2">
          <DynamicLogoWithFallback
            className="h-6 w-6"
            fallbackClassName="h-6 w-6 text-blue-600"
            alt="Logo"
          />
          {(!sidebarCollapsed || mobile) && (
            <span className="text-lg font-semibold">{siteName}</span>
          )}
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {filteredNavItems.map((item) => {
          const Icon = item.icon;
          const isActive = pathname === item.href;

          return (
            <Link
              key={item.href}
              href={item.href}
              onClick={() => mobile && setMobileMenuOpen(false)}
              className={cn(
                "flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                isActive
                  ? "bg-blue-100 text-blue-700"
                  : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
              )}
            >
              <Icon className="h-5 w-5 flex-shrink-0" />
              {(!sidebarCollapsed || mobile) && (
                <span className="ml-3">{item.title}</span>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Collapse button (desktop only) */}
      {!mobile && (
        <div className="border-t p-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="w-full justify-center"
          >
            {sidebarCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <>
                <ChevronLeft className="h-4 w-4 mr-2" />
                Collapse
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Desktop Sidebar */}
      <div
        className={cn(
          "hidden lg:flex lg:flex-col lg:border-r lg:bg-white lg:transition-all lg:duration-300",
          sidebarCollapsed ? "lg:w-16" : "lg:w-64"
        )}
      >
        <SidebarContent />
      </div>

      {/* Mobile Sidebar */}
      <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
        <SheetContent side="left" className="w-64 p-0">
          <VisuallyHidden>
            <SheetTitle>Navigation Menu</SheetTitle>
          </VisuallyHidden>
          <SidebarContent mobile />
        </SheetContent>
      </Sheet>

      {/* Main Content */}
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Top Navigation */}
        <header className="flex h-16 items-center justify-between border-b bg-white px-6">
          <div className="flex items-center space-x-4">
            {/* Mobile menu button */}
            <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="lg:hidden">
                  <Menu className="h-8 w-8" />
                </Button>
              </SheetTrigger>
            </Sheet>

            <h1 className="text-xl font-semibold text-gray-900">
              {userRole === "admin"
                ? "Admin Device Manager"
                : "Company Device Manager"}
            </h1>
          </div>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                <Avatar className="h-8 w-8">
                  {userAvatar && <AvatarImage src={userAvatar} alt="Profile" />}
                  <AvatarFallback>
                    {userName
                      ? userName.charAt(0).toUpperCase()
                      : userEmail?.charAt(0).toUpperCase() || "U"}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <div className="flex items-center justify-start gap-2 p-2">
                <div className="flex flex-col space-y-1 leading-none">
                  {userName && <p className="font-medium">{userName}</p>}
                  <p className="w-[200px] truncate text-sm text-muted-foreground">
                    {userEmail}
                  </p>
                </div>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link
                  href={
                    userRole === "admin"
                      ? "/admin/profile"
                      : "/dashboard/profile"
                  }
                >
                  <User className="mr-2 h-4 w-4" />
                  Profile
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link
                  href={
                    userRole === "admin"
                      ? "/admin/settings"
                      : "/dashboard/settings"
                  }
                >
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="mr-2 h-4 w-4" />
                Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto p-6">{children}</main>
      </div>
    </div>
  );
}
