"use client";

import * as React from "react";
import { forwardRef } from "react";
import ReactDatePicker from "react-datepicker";
import { ChevronDownIcon, CalendarIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

// Import CSS for react-datepicker
import "react-datepicker/dist/react-datepicker.css";

export interface DatePickerProps {
  value?: Date;
  onChange: (date: Date | undefined) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  dateFormat?: string;
  showTimeSelect?: boolean;
  timeFormat?: string;
  minDate?: Date;
  maxDate?: Date;
  excludeDates?: Date[];
  includeDates?: Date[];
  filterDate?: (date: Date) => boolean;
  showMonthDropdown?: boolean;
  showYearDropdown?: boolean;
  dropdownMode?: "scroll" | "select";
  autoComplete?: string;
  id?: string;
  name?: string;
  required?: boolean;
  readOnly?: boolean;
  tabIndex?: number;
  icon?: "calendar" | "chevron";
}

export interface DateRangePickerProps {
  startDate?: Date;
  endDate?: Date;
  onChange: (dates: [Date | null, Date | null]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  dateFormat?: string;
  showTimeSelect?: boolean;
  timeFormat?: string;
  minDate?: Date;
  maxDate?: Date;
  excludeDates?: Date[];
  includeDates?: Date[];
  filterDate?: (date: Date) => boolean;
  showMonthDropdown?: boolean;
  showYearDropdown?: boolean;
  dropdownMode?: "scroll" | "select";
  autoComplete?: string;
  id?: string;
  name?: string;
  required?: boolean;
  readOnly?: boolean;
  tabIndex?: number;
  selectsRange?: boolean;
  monthsShown?: number;
  icon?: "calendar" | "chevron";
}

// Custom input component for the date picker
const DatePickerInput = forwardRef<
  HTMLButtonElement,
  {
    value?: string;
    onClick?: () => void;
    placeholder?: string;
    className?: string;
    disabled?: boolean;
    icon?: "calendar" | "chevron";
  }
>(
  (
    { value, onClick, placeholder, className, disabled, icon = "chevron" },
    ref
  ) => {
    const IconComponent = icon === "calendar" ? CalendarIcon : ChevronDownIcon;

    return (
      <Button
        ref={ref}
        variant="outline"
        className={cn(
          "justify-between font-normal text-left",
          !value && "text-muted-foreground",
          className
        )}
        disabled={disabled}
        onClick={onClick}
        type="button"
      >
        <span className="truncate">
          {value || placeholder || "Select date"}
        </span>
        <IconComponent className="ml-2 h-4 w-4 opacity-50 shrink-0" />
      </Button>
    );
  }
);

DatePickerInput.displayName = "DatePickerInput";

// Single Date Picker Component
const DatePicker = forwardRef<HTMLDivElement, DatePickerProps>(
  (
    {
      value,
      onChange,
      placeholder = "Select date",
      className = "w-48",
      disabled = false,
      dateFormat = "MM/dd/yyyy",
      showTimeSelect = false,
      timeFormat = "HH:mm",
      minDate,
      maxDate,
      excludeDates,
      includeDates,
      filterDate,
      showMonthDropdown = true,
      showYearDropdown = true,
      dropdownMode = "select",
      autoComplete,
      id,
      name,
      required,
      readOnly,
      tabIndex,
      icon = "chevron",
    },
    ref
  ) => {
    const [open, setOpen] = React.useState(false);

    const formatDisplayValue = (date: Date | undefined) => {
      if (!date) return "";
      try {
        return format(date, dateFormat);
      } catch {
        return date.toLocaleDateString();
      }
    };

    return (
      <div ref={ref} className={className}>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <DatePickerInput
              value={formatDisplayValue(value)}
              onClick={() => !disabled && setOpen(true)}
              placeholder={placeholder}
              className="w-full"
              disabled={disabled}
              icon={icon}
            />
          </PopoverTrigger>
          <PopoverContent
            className="w-auto p-0 shadow-lg border"
            align="start"
            sideOffset={4}
          >
            <ReactDatePicker
              selected={value}
              onChange={(date) => {
                onChange(date || undefined);
                setOpen(false);
              }}
              dateFormat={dateFormat}
              showTimeSelect={showTimeSelect}
              timeFormat={timeFormat}
              minDate={minDate}
              maxDate={maxDate}
              excludeDates={excludeDates}
              includeDates={includeDates}
              filterDate={filterDate}
              showMonthDropdown={showMonthDropdown}
              showYearDropdown={showYearDropdown}
              dropdownMode={dropdownMode}
              autoComplete={autoComplete}
              id={id}
              name={name}
              required={required}
              readOnly={readOnly}
              tabIndex={tabIndex}
              inline
              calendarClassName="border-0 shadow-none"
            />
          </PopoverContent>
        </Popover>
      </div>
    );
  }
);

DatePicker.displayName = "DatePicker";

// Date Range Picker Component
export const DateRangePicker = forwardRef<HTMLDivElement, DateRangePickerProps>(
  (
    {
      startDate,
      endDate,
      onChange,
      placeholder = "Select date range",
      className = "w-64",
      disabled = false,
      dateFormat = "MM/dd/yyyy",
      showTimeSelect = false,
      timeFormat = "HH:mm",
      minDate,
      maxDate,
      excludeDates,
      includeDates,
      filterDate,
      showMonthDropdown = true,
      showYearDropdown = true,
      dropdownMode = "select",
      autoComplete,
      id,
      name,
      required,
      readOnly,
      tabIndex,
      monthsShown = 2,
      icon = "chevron",
    },
    ref
  ) => {
    const [open, setOpen] = React.useState(false);

    const formatDisplayValue = () => {
      if (!startDate && !endDate) return "";
      if (startDate && !endDate) {
        try {
          return format(startDate, dateFormat);
        } catch {
          return startDate.toLocaleDateString();
        }
      }
      if (startDate && endDate) {
        try {
          return `${format(startDate, dateFormat)} - ${format(
            endDate,
            dateFormat
          )}`;
        } catch {
          return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
        }
      }
      return "";
    };

    return (
      <div ref={ref} className={className}>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <DatePickerInput
              value={formatDisplayValue()}
              onClick={() => !disabled && setOpen(true)}
              placeholder={placeholder}
              className="w-full"
              disabled={disabled}
              icon={icon}
            />
          </PopoverTrigger>
          <PopoverContent
            className="w-auto p-0 shadow-lg border"
            align="start"
            sideOffset={4}
          >
            <ReactDatePicker
              selected={startDate}
              onChange={onChange}
              startDate={startDate}
              endDate={endDate}
              selectsRange
              dateFormat={dateFormat}
              showTimeSelect={showTimeSelect}
              timeFormat={timeFormat}
              minDate={minDate}
              maxDate={maxDate}
              excludeDates={excludeDates}
              includeDates={includeDates}
              filterDate={filterDate}
              showMonthDropdown={showMonthDropdown}
              showYearDropdown={showYearDropdown}
              dropdownMode={dropdownMode}
              autoComplete={autoComplete}
              id={id}
              name={name}
              required={required}
              readOnly={readOnly}
              tabIndex={tabIndex}
              monthsShown={monthsShown}
              inline
              calendarClassName="border-0 shadow-none"
            />
          </PopoverContent>
        </Popover>
      </div>
    );
  }
);

DateRangePicker.displayName = "DateRangePicker";

export default DatePicker;
