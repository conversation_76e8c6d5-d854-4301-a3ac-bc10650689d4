import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyPassword, createUserSession } from "@/lib/auth";
import { adminLoginSchema, companyLoginSchema } from "@/lib/validations/auth";
import { authenticator } from "otplib";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, ...credentials } = body;

    if (type === "admin") {
      // Admin login
      const validatedData = adminLoginSchema.parse(credentials);

      try {
        // Find admin in database
        const admin = await prisma.admin.findUnique({
          where: { email: validatedData.email },
          select: {
            id: true,
            name: true,
            email: true,
            passwordHash: true,
            loginCount: true,
            twoFactorEnabled: true,
            twoFactorSecret: true,
          },
        });

        if (!admin) {
          return NextResponse.json(
            { error: "Invalid credentials" },
            { status: 401 }
          );
        }

        // Verify password
        const isValidPassword = await verifyPassword(
          validatedData.password,
          admin.passwordHash
        );

        if (!isValidPassword) {
          return NextResponse.json(
            { error: "Invalid credentials" },
            { status: 401 }
          );
        }

        // Check if 2FA is enabled
        if (admin.twoFactorEnabled && admin.twoFactorSecret) {
          // If 2FA is enabled but no token provided, request 2FA token
          if (!validatedData.twoFactorToken) {
            return NextResponse.json(
              {
                requiresTwoFactor: true,
                message: "Two-factor authentication required",
              },
              { status: 200 }
            );
          }

          // Verify 2FA token
          const isValid2FA = authenticator.verify({
            token: validatedData.twoFactorToken,
            secret: admin.twoFactorSecret,
          });

          if (!isValid2FA) {
            return NextResponse.json(
              { error: "Invalid two-factor authentication code" },
              { status: 401 }
            );
          }
        }

        // Update login count and last login
        await prisma.admin.update({
          where: { id: admin.id },
          data: {
            loginCount: admin.loginCount + 1,
            lastLogin: new Date(),
          },
        });

        // Create session token
        const token = createUserSession({
          id: admin.id,
          email: admin.email,
          role: "admin",
        });

        const response = NextResponse.json({
          success: true,
          user: {
            id: admin.id,
            email: admin.email,
            role: "admin",
            name: admin.name,
          },
        });

        // Set HTTP-only cookie
        response.cookies.set("auth-token", token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: "lax",
          maxAge: 60 * 60 * 24 * 7, // 7 days
        });

        return response;
      } catch (dbError) {
        console.warn("Database not available for admin login:", dbError);

        // Fallback to hardcoded admin for demo purposes
        const ADMIN_EMAIL = "<EMAIL>";
        const ADMIN_PASSWORD_HASH =
          "$2b$12$onrewGzReGLpyN3ZKtj3mOfnlsJpw9ZzHvzd1qsjWAS6jyxWy3xrG"; // "admin123"

        if (validatedData.email !== ADMIN_EMAIL) {
          return NextResponse.json(
            { error: "Invalid credentials" },
            { status: 401 }
          );
        }

        const isValidPassword = await verifyPassword(
          validatedData.password,
          ADMIN_PASSWORD_HASH
        );

        if (!isValidPassword) {
          return NextResponse.json(
            { error: "Invalid credentials" },
            { status: 401 }
          );
        }

        // Create session token
        const token = createUserSession({
          id: "admin",
          email: ADMIN_EMAIL,
          role: "admin",
        });

        const response = NextResponse.json({
          success: true,
          user: {
            id: "admin",
            email: ADMIN_EMAIL,
            role: "admin",
            name: "System Administrator",
          },
        });

        // Set HTTP-only cookie
        response.cookies.set("auth-token", token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: "lax",
          maxAge: 60 * 60 * 24 * 7, // 7 days
        });

        return response;
      }
    } else if (type === "company") {
      // Company login
      const validatedData = companyLoginSchema.parse(credentials);

      // Find company by matching the plain login token
      const company = await prisma.company.findFirst({
        where: {
          loginToken: validatedData.loginToken,
        },
      });

      if (!company) {
        return NextResponse.json(
          { error: "Invalid login token" },
          { status: 401 }
        );
      }

      // Check if company is active
      if (company.status !== "ACTIVE") {
        return NextResponse.json(
          { error: "Account is deactivated" },
          { status: 401 }
        );
      }

      // Check if company has expired
      if (company.expiresAt < new Date()) {
        return NextResponse.json(
          { error: "Account has expired" },
          { status: 401 }
        );
      }

      // Update login count and last login
      await prisma.company.update({
        where: { id: company.id },
        data: {
          loginCount: company.loginCount + 1,
          lastLogin: new Date(),
        },
      });

      // Create session token
      const token = createUserSession({
        id: company.id,
        email: company.email,
        role: "company",
        companyId: company.id,
      });

      const response = NextResponse.json({
        success: true,
        user: {
          id: company.id,
          email: company.email,
          role: "company",
          companyId: company.id,
        },
      });

      // Set HTTP-only cookie
      response.cookies.set("auth-token", token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 60 * 60 * 24 * 7, // 7 days
      });

      return response;
    } else {
      return NextResponse.json(
        { error: "Invalid login type" },
        { status: 400 }
      );
    }
  } catch (error) {
    if (error instanceof Error && error.name === "ZodError") {
      return NextResponse.json(
        { error: "Invalid input data" },
        { status: 400 }
      );
    }

    console.error("Login API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
