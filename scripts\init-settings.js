const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

async function initializeSettings() {
  console.log("🔧 Initializing default settings...");

  try {
    // Check if admin settings already exist
    const existingAdminSettings = await prisma.adminSettings.findFirst();

    if (!existingAdminSettings) {
      console.log("📋 Creating default admin settings...");
      await prisma.adminSettings.create({
        data: {
          siteName: "Smart Attendance Portal",
          siteDescription: "Advanced attendance management system",
          defaultTimeZoneId: 57, // Asia/Kolkata
          dateFormat: "MM/DD/YYYY",
          logoUrl: "",
          faviconUrl: "",
          language: "en",
          maintenanceMode: false,
          sessionTimeout: 480,
          maxLoginAttempts: 5,
          passwordMinLength: 8,
          requireTwoFactor: false,
          allowPasswordReset: true,
          ipWhitelist: "[]",
          emailNotifications: true,
          deviceOfflineAlerts: true,
          companyExpiryAlerts: true,
          systemMaintenanceAlerts: true,
          adminEmail: "<EMAIL>",
          deviceOfflineMinutes: 30,
          companyExpiryDays: 7,
          autoSyncInterval: 60,
          maxDevicesPerCompany: 10,
          defaultDeviceTimeout: 300,
          allowBulkOperations: true,
          requireDeviceApproval: false,
          autoBackup: true,
          backupFrequency: "daily",
          retentionDays: 30,
          backupLocation: "/backups",
        },
      });
      console.log("✅ Admin settings created successfully");
    } else {
      console.log("ℹ️  Admin settings already exist");
    }

    // Check if any companies need default settings
    const companies = await prisma.company.findMany({
      include: {
        settings: true,
      },
    });

    for (const company of companies) {
      if (!company.settings) {
        console.log(
          `📋 Creating default settings for company: ${company.name}`
        );
        await prisma.companySettings.create({
          data: {
            companyId: company.id,
            companyName: company.name,
            timezone: "UTC",
            dateFormat: "MM/DD/YYYY",
            workingHoursStart: "09:00",
            workingHoursEnd: "17:00",
            workingDays: "Monday,Tuesday,Wednesday,Thursday,Friday",
            emailAlerts: true,
            attendanceReminders: true,
            lateArrivalAlerts: true,
            absenteeAlerts: true,
            deviceOfflineAlerts: true,
            weeklyReports: true,
            monthlyReports: false,
            graceTime: 15,
            autoClockOut: false,
            autoClockOutTime: "18:00",
            allowManualEntry: false,
            requireApproval: true,
            trackBreaks: false,
            maxWorkingHours: 12,
            allowMultipleCheckIns: false,
            deviceSyncInterval: 30,
            offlineDataRetention: 7,
            faceRecognitionThreshold: 80,
            fingerprintThreshold: 85,
          },
        });
        console.log(`✅ Settings created for company: ${company.name}`);
      }
    }

    console.log("🎉 Settings initialization completed!");
  } catch (error) {
    console.error("❌ Error initializing settings:", error);
  } finally {
    await prisma.$disconnect();
  }
}

initializeSettings();
