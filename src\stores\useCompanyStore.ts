import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface CompanyFilters {
  search: string;
  status?: string;
  userType?: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export interface CompanyPagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

export interface Company {
  id: string;
  name: string;
  organizationId: string;
  email: string;
  userType: string;
  status: string;
  expiresAt: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  loginToken?: string;
  allocations: Array<{ deviceSerialNo: string }>;
}

interface CompanyStoreState {
  // UI State
  filters: CompanyFilters;
  pagination: CompanyPagination;
  selectedCompanies: Company[];
  
  // UI Actions
  setSearch: (search: string) => void;
  setStatus: (status?: string) => void;
  setUserType: (userType?: string) => void;
  setSorting: (sortBy: string, sortOrder?: 'asc' | 'desc') => void;
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;
  setPagination: (pagination: Partial<CompanyPagination>) => void;
  setSelectedCompanies: (companies: Company[]) => void;
  toggleCompanySelection: (company: Company) => void;
  selectAllCompanies: (companies: Company[]) => void;
  clearSelection: () => void;
  resetFilters: () => void;
}

const initialFilters: CompanyFilters = {
  search: '',
  status: undefined,
  userType: undefined,
  sortBy: 'name',
  sortOrder: 'asc',
};

const initialPagination: CompanyPagination = {
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
};

export const useCompanyStore = create<CompanyStoreState>()(
  devtools(
    (set) => ({
      // Initial state
      filters: initialFilters,
      pagination: initialPagination,
      selectedCompanies: [],

      // Filter actions
      setSearch: (search: string) =>
        set(
          (state) => ({
            filters: { ...state.filters, search },
            pagination: { ...state.pagination, page: 1 }, // Reset to first page on search
          }),
          false,
          'setSearch'
        ),

      setStatus: (status?: string) =>
        set(
          (state) => ({
            filters: { ...state.filters, status },
            pagination: { ...state.pagination, page: 1 },
          }),
          false,
          'setStatus'
        ),

      setUserType: (userType?: string) =>
        set(
          (state) => ({
            filters: { ...state.filters, userType },
            pagination: { ...state.pagination, page: 1 },
          }),
          false,
          'setUserType'
        ),

      setSorting: (sortBy: string, sortOrder?: 'asc' | 'desc') => {
        set(
          (state) => {
            const currentFilters = state.filters;
            const newSortOrder = sortOrder || (currentFilters.sortBy === sortBy && currentFilters.sortOrder === 'asc' ? 'desc' : 'asc');

            return {
              filters: { ...currentFilters, sortBy, sortOrder: newSortOrder },
              pagination: { ...state.pagination, page: 1 },
            };
          },
          false,
          'setSorting'
        );
      },

      // Pagination actions
      setPage: (page: number) =>
        set(
          (state) => ({
            pagination: { ...state.pagination, page },
          }),
          false,
          'setPage'
        ),

      setPageSize: (pageSize: number) =>
        set(
          (state) => ({
            pagination: { ...state.pagination, pageSize, page: 1 },
          }),
          false,
          'setPageSize'
        ),

      setPagination: (pagination: Partial<CompanyPagination>) =>
        set(
          (state) => ({
            pagination: { ...state.pagination, ...pagination },
          }),
          false,
          'setPagination'
        ),

      // Selection actions
      setSelectedCompanies: (companies: Company[]) =>
        set({ selectedCompanies: companies }, false, 'setSelectedCompanies'),

      toggleCompanySelection: (company: Company) =>
        set(
          (state) => {
            const isSelected = state.selectedCompanies.some((c) => c.id === company.id);
            const selectedCompanies = isSelected
              ? state.selectedCompanies.filter((c) => c.id !== company.id)
              : [...state.selectedCompanies, company];
            return { selectedCompanies };
          },
          false,
          'toggleCompanySelection'
        ),

      selectAllCompanies: (companies: Company[]) =>
        set({ selectedCompanies: companies }, false, 'selectAllCompanies'),

      clearSelection: () =>
        set({ selectedCompanies: [] }, false, 'clearSelection'),

      resetFilters: () =>
        set(
          {
            filters: initialFilters,
            pagination: initialPagination,
            selectedCompanies: [],
          },
          false,
          'resetFilters'
        ),
    }),
    {
      name: 'company-store',
    }
  )
);
