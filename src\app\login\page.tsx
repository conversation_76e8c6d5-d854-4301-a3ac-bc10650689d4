"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Shield, Eye, EyeOff } from "lucide-react";
import { toast } from "react-toastify";
import { useSiteName } from "@/hooks/useSiteName";
import { DynamicLogoWithFallback } from "@/components/shared/dynamic-logo";

export default function LoginPage() {
  const router = useRouter();
  const { siteName } = useSiteName();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [requiresTwoFactor, setRequiresTwoFactor] = useState(false);

  // Admin login form
  const [adminForm, setAdminForm] = useState({
    email: "",
    password: "",
    twoFactorToken: "",
  });

  // Company login form
  const [companyForm, setCompanyForm] = useState({
    loginToken: "",
  });

  const handleAdminLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const loginData: any = {
        type: "admin",
        email: adminForm.email,
        password: adminForm.password,
      };

      // Include 2FA token if we're in 2FA mode
      if (requiresTwoFactor && adminForm.twoFactorToken) {
        loginData.twoFactorToken = adminForm.twoFactorToken;
      }

      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(loginData),
      });

      const data = await response.json();

      if (response.ok) {
        if (data.requiresTwoFactor) {
          // 2FA is required, show 2FA input
          setRequiresTwoFactor(true);
          toast.info("Please enter your two-factor authentication code");
        } else {
          // Login successful
          toast.success("Login successful!");
          router.push("/admin/dashboard");
        }
      } else {
        toast.error(data.error || "Login failed");
      }
    } catch (error) {
      toast.error("An error occurred during login");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCompanyLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "company",
          ...companyForm,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Login successful!");
        router.push("/dashboard");
      } else {
        toast.error(data.error || "Login failed");
      }
    } catch (error) {
      toast.error("An error occurred during login");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2 mb-4">
            <DynamicLogoWithFallback
              className="h-8 w-8"
              fallbackClassName="h-8 w-8 text-blue-600"
              alt="Logo"
            />
            <span className="text-2xl font-bold text-gray-900">{siteName}</span>
          </Link>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome Back
          </h1>
          <p className="text-gray-600">Sign in to your account to continue</p>
        </div>

        <Card className="shadow-lg">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">Sign In</CardTitle>
            <CardDescription className="text-center">
              Choose your account type to continue
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="company" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="admin">Admin</TabsTrigger>
                <TabsTrigger value="company">Company</TabsTrigger>
              </TabsList>

              <TabsContent value="admin" className="space-y-4">
                <form onSubmit={handleAdminLogin} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="admin-email">Email</Label>
                    <Input
                      id="admin-email"
                      type="email"
                      placeholder="Enter admin email"
                      value={adminForm.email}
                      onChange={(e) =>
                        setAdminForm({ ...adminForm, email: e.target.value })
                      }
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="admin-password">Password</Label>
                    <div className="relative">
                      <Input
                        id="admin-password"
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter admin password"
                        value={adminForm.password}
                        onChange={(e) =>
                          setAdminForm({
                            ...adminForm,
                            password: e.target.value,
                          })
                        }
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  {requiresTwoFactor && (
                    <div className="space-y-2">
                      <Label htmlFor="admin-2fa">
                        Two-Factor Authentication Code
                      </Label>
                      <Input
                        id="admin-2fa"
                        type="text"
                        placeholder="Enter 6-digit code from your authenticator app"
                        value={adminForm.twoFactorToken}
                        onChange={(e) =>
                          setAdminForm({
                            ...adminForm,
                            twoFactorToken: e.target.value,
                          })
                        }
                        maxLength={6}
                        pattern="[0-9]{6}"
                        required={requiresTwoFactor}
                        autoComplete="one-time-code"
                      />
                      <p className="text-sm text-gray-600">
                        Enter the 6-digit code from your authenticator app
                      </p>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setRequiresTwoFactor(false);
                          setAdminForm({ ...adminForm, twoFactorToken: "" });
                        }}
                        className="mt-2"
                      >
                        ← Back to Password
                      </Button>
                    </div>
                  )}

                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading
                      ? "Signing in..."
                      : requiresTwoFactor
                      ? "Verify & Sign In"
                      : "Sign In as Admin"}
                  </Button>
                </form>
              </TabsContent>

              <TabsContent value="company" className="space-y-4">
                <form onSubmit={handleCompanyLogin} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="login-token">Login Token</Label>
                    <Input
                      id="login-token"
                      type="text"
                      placeholder="Enter your login token"
                      value={companyForm.loginToken}
                      onChange={(e) =>
                        setCompanyForm({
                          ...companyForm,
                          loginToken: e.target.value,
                        })
                      }
                      required
                    />
                  </div>
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? "Signing in..." : "Sign In as Company"}
                  </Button>
                </form>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        <div className="text-center mt-6">
          <Link href="/" className="text-sm text-gray-600 hover:text-gray-900">
            ← Back to Home
          </Link>
        </div>
      </div>
    </div>
  );
}
