import { NextRequest, NextResponse } from "next/server";
import { externalDeviceAPI } from "@/lib/api/external-device-api";
import { verifyAdminAuth } from "@/lib/auth";

// POST /api/admin/devices/[deviceId]/users/bulk - Bulk user operations
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const deviceSerial = resolvedParams.deviceId;
    const body = await request.json();
    const { operation, users } = body;

    // Validate required fields
    if (!operation || !users || !Array.isArray(users)) {
      return NextResponse.json(
        {
          error: "Operation and users array are required",
        },
        { status: 400 }
      );
    }

    if (users.length === 0) {
      return NextResponse.json(
        {
          error: "At least one user is required",
        },
        { status: 400 }
      );
    }

    console.log(
      `Starting bulk ${operation} operation for ${users.length} users on device ${deviceSerial}`
    );

    const results = [];
    let successCount = 0;
    let failureCount = 0;

    // Process each user based on operation type
    for (const user of users) {
      try {
        let response;
        let operationDescription = "";

        switch (operation) {
          case "add":
            if (!user.enrollmentNo || !user.name) {
              throw new Error(
                "Enrollment number and name are required for add operation"
              );
            }

            response = await externalDeviceAPI.addUser({
              DEVICESLNO: deviceSerial,
              USERID: user.enrollmentNo,
              USERNAME: user.name,
              FINGERDATA: user.fingerData,
              FACEDATA: user.faceData,
              CARDDATA: user.cardData,
              PASSWORDDATA: user.passwordData,
              ADMIN: user.isAdmin ? "1" : "0",
            });
            operationDescription = `add user ${user.enrollmentNo}`;
            break;

          case "delete":
            if (!user.enrollmentNo) {
              throw new Error(
                "Enrollment number is required for delete operation"
              );
            }

            response = await externalDeviceAPI.deleteUser(
              deviceSerial,
              user.enrollmentNo
            );
            operationDescription = `delete user ${user.enrollmentNo}`;
            break;

          case "enable":
            if (!user.enrollmentNo) {
              throw new Error(
                "Enrollment number is required for enable operation"
              );
            }

            response = await externalDeviceAPI.enableUser([
              {
                DEVICESLNO: deviceSerial,
                USERID: user.enrollmentNo,
                STATUS: "ENABLE",
              },
            ]);
            operationDescription = `enable user ${user.enrollmentNo}`;
            break;

          case "disable":
            if (!user.enrollmentNo) {
              throw new Error(
                "Enrollment number is required for disable operation"
              );
            }

            response = await externalDeviceAPI.enableUser([
              {
                DEVICESLNO: deviceSerial,
                USERID: user.enrollmentNo,
                STATUS: "DISABLE",
              },
            ]);
            operationDescription = `disable user ${user.enrollmentNo}`;
            break;

          case "clear_all":
            // Clear all users from device
            response = await externalDeviceAPI.clearAllUser(deviceSerial);
            operationDescription = "clear all users";
            break;

          default:
            throw new Error(`Unknown operation: ${operation}`);
        }

        if (response.status === "200") {
          results.push({
            user: user.enrollmentNo || "all",
            status: "success",
            message:
              response.msg || `Successfully completed ${operationDescription}`,
          });
          successCount++;
          console.log(`✅ Successfully completed ${operationDescription}`);
        } else {
          results.push({
            user: user.enrollmentNo || "all",
            status: "error",
            message: response.msg || `Failed to ${operationDescription}`,
          });
          failureCount++;
          console.log(`❌ Failed to ${operationDescription}: ${response.msg}`);
        }

        // Add a small delay between operations to avoid overwhelming the device
        await new Promise((resolve) => setTimeout(resolve, 500));
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        results.push({
          user: user.enrollmentNo || "unknown",
          status: "error",
          message: errorMessage,
        });
        failureCount++;
        console.log(`❌ Error processing user ${user.enrollmentNo}:`, error);
      }
    }

    const summary = {
      operation,
      deviceSerial,
      total: operation === "clear_all" ? 1 : users.length,
      successful: successCount,
      failed: failureCount,
      completedAt: new Date().toISOString(),
    };

    console.log("Bulk operation completed:", summary);

    return NextResponse.json({
      success: failureCount === 0,
      message:
        failureCount === 0
          ? `Successfully completed ${operation} operation for all users`
          : `Bulk operation completed with ${successCount} successes and ${failureCount} failures`,
      data: {
        summary,
        results,
      },
    });
  } catch (error) {
    console.error("Error in bulk user operation:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}

// GET /api/admin/devices/[deviceId]/users/bulk - Get bulk operation status
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const deviceSerial = resolvedParams.deviceId;

    return NextResponse.json({
      success: true,
      data: {
        deviceSerial,
        availableOperations: [
          {
            operation: "add",
            description: "Add multiple users to device",
            requiredFields: ["enrollmentNo", "name"],
            optionalFields: [
              "isAdmin",
              "fingerData",
              "faceData",
              "cardData",
              "passwordData",
            ],
          },
          {
            operation: "delete",
            description: "Delete multiple users from device",
            requiredFields: ["enrollmentNo"],
          },
          {
            operation: "enable",
            description: "Enable multiple users on device",
            requiredFields: ["enrollmentNo"],
          },
          {
            operation: "disable",
            description: "Disable multiple users on device",
            requiredFields: ["enrollmentNo"],
          },
          {
            operation: "clear_all",
            description: "Clear all users from device",
            requiredFields: [],
          },
        ],
        status: "ready",
      },
    });
  } catch (error) {
    console.error("Error getting bulk operation status:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}
