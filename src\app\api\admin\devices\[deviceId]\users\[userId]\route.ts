import { NextRequest, NextResponse } from "next/server";
import { externalDeviceAPI } from "@/lib/api/external-device-api";
import { verifyAdminAuth } from "@/lib/auth";

// Transform external API user data to our internal format
function transformUserData(apiUser: any) {
  return {
    id: apiUser.RowId?.toString() || apiUser.EnrollmentNo,
    enrollmentNo: apiUser.EnrollmentNo,
    name: apiUser.Name || `User ${apiUser.EnrollmentNo}`,
    deviceSerial: apiUser.DevicesSlno,
    isAdmin: apiUser.Admin === 1,
    biometrics: {
      hasFace:
        typeof apiUser.FaceData === "string" && apiUser.FaceData.length > 0,
      hasFingerprint:
        apiUser.FingerData && Object.keys(apiUser.FingerData).length > 0,
      hasCard: apiUser.Card && Object.keys(apiUser.Card).length > 0,
      hasPassword:
        (typeof apiUser.PWD === "string" && apiUser.PWD !== "0") ||
        (typeof apiUser.PWD === "object" &&
          Object.keys(apiUser.PWD).length > 0),
    },
    isEnabled: apiUser.Enabled && Object.keys(apiUser.Enabled).length > 0,
    privilege: apiUser.Privilege,
    faceData: apiUser.FaceData, // Include raw face data for editing
    fingerData: apiUser.FingerData,
    cardData: apiUser.Card,
    passwordData: apiUser.PWD,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
}

// GET /api/admin/devices/[deviceId]/users/[userId] - Get specific user data
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string; userId: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const deviceSerial = resolvedParams.deviceId;
    const userId = resolvedParams.userId;

    console.log(`Getting user ${userId} data from device ${deviceSerial}`);

    // Get single user data from external API
    const response = await externalDeviceAPI.getSingleUserData(
      deviceSerial,
      userId
    );

    if (response.status !== "200") {
      return NextResponse.json(
        {
          error: response.msg || "Failed to retrieve user data",
        },
        { status: 400 }
      );
    }

    // Check if user was found
    if (!response.data || response.data.length === 0) {
      return NextResponse.json(
        {
          error: "User not found on device",
        },
        { status: 404 }
      );
    }

    // Transform the user data
    const userData = transformUserData(response.data[0]);

    console.log(
      `Successfully retrieved user ${userId} from device ${deviceSerial}`
    );

    return NextResponse.json({
      success: true,
      data: userData,
    });
  } catch (error) {
    console.error("Error retrieving user data:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}

// PUT /api/admin/devices/[deviceId]/users/[userId] - Update user data
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string; userId: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const deviceSerial = resolvedParams.deviceId;
    const userId = resolvedParams.userId;
    const body = await request.json();

    const { name, isAdmin, fingerData, faceData, cardData, passwordData } =
      body;

    console.log(`Updating user ${userId} on device ${deviceSerial}`);

    // First delete the existing user
    const deleteResponse = await externalDeviceAPI.deleteUser(
      deviceSerial,
      userId
    );

    if (deleteResponse.status !== "200") {
      return NextResponse.json(
        {
          error: `Failed to delete existing user: ${deleteResponse.msg}`,
        },
        { status: 400 }
      );
    }

    // Add a small delay to ensure deletion is processed
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Then add the user with updated data
    const addResponse = await externalDeviceAPI.addUser({
      DEVICESLNO: deviceSerial,
      USERID: userId,
      USERNAME: name || `User ${userId}`,
      FINGERDATA: fingerData,
      FACEDATA: faceData,
      CARDDATA: cardData,
      PASSWORDDATA: passwordData,
      ADMIN: isAdmin ? "1" : "0",
    });

    if (addResponse.status !== "200") {
      return NextResponse.json(
        {
          error: `Failed to add updated user: ${addResponse.msg}`,
        },
        { status: 400 }
      );
    }

    console.log(
      `Successfully updated user ${userId} on device ${deviceSerial}`
    );

    return NextResponse.json({
      success: true,
      message: "User updated successfully",
      data: {
        enrollmentNo: userId,
        name,
        deviceSerial,
        isAdmin,
      },
    });
  } catch (error) {
    console.error("Error updating user:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/devices/[deviceId]/users/[userId] - Delete specific user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string; userId: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const deviceSerial = resolvedParams.deviceId;
    const userId = resolvedParams.userId;

    console.log(`Deleting user ${userId} from device ${deviceSerial}`);

    // Delete user from device via external API
    const response = await externalDeviceAPI.deleteUser(deviceSerial, userId);

    if (response.status !== "200") {
      return NextResponse.json(
        {
          error: response.msg || "Failed to delete user",
        },
        { status: 400 }
      );
    }

    console.log(
      `Successfully deleted user ${userId} from device ${deviceSerial}`
    );

    return NextResponse.json({
      success: true,
      message: "User deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting user:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}
