import { NextRequest, NextResponse } from "next/server";
import { verifyJWT } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET /api/admin/companies/with-tokens - List companies with login tokens (admin only)
export async function GET(req: NextRequest) {
  try {
    // Authentication check
    const token = req.cookies.get("auth-token")?.value;
    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const user = verifyJWT(token);
    if (!user || user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const query = searchParams.get("query") || "";
    const status = searchParams.get("status");
    const userType = searchParams.get("userType");
    const sortBy = searchParams.get("sortBy") || "name";
    const sortOrder = searchParams.get("sortOrder") || "asc";

    const skip = (page - 1) * pageSize;

    try {
      // Build where clause
      const where: any = {};

      if (query) {
        where.OR = [
          { name: { contains: query, mode: "insensitive" } },
          { email: { contains: query, mode: "insensitive" } },
          { organizationId: { contains: query, mode: "insensitive" } },
        ];
      }

      if (status) {
        where.status = status;
      }

      if (userType) {
        where.userType = userType;
      }

      // Get companies with pagination and login tokens
      const [companies, total] = await Promise.all([
        prisma.company.findMany({
          where,
          skip,
          take: pageSize,
          orderBy: (() => {
            switch (sortBy) {
              case "name":
                return { name: sortOrder as "asc" | "desc" };
              case "email":
                return { email: sortOrder as "asc" | "desc" };
              case "status":
                return { status: sortOrder as "asc" | "desc" };
              case "expiresAt":
                return { expiresAt: sortOrder as "asc" | "desc" };
              case "createdAt":
                return { createdAt: sortOrder as "asc" | "desc" };
              default:
                return { name: sortOrder as "asc" | "desc" };
            }
          })(),
          select: {
            id: true,
            name: true,
            organizationId: true,
            email: true,
            userType: true,
            status: true,
            expiresAt: true,
            description: true,
            createdAt: true,
            updatedAt: true,
            loginTokenHash: true,
            loginToken: true, // Include the plain login token
            allocations: {
              select: {
                deviceSerialNo: true,
              },
            },
          },
        }),
        prisma.company.count({ where }),
      ]);

      // Return companies with their actual login tokens
      const companiesWithTokens = companies.map((company) => ({
        ...company,
        // loginToken is already included from the database select
      }));

      return NextResponse.json({
        success: true,
        data: companiesWithTokens,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
      });
    } catch (dbError) {
      console.error("Database connection failed:", dbError);

      return NextResponse.json(
        {
          error:
            "Database connection failed. Please ensure PostgreSQL is running.",
          details: "Cannot fetch companies without database connection.",
        },
        { status: 503 }
      );
    }
  } catch (error) {
    console.error("Get companies with tokens error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
