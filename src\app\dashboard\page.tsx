"use client";

import React from "react";
import { DashboardLayout } from "@/components/shared/dashboard-layout";
import { AdminReturnBanner } from "@/components/shared/admin-return-banner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Building2,
  Users,
  Clock,
  Settings,
  Activity,
  TrendingUp,
  Calendar,
  CalendarDays,
  Smartphone,
  Zap,
} from "lucide-react";
import { UserTypeProvider, useUserType } from "@/contexts/user-type-context";
import {
  useCompanyStatsQuery,
  useCompanyActivityQuery,
} from "@/hooks/useOptimizedQueries";

import { RecentActivity } from "@/components/features/dashboard/recent-activity";
import { useCompanyProfileForLayout } from "@/hooks/useCompanyProfileForLayout";
import { useCompanyProfileQuery } from "@/hooks/queries/useProfileQueries";

interface CompanyStats {
  totalEmployees: number;
  presentToday: number;
  totalDevices: number;
  activeDevices: number;
  attendanceRate: number;
  lastSyncTime: string;
}

// Dynamic dashboard component that uses user type context
function DynamicCompanyDashboard() {
  const { labels, userType } = useUserType();
  const { userEmail, userName } = useCompanyProfileForLayout();

  // Use optimized queries without Zustand store integration

  const {
    data: stats,
    isLoading: statsLoading,
    error: statsError,
  } = useCompanyStatsQuery();

  const {
    data: activities,
    isLoading: activitiesLoading,
    error: activitiesError,
  } = useCompanyActivityQuery();

  const { data: profile } = useCompanyProfileQuery();

  // No Zustand store integration needed - just use the query data directly

  // Helper function to format dates
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Helper function to calculate days remaining
  const getDaysRemaining = (expiryDate: string) => {
    const now = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const statCards = [
    {
      title: "Joined Date",
      value: profile ? formatDate(profile.createdAt) : "Loading...",
      description: "Company registration date",
      icon: Calendar,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Expiry Date",
      value: profile ? formatDate(profile.expiresAt) : "Loading...",
      description: profile
        ? `${getDaysRemaining(profile.expiresAt)} days remaining`
        : "Loading...",
      icon: CalendarDays,
      color:
        getDaysRemaining(profile?.expiresAt || "") < 30
          ? "text-red-600"
          : "text-green-600",
      bgColor:
        getDaysRemaining(profile?.expiresAt || "") < 30
          ? "bg-red-100"
          : "bg-green-100",
    },
    {
      title: "Total Allocated Devices",
      value: stats?.totalDevices || 0,
      description: "Devices assigned to company",
      icon: Smartphone,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Active Devices",
      value: stats?.activeDevices || 0,
      description: "Currently online devices",
      icon: Zap,
      color: "text-indigo-600",
      bgColor: "bg-indigo-100",
    },
  ];

  return (
    <>
      <AdminReturnBanner />
      <DashboardLayout
        userRole="company"
        userEmail={userEmail}
        userName={userName}
      >
        <div className="space-y-6">
          {/* Company Welcome Banner */}
          {profile && (
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold mb-2">
                    Welcome to {profile.name}
                  </h1>
                  <p className="text-blue-100">
                    {userType === "SCHOOL_MANAGEMENT"
                      ? "School Management Portal"
                      : userType === "EMPLOYEE_MANAGEMENT"
                      ? "HR Management Portal"
                      : "Company Management Portal"}
                  </p>
                </div>
                <div className="text-right">
                  <div className="bg-white/20 rounded-lg p-3">
                    <Building2 className="h-8 w-8 mb-1" />
                    <p className="text-sm font-medium">Organization</p>
                    <p className="text-xs text-blue-100">
                      {profile.organizationId}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="mb-8">
            <h2 className="text-3xl font-bold text-gray-900">
              {userType === "SCHOOL_MANAGEMENT"
                ? "School Dashboard"
                : userType === "EMPLOYEE_MANAGEMENT"
                ? "HR Dashboard"
                : "Company Dashboard"}
            </h2>
            <p className="text-gray-600 mt-2">
              Manage your {labels.attendanceLabel.toLowerCase()} and track
              performance
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {statsLoading
              ? // Loading skeleton
                Array.from({ length: 4 }).map((_, index) => (
                  <Card key={index} className="animate-pulse">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <div className="h-4 bg-gray-200 rounded w-24"></div>
                      <div className="h-4 w-4 bg-gray-200 rounded"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-20"></div>
                    </CardContent>
                  </Card>
                ))
              : statCards.map((card, index) => {
                  const Icon = card.icon;
                  return (
                    <Card
                      key={index}
                      className="hover:shadow-lg transition-shadow"
                    >
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-gray-600">
                          {card.title}
                        </CardTitle>
                        <div className={`p-2 rounded-lg ${card.bgColor}`}>
                          <Icon className={`h-4 w-4 ${card.color}`} />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-gray-900">
                          {typeof card.value === "number"
                            ? card.value.toLocaleString()
                            : card.value}
                        </div>
                        <p className="text-xs text-gray-600 mt-1">
                          {card.description}
                        </p>
                      </CardContent>
                    </Card>
                  );
                })}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <RecentActivity
              activities={activities || []}
              loading={activitiesLoading}
              error={activitiesError?.message}
              title="Recent Activity"
              description="Latest attendance records and system events"
            />

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common tasks and system management
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Device Management</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      Monitor and configure your allocated attendance devices
                    </p>
                    <button
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      onClick={() =>
                        (window.location.href = "/dashboard/devices")
                      }
                    >
                      Manage Devices →
                    </button>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Profile Settings</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      Update your profile information and account settings
                    </p>
                    <button
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      onClick={() =>
                        (window.location.href = "/dashboard/profile")
                      }
                    >
                      View Profile →
                    </button>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">System Settings</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      Configure system preferences and security settings
                    </p>
                    <button
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      onClick={() =>
                        (window.location.href = "/dashboard/settings")
                      }
                    >
                      System Settings →
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DashboardLayout>
    </>
  );
}

// Main component with UserTypeProvider
export default function CompanyDashboard() {
  // TODO: Get user type from company data/session
  // For now, we'll use API_USER as default, but this should come from the logged-in company's user_type
  const userType = "API_USER"; // This should be dynamic based on the company's user_type

  return (
    <UserTypeProvider initialUserType={userType}>
      <DynamicCompanyDashboard />
    </UserTypeProvider>
  );
}
