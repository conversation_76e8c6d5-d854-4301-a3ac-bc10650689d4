import { NextRequest, NextResponse } from 'next/server';
import { withCompanyAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { externalDeviceAPI } from '@/lib/api/external-device-api';
import { prisma } from '@/lib/prisma';

// GET /api/dashboard/devices/[deviceId]/status - Get single device status for company users
async function getDeviceStatus(
  req: AuthenticatedRequest
) {
  try {
    const user = req.user!;

    // Extract deviceId from URL path
    const url = new URL(req.url!);
    const pathParts = url.pathname.split('/');
    const deviceId = pathParts[pathParts.length - 2]; // Get deviceId from path
    const deviceSerial = deviceId;

    // Check if device is allocated to this company
    const allocation = await prisma.allocation.findFirst({
      where: {
        deviceSerialNo: deviceSerial,
        companyId: user.companyId
      }
    });

    if (!allocation) {
      return NextResponse.json({
        error: 'Device not allocated to your company'
      }, { status: 403 });
    }

    console.log(`Company ${user.companyId} checking status for device: ${deviceSerial}`);

    try {
      // Check device status using external API
      const response = await externalDeviceAPI.getDeviceOnlineStatus(deviceSerial);

      // Determine status based on response
      let status = 'UNKNOWN';
      if (response.status === '200') {
        status = 'ONLINE';
      } else if (response.status === '300') {
        status = 'OFFLINE';
      }

      console.log(`Device ${deviceSerial} status: ${status} (API response: ${response.status})`);

      return NextResponse.json({
        success: true,
        status,
        message: response.msg || 'Status checked successfully',
        timestamp: Date.now(),
        deviceSerial
      });

    } catch (error) {
      console.error(`Error checking status for device ${deviceSerial}:`, error);
      return NextResponse.json({
        success: false,
        status: 'UNKNOWN',
        message: 'Failed to check device status',
        timestamp: Date.now(),
        deviceSerial
      });
    }

  } catch (error) {
    console.error('Error in device status check:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export const GET = withCompanyAuth(getDeviceStatus);
