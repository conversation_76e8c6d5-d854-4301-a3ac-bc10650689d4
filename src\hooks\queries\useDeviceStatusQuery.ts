import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "@/lib/queryKeys";
import { DeviceStatus } from "@/stores/useDeviceStore";

interface DeviceStatusResponse {
  success: boolean;
  statusMap: Record<string, DeviceStatus>;
  results: Array<{
    serialNumber: string;
    status: DeviceStatus;
    message: string;
    timestamp?: number;
  }>;
}

interface SingleDeviceStatusResponse {
  success: boolean;
  status: DeviceStatus;
  message: string;
  timestamp: number;
}

// Fetch status for multiple devices
async function fetchDeviceStatuses(
  serialNumbers: string[]
): Promise<DeviceStatusResponse> {
  const response = await fetch("/api/admin/devices/status", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ deviceSerialNumbers: serialNumbers }),
  });

  if (!response.ok) {
    throw new Error("Failed to fetch device statuses");
  }

  return response.json();
}

// Fetch status for a single device
async function fetchSingleDeviceStatus(
  serialNumber: string
): Promise<SingleDeviceStatusResponse> {
  const response = await fetch(`/api/admin/devices/${serialNumber}/status`);

  if (!response.ok) {
    throw new Error("Failed to fetch device status");
  }

  return response.json();
}

// Hook for fetching status of multiple devices
export function useDeviceStatusesQuery(
  serialNumbers: string[],
  enabled = true
) {
  return useQuery({
    queryKey: queryKeys.devices.statusBatch(serialNumbers),
    queryFn: () => fetchDeviceStatuses(serialNumbers),
    enabled: enabled && serialNumbers.length > 0,
    staleTime: 30 * 1000, // 30 seconds - status can change frequently
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 60 * 1000, // Refetch every minute for real-time updates
    refetchIntervalInBackground: false, // Only refetch when tab is active
    retry: (failureCount) => {
      // Don't retry too aggressively for status checks
      return failureCount < 2;
    },
  });
}

// Hook for fetching status of a single device
export function useDeviceStatusQuery(serialNumber: string, enabled = true) {
  return useQuery({
    queryKey: queryKeys.devices.singleStatus(serialNumber),
    queryFn: () => fetchSingleDeviceStatus(serialNumber),
    enabled: enabled && !!serialNumber,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 60 * 1000, // Refetch every minute
    refetchIntervalInBackground: false,
    retry: (failureCount) => {
      return failureCount < 2;
    },
  });
}

// Mutation for manually refreshing device statuses
export function useRefreshDeviceStatusesMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (serialNumbers: string[]) => {
      // Invalidate existing queries first to show loading state
      queryClient.invalidateQueries({
        queryKey: queryKeys.devices.statusBatch(serialNumbers),
      });

      // Fetch fresh status data
      return fetchDeviceStatuses(serialNumbers);
    },
    onSuccess: (data, serialNumbers) => {
      // Update the query cache with fresh data
      queryClient.setQueryData(
        queryKeys.devices.statusBatch(serialNumbers),
        data
      );

      // Also invalidate any individual device status queries
      serialNumbers.forEach((serialNumber) => {
        queryClient.invalidateQueries({
          queryKey: queryKeys.devices.singleStatus(serialNumber),
        });
      });
    },
  });
}

// Mutation for refreshing a single device status
export function useRefreshSingleDeviceStatusMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (serialNumber: string) => {
      // Invalidate existing query first
      queryClient.invalidateQueries({
        queryKey: queryKeys.devices.singleStatus(serialNumber),
      });

      return fetchSingleDeviceStatus(serialNumber);
    },
    onSuccess: (data, serialNumber) => {
      // Update the query cache with fresh data
      queryClient.setQueryData(
        queryKeys.devices.singleStatus(serialNumber),
        data
      );
    },
  });
}

// Hook to get cached device statuses without triggering a fetch
export function useCachedDeviceStatuses(serialNumbers: string[]) {
  const queryClient = useQueryClient();

  return queryClient.getQueryData<DeviceStatusResponse>(
    queryKeys.devices.statusBatch(serialNumbers)
  );
}

// Utility hook to check if device status data is stale
export function useDeviceStatusStaleTime(serialNumbers: string[]) {
  const queryClient = useQueryClient();
  const queryState = queryClient.getQueryState(
    queryKeys.devices.statusBatch(serialNumbers)
  );

  const isStale = queryState
    ? Date.now() - (queryState.dataUpdatedAt || 0) > 60 * 1000 // 1 minute
    : true;

  return {
    isStale,
    lastFetched: queryState?.dataUpdatedAt || null,
  };
}

// Hook for optimistic status updates (useful for immediate UI feedback)
export function useOptimisticDeviceStatusUpdate() {
  const queryClient = useQueryClient();

  return {
    updateStatus: (serialNumber: string, status: DeviceStatus) => {
      // Update any queries that include this device
      queryClient.setQueriesData(
        { queryKey: queryKeys.devices.status() },
        (oldData: DeviceStatusResponse | undefined) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            statusMap: {
              ...oldData.statusMap,
              [serialNumber]: status,
            },
            results: oldData.results.map((result) =>
              result.serialNumber === serialNumber
                ? { ...result, status, timestamp: Date.now() }
                : result
            ),
          };
        }
      );
    },
  };
}
