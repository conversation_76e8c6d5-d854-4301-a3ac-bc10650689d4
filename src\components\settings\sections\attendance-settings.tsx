import React from "react";
import { Clock } from "lucide-react";
import { SettingsSection } from "../settings-section";
import { SettingsField } from "../settings-field";

interface AttendanceSettingsData {
  graceTime?: number;
  autoClockOut?: boolean;
  autoClockOutTime?: string;
  allowManualEntry?: boolean;
  requireApproval?: boolean;
  trackBreaks?: boolean;
  maxWorkingHours?: number;
}

interface AttendanceSettingsProps {
  data: AttendanceSettingsData;
  onChange: (field: keyof AttendanceSettingsData, value: any) => void;
  type: "admin" | "company";
}

export function AttendanceSettings({ data, onChange, type }: AttendanceSettingsProps) {
  // Only show attendance settings for companies
  if (type !== "company") {
    return null;
  }

  return (
    <SettingsSection
      title="Attendance Settings"
      description="Configure attendance policies and rules for your company"
      icon={Clock}
    >
      {data.graceTime !== undefined && (
        <SettingsField
          type="number"
          label="Grace Time (minutes)"
          value={data.graceTime}
          onChange={(value) => onChange("graceTime", parseInt(value) || 0)}
          min={0}
          max={60}
          description="Allow employees to be this many minutes late without penalty"
        />
      )}

      {data.autoClockOut !== undefined && (
        <SettingsField
          type="switch"
          label="Auto Clock Out"
          checked={data.autoClockOut}
          onChange={(checked) => onChange("autoClockOut", checked)}
          description="Automatically clock out employees at the end of the day"
        />
      )}

      {data.autoClockOutTime !== undefined && (
        <SettingsField
          type="time"
          label="Auto Clock Out Time"
          value={data.autoClockOutTime}
          onChange={(value) => onChange("autoClockOutTime", value)}
          description="Time when employees are automatically clocked out"
        />
      )}

      {data.allowManualEntry !== undefined && (
        <SettingsField
          type="switch"
          label="Allow Manual Entry"
          checked={data.allowManualEntry}
          onChange={(checked) => onChange("allowManualEntry", checked)}
          description="Allow manual attendance entry by supervisors"
        />
      )}

      {data.requireApproval !== undefined && (
        <SettingsField
          type="switch"
          label="Require Approval"
          checked={data.requireApproval}
          onChange={(checked) => onChange("requireApproval", checked)}
          description="Require supervisor approval for manual attendance entries"
        />
      )}

      {data.trackBreaks !== undefined && (
        <SettingsField
          type="switch"
          label="Track Breaks"
          checked={data.trackBreaks}
          onChange={(checked) => onChange("trackBreaks", checked)}
          description="Track employee break times separately"
        />
      )}

      {data.maxWorkingHours !== undefined && (
        <SettingsField
          type="number"
          label="Maximum Working Hours"
          value={data.maxWorkingHours}
          onChange={(value) => onChange("maxWorkingHours", parseInt(value) || 8)}
          min={1}
          max={24}
          description="Maximum hours an employee can work in a single day"
        />
      )}
    </SettingsSection>
  );
}
