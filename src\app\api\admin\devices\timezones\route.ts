import { NextRequest, NextResponse } from "next/server";
import { verifyAdminAuth } from "@/lib/auth";
import { externalDeviceAPI } from "@/lib/api/external-device-api";

// GET /api/admin/devices/timezones - Get available time zones
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Fetch time zones from external API
    const response = await externalDeviceAPI.selectTimeZone();

    if (response.status !== "200") {
      return NextResponse.json(
        { error: "Failed to fetch time zones from external API" },
        { status: 500 }
      );
    }

    // Transform the data to match our interface
    const timeZones =
      response.data?.map((tz) => ({
        id: tz.TIMEZONE_ID,
        name: tz.TIMEZONE_NAME || `Time Zone ${tz.TIMEZONE_ID}`,
      })) || [];

    return NextResponse.json({
      success: true,
      data: timeZones,
    });
  } catch (error) {
    console.error("Error fetching time zones:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
