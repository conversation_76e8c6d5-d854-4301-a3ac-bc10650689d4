import { NextRequest, NextResponse } from "next/server";
import { withAdminAuth, AuthenticatedRequest } from "@/lib/auth/middleware";
import { prisma } from "@/lib/prisma";
import { authenticator } from "otplib";

interface Disable2FARequest {
  token: string;
}

async function disable2FA(req: AuthenticatedRequest) {
  try {
    const user = req.user!;
    const body: Disable2FARequest = await req.json();

    const { token } = body;

    if (!token) {
      return NextResponse.json(
        { error: "Verification token is required to disable 2FA" },
        { status: 400 }
      );
    }

    // Clean and validate token format
    const cleanToken = token.toString().replace(/\s/g, ""); // Remove any spaces

    if (!/^\d{6}$/.test(cleanToken)) {
      return NextResponse.json(
        { error: "Invalid token format. Please enter a 6-digit code." },
        { status: 400 }
      );
    }

    try {
      // Get admin user to verify 2FA token
      const admin = await prisma.admin.findUnique({
        where: { id: user.userId },
        select: { twoFactorEnabled: true, twoFactorSecret: true },
      });

      if (!admin) {
        return NextResponse.json(
          { error: "Admin user not found" },
          { status: 404 }
        );
      }

      if (!admin.twoFactorEnabled || !admin.twoFactorSecret) {
        return NextResponse.json(
          { error: "2FA is not enabled for this account" },
          { status: 400 }
        );
      }

      // Verify 2FA token
      const isValidToken = authenticator.verify({
        token,
        secret: admin.twoFactorSecret,
      });

      if (!isValidToken) {
        return NextResponse.json(
          { error: "Invalid verification code" },
          { status: 401 }
        );
      }

      // Disable 2FA for the admin
      await prisma.admin.update({
        where: { id: user.userId },
        data: {
          twoFactorSecret: null,
          twoFactorEnabled: false,
          updatedAt: new Date(),
        },
      });
    } catch (dbError) {
      console.warn("Database not available for admin 2FA disable:", dbError);
      // Continue with success response even if database update fails
    }

    return NextResponse.json({
      success: true,
      message: "2FA has been successfully disabled",
    });
  } catch (error) {
    console.error("2FA disable error:", error);
    return NextResponse.json(
      { error: "Failed to disable 2FA" },
      { status: 500 }
    );
  }
}

export const POST = withAdminAuth(disable2FA);
