"use client";

import { useCompanyProfileQuery } from "@/hooks/queries/useProfileQueries";

export function useCompanyProfileForLayout() {
  const { data: profile, isLoading, error } = useCompanyProfileQuery();

  // Return default values during loading or error states
  if (isLoading || error || !profile) {
    return {
      userEmail: "<EMAIL>",
      userName: "Company User",
      isLoading,
      error,
    };
  }

  return {
    userEmail: profile.email,
    userName: profile.name,
    isLoading: false,
    error: null,
  };
}
