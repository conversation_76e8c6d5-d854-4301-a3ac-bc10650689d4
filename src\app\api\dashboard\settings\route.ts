import { NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { prisma } from '@/lib/prisma'

interface CompanySettings {
  general: {
    timezone: string
    dateFormat: string
    workingHours: {
      start: string
      end: string
    }
    workingDays: string[]
  }
  notifications: {
    emailAlerts: boolean
    attendanceReminders: boolean
    lateArrivalAlerts: boolean
    absenteeAlerts: boolean
    deviceOfflineAlerts: boolean
    weeklyReports: boolean
    monthlyReports: boolean
  }
  attendance: {
    graceTime: number
    autoClockOut: boolean
    autoClockOutTime: string
    allowManualEntry: boolean
    requireApproval: boolean
    trackBreaks: boolean
    maxWorkingHours: number
  }
  devices: {
    allowMultipleCheckIns: boolean
    deviceSyncInterval: number
    offlineDataRetention: number
    faceRecognitionThreshold: number
    fingerprintThreshold: number
  }
}

async function getCompanySettings(req: AuthenticatedRequest) {
  try {
    const user = req.user!
    
    if (user.role !== 'company') {
      return NextResponse.json(
        { error: 'Company access required' },
        { status: 403 }
      )
    }

    try {
      // Get company details
      const company = await prisma.company.findUnique({
        where: { id: user.companyId }
      })

      if (!company) {
        return NextResponse.json(
          { error: 'Company not found' },
          { status: 404 }
        )
      }

      // Get or create company settings
      let companySettings = await prisma.companySettings.findUnique({
        where: { companyId: company.id }
      })

      if (!companySettings) {
        // Create default settings if none exist
        companySettings = await prisma.companySettings.create({
          data: {
            companyId: company.id,
            companyName: company.name,
            timezone: 'UTC',
            dateFormat: 'MM/DD/YYYY',
            workingHoursStart: '09:00',
            workingHoursEnd: '17:00',
            workingDays: 'Monday,Tuesday,Wednesday,Thursday,Friday',
            emailAlerts: true,
            attendanceReminders: true,
            lateArrivalAlerts: true,
            absenteeAlerts: true,
            deviceOfflineAlerts: true,
            weeklyReports: true,
            monthlyReports: false,
            graceTime: 15,
            autoClockOut: false,
            autoClockOutTime: '18:00',
            allowManualEntry: false,
            requireApproval: true,
            trackBreaks: false,
            maxWorkingHours: 12,
            allowMultipleCheckIns: false,
            deviceSyncInterval: 30,
            offlineDataRetention: 7,
            faceRecognitionThreshold: 80,
            fingerprintThreshold: 85,
          }
        })
      }

      // Convert database format to API format
      const settings: CompanySettings = {
        general: {
          timezone: companySettings.timezone,
          dateFormat: companySettings.dateFormat,
          workingHours: {
            start: companySettings.workingHoursStart,
            end: companySettings.workingHoursEnd
          },
          workingDays: companySettings.workingDays.split(',')
        },
        notifications: {
          emailAlerts: companySettings.emailAlerts,
          attendanceReminders: companySettings.attendanceReminders,
          lateArrivalAlerts: companySettings.lateArrivalAlerts,
          absenteeAlerts: companySettings.absenteeAlerts,
          deviceOfflineAlerts: companySettings.deviceOfflineAlerts,
          weeklyReports: companySettings.weeklyReports,
          monthlyReports: companySettings.monthlyReports
        },
        attendance: {
          graceTime: companySettings.graceTime,
          autoClockOut: companySettings.autoClockOut,
          autoClockOutTime: companySettings.autoClockOutTime,
          allowManualEntry: companySettings.allowManualEntry,
          requireApproval: companySettings.requireApproval,
          trackBreaks: companySettings.trackBreaks,
          maxWorkingHours: companySettings.maxWorkingHours
        },
        devices: {
          allowMultipleCheckIns: companySettings.allowMultipleCheckIns,
          deviceSyncInterval: companySettings.deviceSyncInterval,
          offlineDataRetention: companySettings.offlineDataRetention,
          faceRecognitionThreshold: companySettings.faceRecognitionThreshold,
          fingerprintThreshold: companySettings.fingerprintThreshold
        }
      }

      return NextResponse.json({
        success: true,
        data: settings
      })

    } catch (dbError) {
      console.warn('Database not available for settings:', dbError)
      
      // Return default settings when database is not available
      const defaultSettings: CompanySettings = {
        general: {
          timezone: 'UTC',
          dateFormat: 'MM/DD/YYYY',
          workingHours: {
            start: '09:00',
            end: '17:00'
          },
          workingDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
        },
        notifications: {
          emailAlerts: true,
          attendanceReminders: true,
          lateArrivalAlerts: true,
          absenteeAlerts: true,
          deviceOfflineAlerts: true,
          weeklyReports: true,
          monthlyReports: false
        },
        attendance: {
          graceTime: 15,
          autoClockOut: false,
          autoClockOutTime: '18:00',
          allowManualEntry: false,
          requireApproval: true,
          trackBreaks: false,
          maxWorkingHours: 12
        },
        devices: {
          allowMultipleCheckIns: false,
          deviceSyncInterval: 30,
          offlineDataRetention: 7,
          faceRecognitionThreshold: 80,
          fingerprintThreshold: 85
        }
      }

      return NextResponse.json({
        success: true,
        data: defaultSettings
      })
    }

  } catch (error) {
    console.error('Get company settings error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch company settings' },
      { status: 500 }
    )
  }
}

async function updateCompanySettings(req: AuthenticatedRequest) {
  try {
    const user = req.user!
    
    if (user.role !== 'company') {
      return NextResponse.json(
        { error: 'Company access required' },
        { status: 403 }
      )
    }

    const body = await req.json()
    const { general, notifications, attendance, devices } = body
    
    // Validate settings structure
    if (!general || !notifications || !attendance || !devices) {
      return NextResponse.json(
        { error: 'Invalid settings structure' },
        { status: 400 }
      )
    }

    // Validate specific settings
    if (attendance.graceTime < 0 || attendance.graceTime > 60) {
      return NextResponse.json(
        { error: 'Grace time must be between 0 and 60 minutes' },
        { status: 400 }
      )
    }

    if (attendance.maxWorkingHours < 1 || attendance.maxWorkingHours > 24) {
      return NextResponse.json(
        { error: 'Max working hours must be between 1 and 24 hours' },
        { status: 400 }
      )
    }

    if (devices.deviceSyncInterval < 5 || devices.deviceSyncInterval > 120) {
      return NextResponse.json(
        { error: 'Device sync interval must be between 5 and 120 minutes' },
        { status: 400 }
      )
    }

    // Get or create company settings
    let companySettings = await prisma.companySettings.findUnique({
      where: { companyId: user.companyId }
    })

    if (!companySettings) {
      // Create new settings if none exist
      companySettings = await prisma.companySettings.create({
        data: {
          companyId: user.companyId!,
          companyName: 'Company', // Use a default value since we don't update it from settings
          timezone: general.timezone,
          dateFormat: general.dateFormat,
          workingHoursStart: general.workingHours.start,
          workingHoursEnd: general.workingHours.end,
          workingDays: general.workingDays.join(','),
          emailAlerts: notifications.emailAlerts,
          attendanceReminders: notifications.attendanceReminders,
          lateArrivalAlerts: notifications.lateArrivalAlerts,
          absenteeAlerts: notifications.absenteeAlerts,
          deviceOfflineAlerts: notifications.deviceOfflineAlerts,
          weeklyReports: notifications.weeklyReports,
          monthlyReports: notifications.monthlyReports,
          graceTime: attendance.graceTime,
          autoClockOut: attendance.autoClockOut,
          autoClockOutTime: attendance.autoClockOutTime,
          allowManualEntry: attendance.allowManualEntry,
          requireApproval: attendance.requireApproval,
          trackBreaks: attendance.trackBreaks,
          maxWorkingHours: attendance.maxWorkingHours,
          allowMultipleCheckIns: devices.allowMultipleCheckIns,
          deviceSyncInterval: devices.deviceSyncInterval,
          offlineDataRetention: devices.offlineDataRetention,
          faceRecognitionThreshold: devices.faceRecognitionThreshold,
          fingerprintThreshold: devices.fingerprintThreshold,
        }
      })
    } else {
      // Update existing settings
      companySettings = await prisma.companySettings.update({
        where: { companyId: user.companyId },
        data: {
          // Don't update companyName from settings - it's managed in profile
          timezone: general.timezone,
          dateFormat: general.dateFormat,
          workingHoursStart: general.workingHours.start,
          workingHoursEnd: general.workingHours.end,
          workingDays: general.workingDays.join(','),
          emailAlerts: notifications.emailAlerts,
          attendanceReminders: notifications.attendanceReminders,
          lateArrivalAlerts: notifications.lateArrivalAlerts,
          absenteeAlerts: notifications.absenteeAlerts,
          deviceOfflineAlerts: notifications.deviceOfflineAlerts,
          weeklyReports: notifications.weeklyReports,
          monthlyReports: notifications.monthlyReports,
          graceTime: attendance.graceTime,
          autoClockOut: attendance.autoClockOut,
          autoClockOutTime: attendance.autoClockOutTime,
          allowManualEntry: attendance.allowManualEntry,
          requireApproval: attendance.requireApproval,
          trackBreaks: attendance.trackBreaks,
          maxWorkingHours: attendance.maxWorkingHours,
          allowMultipleCheckIns: devices.allowMultipleCheckIns,
          deviceSyncInterval: devices.deviceSyncInterval,
          offlineDataRetention: devices.offlineDataRetention,
          faceRecognitionThreshold: devices.faceRecognitionThreshold,
          fingerprintThreshold: devices.fingerprintThreshold,
        }
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Company settings updated successfully'
    })

  } catch (error) {
    console.error('Update company settings error:', error)
    return NextResponse.json(
      { error: 'Failed to update company settings' },
      { status: 500 }
    )
  }
}

export const GET = withAuth(getCompanySettings)
export const PUT = withAuth(updateCompanySettings)
