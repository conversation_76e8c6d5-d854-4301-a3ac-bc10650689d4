"use client";

import { memo } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  <PERSON>Circle,
  AlertCircle,
  AlertTriangle,
  Clock,
  Zap,
} from "lucide-react";
import { SystemStatus } from "@/hooks/queries/useDashboardQueries";

interface SystemStatusProps {
  systemStatus: SystemStatus | undefined;
  loading: boolean;
  error?: string;
  title?: string;
  description?: string;
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "operational":
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case "degraded":
      return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    case "outage":
      return <AlertCircle className="h-4 w-4 text-red-600" />;
    default:
      return <Clock className="h-4 w-4 text-gray-600" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case "operational":
      return "bg-green-100 text-green-800 border-green-200";
    case "degraded":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "outage":
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return "bg-green-500";
  if (percentage >= 70) return "bg-yellow-500";
  return "bg-red-500";
};

const formatTimeAgo = (timestamp: string) => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffInSeconds = Math.floor((now.getTime() - time.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return `${diffInSeconds}s ago`;
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m ago`;
  } else {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h ago`;
  }
};

export const SystemStatusCard = memo(function SystemStatusCard({
  systemStatus,
  loading,
  error,
  title = "System Status",
  description = "Current system health and service status",
}: SystemStatusProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-6 w-20" />
            </div>
            <Skeleton className="h-2 w-full" />
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-4 w-4 rounded-full" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                  <Skeleton className="h-4 w-16" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-300" />
            <p className="text-red-600">Failed to load system status</p>
            <p className="text-sm text-gray-500">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!systemStatus) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No status data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          {title}
          <Badge className={getStatusColor(systemStatus.overall.status)}>
            {getStatusIcon(systemStatus.overall.status)}
            <span className="ml-1 capitalize">
              {systemStatus.overall.status}
            </span>
          </Badge>
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Overall Health */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium">Overall Health</span>
              <span className="text-gray-600">
                {systemStatus.overall.healthPercentage}%
              </span>
            </div>
            <div className="relative bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all ${getProgressColor(
                  systemStatus.overall.healthPercentage
                )}`}
                style={{ width: `${systemStatus.overall.healthPercentage}%` }}
              />
            </div>
            <p className="text-xs text-gray-600">
              {systemStatus.overall.message}
            </p>
          </div>

          {/* Service Status */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900">Services</h4>
            {systemStatus.services.map((service) => (
              <div
                key={service.id}
                className="flex items-center justify-between"
              >
                <div className="flex items-center space-x-2">
                  {getStatusIcon(service.status)}
                  <span className="text-sm font-medium">{service.service}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {service.responseTime && (
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <Zap className="h-3 w-3" />
                      <span>{service.responseTime}ms</span>
                    </div>
                  )}
                  <span className="text-xs text-gray-500">
                    {formatTimeAgo(service.lastChecked)}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Last Updated */}
          <div className="pt-2 border-t text-xs text-gray-500">
            Last updated: {formatTimeAgo(systemStatus.overall.lastUpdated)}
          </div>
        </div>
      </CardContent>
    </Card>
  );
});
