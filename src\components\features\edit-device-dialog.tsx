"use client";

import { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Save, X } from "lucide-react";
import { toast } from "sonner";
import { useTimezoneOptions } from "@/hooks/queries/useTimezoneQueries";

interface Device {
  id: string;
  name: string;
  serialNumber: string;
  model: string;
  location: string;
  timeZone: number;
  ipAddress: string;
  port: number;
}

interface EditDeviceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  device: Device | null;
  onSave: (deviceData: Record<string, unknown>) => Promise<void>;
}

interface EditDeviceData {
  deviceSlno: string;
  deviceName: string;
  modelNo: string;
  timeZoneId: string;
  location: string;
  endpointUrl: string;
}

export function EditDeviceDialog({
  open,
  onOpenChange,
  device,
  onSave,
}: EditDeviceDialogProps) {
  const { options: timezoneOptions, isLoading: timezonesLoading } =
    useTimezoneOptions();
  const [formData, setFormData] = useState<EditDeviceData>({
    deviceSlno: "",
    deviceName: "",
    modelNo: "",
    timeZoneId: "",
    location: "",
    endpointUrl: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form data when device changes
  useEffect(() => {
    if (device) {
      setFormData({
        deviceSlno: device.serialNumber,
        deviceName: device.name,
        modelNo: device.model,
        timeZoneId: device.timeZone.toString(),
        location: device.location,
        endpointUrl: `http://${device.ipAddress}:${device.port}`,
      });
      setErrors({});
    }
  }, [device]);

  const handleInputChange = (field: keyof EditDeviceData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.deviceSlno.trim()) {
      newErrors.deviceSlno = "Device serial number is required";
    }

    if (!formData.deviceName.trim()) {
      newErrors.deviceName = "Device name is required";
    }

    if (!formData.modelNo.trim()) {
      newErrors.modelNo = "Model number is required";
    }

    if (!formData.timeZoneId) {
      newErrors.timeZoneId = "Time zone is required";
    }

    if (!formData.location.trim()) {
      newErrors.location = "Location is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!device || !validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await onSave(formData);
      toast.success("Device updated successfully");
      onOpenChange(false);
    } catch (error: unknown) {
      const errorMessage =
        (error as { response?: { data?: { error?: string } } }).response?.data
          ?.error || "Failed to update device";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    setErrors({});
  };

  if (!device) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Device</DialogTitle>
          <DialogDescription>
            Update device information and settings
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Device Serial Number */}
          <div className="space-y-2">
            <Label htmlFor="deviceSlno">Device Serial Number</Label>
            <Input
              id="deviceSlno"
              value={formData.deviceSlno}
              onChange={(e) => handleInputChange("deviceSlno", e.target.value)}
              className={errors.deviceSlno ? "border-red-500" : ""}
              disabled={true} // Serial number should not be editable
            />
            {errors.deviceSlno && (
              <p className="text-sm text-red-600">{errors.deviceSlno}</p>
            )}
          </div>

          {/* Device Name */}
          <div className="space-y-2">
            <Label htmlFor="deviceName">Device Name</Label>
            <Input
              id="deviceName"
              value={formData.deviceName}
              onChange={(e) => handleInputChange("deviceName", e.target.value)}
              className={errors.deviceName ? "border-red-500" : ""}
              disabled={isLoading}
            />
            {errors.deviceName && (
              <p className="text-sm text-red-600">{errors.deviceName}</p>
            )}
          </div>

          {/* Model Number */}
          <div className="space-y-2">
            <Label htmlFor="modelNo">Model Number</Label>
            <Input
              id="modelNo"
              value={formData.modelNo}
              onChange={(e) => handleInputChange("modelNo", e.target.value)}
              className={errors.modelNo ? "border-red-500" : ""}
              disabled={isLoading}
            />
            {errors.modelNo && (
              <p className="text-sm text-red-600">{errors.modelNo}</p>
            )}
          </div>

          {/* Time Zone */}
          <div className="space-y-2">
            <Label htmlFor="timeZoneId">Time Zone</Label>
            <Select
              value={formData.timeZoneId}
              onValueChange={(value) => handleInputChange("timeZoneId", value)}
              disabled={isLoading || timezonesLoading}
            >
              <SelectTrigger>
                <SelectValue
                  placeholder={
                    timezonesLoading
                      ? "Loading timezones..."
                      : "Select time zone"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {timezoneOptions.map((tz: { value: string; label: string }) => (
                  <SelectItem key={tz.value} value={tz.value}>
                    {tz.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.timeZoneId && (
              <p className="text-sm text-red-600">{errors.timeZoneId}</p>
            )}
          </div>

          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location">Location</Label>
            <Input
              id="location"
              value={formData.location}
              onChange={(e) => handleInputChange("location", e.target.value)}
              className={errors.location ? "border-red-500" : ""}
              disabled={isLoading}
            />
            {errors.location && (
              <p className="text-sm text-red-600">{errors.location}</p>
            )}
          </div>

          {/* Endpoint URL */}
          <div className="space-y-2">
            <Label htmlFor="endpointUrl">Endpoint URL (Optional)</Label>
            <Input
              id="endpointUrl"
              value={formData.endpointUrl}
              onChange={(e) => handleInputChange("endpointUrl", e.target.value)}
              placeholder="http://device-ip:port"
              disabled={isLoading}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
