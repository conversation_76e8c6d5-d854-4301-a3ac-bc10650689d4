"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Shield,
  ShieldCheck,
  ShieldX,
  Smartphone,
  Copy,
  Download,
  AlertTriangle,
} from "lucide-react";
import { toast } from "react-toastify";
import {
  useSetupAdmin2FAMutation,
  useVerifyAdmin2FAMutation,
  useDisableAdmin2FAMutation,
  useSetupCompany2FAMutation,
  useVerifyCompany2FAMutation,
  useDisableCompany2FAMutation,
} from "@/hooks/queries/useSecurityQueries";

interface TwoFactorAuthProps {
  isEnabled?: boolean;
  userRole: "admin" | "company";
}

export function TwoFactorAuth({
  isEnabled = false,
  userRole,
}: TwoFactorAuthProps) {
  const [setupData, setSetupData] = useState<{
    secret: string;
    qrCodeUrl: string;
    backupCodes: string[];
  } | null>(null);
  const [verificationCode, setVerificationCode] = useState("");
  const [disableToken, setDisableToken] = useState("");
  const [showSetupDialog, setShowSetupDialog] = useState(false);
  const [showDisableDialog, setShowDisableDialog] = useState(false);
  const [step, setStep] = useState<"setup" | "verify" | "complete">("setup");

  // Use real mutations based on user role
  const adminSetup2FA = useSetupAdmin2FAMutation();
  const adminVerify2FA = useVerifyAdmin2FAMutation();
  const adminDisable2FA = useDisableAdmin2FAMutation();

  const companySetup2FA = useSetupCompany2FAMutation();
  const companyVerify2FA = useVerifyCompany2FAMutation();
  const companyDisable2FA = useDisableCompany2FAMutation();

  const setup2FAMutation =
    userRole === "admin" ? adminSetup2FA : companySetup2FA;
  const verify2FAMutation =
    userRole === "admin" ? adminVerify2FA : companyVerify2FA;
  const disable2FAMutation =
    userRole === "admin" ? adminDisable2FA : companyDisable2FA;

  const handleSetup2FA = async () => {
    try {
      const data = await setup2FAMutation.mutateAsync();
      setSetupData(data);
      setStep("verify");
      setShowSetupDialog(true);
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || "Failed to setup 2FA";
      toast.error(errorMessage);
    }
  };

  const handleVerify2FA = async () => {
    if (!setupData || !verificationCode) {
      toast.error("Please enter the verification code");
      return;
    }

    try {
      await verify2FAMutation.mutateAsync({
        secret: setupData.secret,
        token: verificationCode,
      });
      setStep("complete");
      toast.success("2FA has been successfully enabled");

      // Close dialog after a short delay
      setTimeout(() => {
        setShowSetupDialog(false);
        setStep("setup");
        setVerificationCode("");
        setSetupData(null);
        // Profile query will be invalidated by the mutation's onSuccess
      }, 2000);
    } catch (error: any) {
      let errorMessage = "Failed to verify 2FA code";

      if (error.response?.status === 400 || error.response?.status === 401) {
        errorMessage =
          "❌ Invalid verification code. Please check the code from your authenticator app and try again.";
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      }

      toast.error(errorMessage);
    }
  };

  const handleDisable2FA = async () => {
    if (!disableToken) {
      toast.error("Please enter your 2FA verification code");
      return;
    }

    try {
      await disable2FAMutation.mutateAsync({ token: disableToken });
      toast.success("2FA has been successfully disabled");
      setShowDisableDialog(false);
      setDisableToken("");
      // Profile query will be invalidated by the mutation's onSuccess
    } catch (error: any) {
      let errorMessage = "Failed to disable 2FA";

      if (error.response?.status === 401) {
        errorMessage =
          "❌ Invalid verification code. Please enter the correct 6-digit code from your authenticator app.";
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      }

      toast.error(errorMessage);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard");
  };

  const downloadBackupCodes = () => {
    if (!setupData || !setupData.backupCodes) return;

    const content = `Smart Attendance Portal - Backup Codes\n\nGenerated: ${new Date().toLocaleString()}\n\nBackup Codes:\n${setupData.backupCodes.join(
      "\n"
    )}\n\nKeep these codes safe! Each code can only be used once.`;

    const blob = new Blob([content], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "smart-attendance-backup-codes.txt";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success("Backup codes downloaded");
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            Two-Factor Authentication
          </div>
          <Badge
            variant={isEnabled ? "default" : "secondary"}
            className={isEnabled ? "bg-green-100 text-green-800" : ""}
          >
            {isEnabled ? (
              <>
                <ShieldCheck className="h-3 w-3 mr-1" />
                Enabled
              </>
            ) : (
              <>
                <ShieldX className="h-3 w-3 mr-1" />
                Disabled
              </>
            )}
          </Badge>
        </CardTitle>
        <CardDescription>
          Add an extra layer of security to your account with two-factor
          authentication
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isEnabled ? (
          <>
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Two-factor authentication is not enabled. Your account is more
                vulnerable to unauthorized access.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <h4 className="font-medium">Benefits of 2FA:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Protects against password theft</li>
                <li>• Prevents unauthorized access</li>
                <li>• Meets security compliance requirements</li>
                <li>• Works with popular authenticator apps</li>
              </ul>
            </div>

            <Button
              onClick={handleSetup2FA}
              disabled={setup2FAMutation.isPending}
            >
              <Smartphone className="h-4 w-4 mr-2" />
              {setup2FAMutation.isPending ? "Setting up..." : "Enable 2FA"}
            </Button>
          </>
        ) : (
          <>
            <Alert>
              <ShieldCheck className="h-4 w-4" />
              <AlertDescription>
                Two-factor authentication is enabled and protecting your
                account.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <h4 className="font-medium">2FA Status:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Authenticator app configured</li>
                <li>• Backup codes generated</li>
                <li>• Account protection active</li>
              </ul>
            </div>

            <Dialog
              open={showDisableDialog}
              onOpenChange={setShowDisableDialog}
            >
              <DialogTrigger asChild>
                <Button variant="destructive">
                  <ShieldX className="h-4 w-4 mr-2" />
                  Disable 2FA
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Disable Two-Factor Authentication</DialogTitle>
                  <DialogDescription>
                    This will remove the extra security layer from your account.
                    Enter your current 2FA code to confirm.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="disableToken">Verification Code</Label>
                    <Input
                      id="disableToken"
                      type="text"
                      value={disableToken}
                      onChange={(e) => {
                        // Only allow digits and limit to 6 characters
                        const value = e.target.value
                          .replace(/\D/g, "")
                          .slice(0, 6);
                        setDisableToken(value);
                      }}
                      placeholder="Enter 6-digit code from your authenticator app"
                      maxLength={6}
                      pattern="[0-9]{6}"
                    />
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>
                        Enter the current 6-digit code from your authenticator
                        app to disable 2FA
                      </p>
                      <p className="text-xs text-gray-500">
                        💡 Tip: Make sure your device time is correct and use a
                        fresh code (codes change every 30 seconds)
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setShowDisableDialog(false)}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={handleDisable2FA}
                      disabled={disable2FAMutation.isPending || !disableToken}
                      className="flex-1"
                    >
                      {disable2FAMutation.isPending
                        ? "Disabling..."
                        : "Disable 2FA"}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </>
        )}

        {/* Setup Dialog */}
        <Dialog open={showSetupDialog} onOpenChange={setShowSetupDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {step === "setup" && "Set up Two-Factor Authentication"}
                {step === "verify" && "Verify Your Setup"}
                {step === "complete" && "2FA Successfully Enabled"}
              </DialogTitle>
              <DialogDescription>
                {step === "setup" &&
                  "Scan the QR code with your authenticator app"}
                {step === "verify" &&
                  "Enter the code from your authenticator app"}
                {step === "complete" &&
                  "Your account is now protected with 2FA"}
              </DialogDescription>
            </DialogHeader>

            {step === "verify" && setupData && (
              <div className="space-y-4">
                {/* QR Code */}
                <div className="text-center">
                  <img
                    src={setupData.qrCodeUrl}
                    alt="2FA QR Code"
                    className="mx-auto border rounded-lg"
                    width={200}
                    height={200}
                  />
                </div>

                {/* Manual Entry */}
                <div className="space-y-2">
                  <Label>Manual Entry Key</Label>
                  <div className="flex space-x-2">
                    <Input
                      value={setupData.secret}
                      readOnly
                      className="font-mono text-xs"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(setupData.secret)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Verification Code Input */}
                <div className="space-y-2">
                  <Label htmlFor="verificationCode">Verification Code</Label>
                  <Input
                    id="verificationCode"
                    value={verificationCode}
                    onChange={(e) => {
                      // Only allow digits and limit to 6 characters
                      const value = e.target.value
                        .replace(/\D/g, "")
                        .slice(0, 6);
                      setVerificationCode(value);
                    }}
                    placeholder="Enter 6-digit code"
                    maxLength={6}
                    pattern="[0-9]{6}"
                  />
                  <p className="text-xs text-gray-500">
                    💡 Enter the 6-digit code currently shown in your
                    authenticator app
                  </p>
                </div>

                {/* Backup Codes */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>Backup Codes</Label>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={downloadBackupCodes}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                  </div>
                  <div className="grid grid-cols-2 gap-1 text-xs font-mono bg-gray-50 p-2 rounded">
                    {setupData.backupCodes?.map((code, index) => (
                      <div key={index}>{code}</div>
                    )) || (
                      <div className="col-span-2 text-center text-gray-500">
                        No backup codes available
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-gray-600">
                    Save these codes safely. Each can only be used once.
                  </p>
                </div>

                <Button
                  onClick={handleVerify2FA}
                  disabled={verify2FAMutation.isPending || !verificationCode}
                  className="w-full"
                >
                  {verify2FAMutation.isPending
                    ? "Verifying..."
                    : "Verify & Enable 2FA"}
                </Button>
              </div>
            )}

            {step === "complete" && (
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <ShieldCheck className="h-8 w-8 text-green-600" />
                </div>
                <p className="text-green-600 font-medium">
                  Two-factor authentication has been successfully enabled!
                </p>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
