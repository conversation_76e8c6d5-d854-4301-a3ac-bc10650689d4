import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { externalDeviceAPI } from "@/lib/api/external-device-api";
import { verifyAdminAuth } from "@/lib/auth";

const remoteRegisterSchema = z.object({
  action: z.enum(["remoteRegisterFace", "remoteRegisterFinger"]),
  userId: z.string().min(1, "User ID is required"),
  userName: z.string().min(1, "User name is required"),
});

const openDoorSchema = z.object({
  action: z.literal("openDoor"),
});

const rebootDeviceSchema = z.object({
  action: z.literal("rebootDevice"),
});

const requestSchema = z.union([
  remoteRegisterSchema,
  openDoorSchema,
  rebootDeviceSchema,
]);

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const deviceSerial = resolvedParams.deviceId;
    const body = await request.json();
    console.log("Device actions request:", { deviceSerial, body });

    // Validate request body
    const validatedData = requestSchema.parse(body);

    switch (validatedData.action) {
      case "remoteRegisterFace": {
        console.log(
          `Remote registering face for user ${validatedData.userId} on device ${deviceSerial}`
        );

        const response = await externalDeviceAPI.remoteRegisterFace({
          DEVICESLNO: deviceSerial,
          USERID: validatedData.userId,
          USERNAME: validatedData.userName,
        });

        if (response.status === "200") {
          console.log("Successfully initiated remote face registration");

          return NextResponse.json({
            success: true,
            message: `Remote face registration initiated for user ${validatedData.userId}. Please follow the instructions on the device.`,
          });
        } else {
          console.error(
            "Failed to initiate remote face registration:",
            response
          );
          return NextResponse.json(
            {
              error:
                response.msg || "Failed to initiate remote face registration",
            },
            { status: 400 }
          );
        }
      }

      case "remoteRegisterFinger": {
        console.log(
          `Remote registering fingerprint for user ${validatedData.userId} on device ${deviceSerial}`
        );

        const response = await externalDeviceAPI.remoteRegisterFinger({
          DEVICESLNO: deviceSerial,
          USERID: validatedData.userId,
          USERNAME: validatedData.userName,
        });

        if (response.status === "200") {
          console.log("Successfully initiated remote fingerprint registration");

          return NextResponse.json({
            success: true,
            message: `Remote fingerprint registration initiated for user ${validatedData.userId}. Please follow the instructions on the device.`,
          });
        } else {
          console.error(
            "Failed to initiate remote fingerprint registration:",
            response
          );
          return NextResponse.json(
            {
              error:
                response.msg ||
                "Failed to initiate remote fingerprint registration",
            },
            { status: 400 }
          );
        }
      }

      case "openDoor": {
        console.log(`Opening door for device ${deviceSerial}`);

        const response = await externalDeviceAPI.openDoor(deviceSerial);

        if (response.status === "200") {
          console.log("Successfully opened door");

          return NextResponse.json({
            success: true,
            message: "Door opened successfully",
          });
        } else {
          console.error("Failed to open door:", response);
          return NextResponse.json(
            {
              error: response.msg || "Failed to open door",
            },
            { status: 400 }
          );
        }
      }

      case "rebootDevice": {
        console.log(`Rebooting device ${deviceSerial}`);

        const response = await externalDeviceAPI.rebootDevice(deviceSerial);

        if (response.status === "200") {
          console.log("Successfully initiated device reboot");

          return NextResponse.json({
            success: true,
            message:
              "Device reboot initiated successfully. The device will restart shortly.",
          });
        } else {
          console.error("Failed to reboot device:", response);
          return NextResponse.json(
            {
              error: response.msg || "Failed to reboot device",
            },
            { status: 400 }
          );
        }
      }

      default:
        return NextResponse.json(
          {
            error: "Invalid action",
          },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Device actions API error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: error.issues,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}
