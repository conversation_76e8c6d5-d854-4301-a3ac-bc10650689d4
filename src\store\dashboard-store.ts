import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface CompanyProfile {
  id: string;
  name: string;
  email: string;
  organizationId: string;
  userType: string;
  status: string;
  expiresAt: string;
  createdAt: string;
  totalDevices: number;
  totalEmployees: number;
  lastSyncTime: string;
  accountHealth: number;
  daysRemaining: number;
}

interface CompanyStats {
  totalEmployees: number;
  presentToday: number;
  totalDevices: number;
  activeDevices: number;
  attendanceRate: number;
  lastSyncTime: string;
}

interface DeviceStatus {
  [deviceId: string]: {
    status: string;
    lastChecked: number;
  };
}

interface DashboardState {
  // Company data
  companyProfile: CompanyProfile | null;
  companyStats: CompanyStats | null;
  
  // Device data
  deviceStatuses: DeviceStatus;
  
  // UI state
  isLoading: boolean;
  lastRefresh: number;
  
  // Settings
  autoRefresh: boolean;
  refreshInterval: number;
  
  // Actions
  setCompanyProfile: (profile: CompanyProfile) => void;
  setCompanyStats: (stats: CompanyStats) => void;
  setDeviceStatus: (deviceId: string, status: string) => void;
  setLoading: (loading: boolean) => void;
  setLastRefresh: (timestamp: number) => void;
  setAutoRefresh: (enabled: boolean) => void;
  setRefreshInterval: (interval: number) => void;
  
  // Utility functions (removed computed getters to prevent infinite loops)
  
  // Cache management
  clearCache: () => void;
  isDataStale: (maxAge: number) => boolean;
}

export const useDashboardStore = create<DashboardState>()(
  devtools(
    (set, get) => ({
      // Initial state
      companyProfile: null,
      companyStats: null,
      deviceStatuses: {},
      isLoading: false,
      lastRefresh: 0,
      autoRefresh: true,
      refreshInterval: 30000, // 30 seconds

      // Actions
      setCompanyProfile: (profile) =>
        set({ companyProfile: profile }, false, 'setCompanyProfile'),

      setCompanyStats: (stats) =>
        set({ companyStats: stats }, false, 'setCompanyStats'),

      setDeviceStatus: (deviceId, status) =>
        set(
          (state) => ({
            deviceStatuses: {
              ...state.deviceStatuses,
              [deviceId]: {
                status,
                lastChecked: Date.now(),
              },
            },
          }),
          false,
          'setDeviceStatus'
        ),

      setLoading: (loading) =>
        set({ isLoading: loading }, false, 'setLoading'),

      setLastRefresh: (timestamp) =>
        set({ lastRefresh: timestamp }, false, 'setLastRefresh'),

      setAutoRefresh: (enabled) =>
        set({ autoRefresh: enabled }, false, 'setAutoRefresh'),

      setRefreshInterval: (interval) =>
        set({ refreshInterval: interval }, false, 'setRefreshInterval'),

      // Cache management
      clearCache: () =>
        set(
          {
            companyProfile: null,
            companyStats: null,
            deviceStatuses: {},
            lastRefresh: 0,
          },
          false,
          'clearCache'
        ),

      isDataStale: (maxAge) => {
        const state = get();
        return Date.now() - state.lastRefresh > maxAge;
      },
    }),
    {
      name: 'dashboard-store',
    }
  )
);

// Selectors for better performance
export const useCompanyProfile = () => useDashboardStore((state) => state.companyProfile);
export const useCompanyStats = () => useDashboardStore((state) => state.companyStats);
export const useDeviceStatuses = () => useDashboardStore((state) => state.deviceStatuses);
export const useIsLoading = () => useDashboardStore((state) => state.isLoading);
export const useAutoRefresh = () => useDashboardStore((state) => state.autoRefresh);

// Action selectors
export const useDashboardActions = () => useDashboardStore((state) => ({
  setCompanyProfile: state.setCompanyProfile,
  setCompanyStats: state.setCompanyStats,
  setDeviceStatus: state.setDeviceStatus,
  setLoading: state.setLoading,
  setLastRefresh: state.setLastRefresh,
  clearCache: state.clearCache,
}));

// Utility functions (moved outside store to prevent infinite loops)
export const getDeviceStatus = (deviceStatuses: DeviceStatus, deviceId: string): string | null => {
  const device = deviceStatuses[deviceId];
  if (!device) return null;

  // Check if status is stale (older than 1 minute)
  const isStale = Date.now() - device.lastChecked > 60000;
  return isStale ? null : device.status;
};

export const isDeviceOnline = (deviceStatuses: DeviceStatus, deviceId: string): boolean => {
  const status = getDeviceStatus(deviceStatuses, deviceId);
  return status === 'ONLINE';
};

export const getOnlineDeviceCount = (deviceStatuses: DeviceStatus): number => {
  return Object.values(deviceStatuses).filter(
    (device) =>
      device.status === 'ONLINE' &&
      Date.now() - device.lastChecked < 60000 // Not stale
  ).length;
};

// Device-specific selectors (using utility functions)
export const useDeviceStatus = (deviceId: string) => {
  const deviceStatuses = useDashboardStore((state) => state.deviceStatuses);
  return getDeviceStatus(deviceStatuses, deviceId);
};

export const useIsDeviceOnline = (deviceId: string) => {
  const deviceStatuses = useDashboardStore((state) => state.deviceStatuses);
  return isDeviceOnline(deviceStatuses, deviceId);
};

export const useOnlineDeviceCount = () => {
  const deviceStatuses = useDashboardStore((state) => state.deviceStatuses);
  return getOnlineDeviceCount(deviceStatuses);
};
