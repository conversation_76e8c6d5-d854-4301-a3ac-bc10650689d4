"use client";

import { memo } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { DataTable, Column } from "@/components/shared/data-table";
import { SearchInput } from "@/components/shared/SearchInput";

interface Device {
  id: string;
  name: string;
  serialNumber: string;
  model: string;
  modelName: string;
  location: string;
  ipAddress: string;
  port: number;
  status: string;
  lastSeen?: string | null;
  firmwareVersion: string;
  totalUsers: number;
  timeZone: number;
  logCount: number;
  features: {
    faceRecognition: boolean;
    fingerprintScanner: boolean;
    cardReader: boolean;
    temperatureCheck: boolean;
  };
  biometricCounts: {
    fingerprints: number;
    faces: number;
    cards: number;
    passwords: number;
  };
  createdAt: string;
  updatedAt: string;
  allocatedCompany?: string | null;
}

interface PaginationInfo {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

interface DeviceTableSectionProps {
  devices: Device[];
  columns: Column<Device>[];
  loading: boolean;
  pagination: PaginationInfo;
  selectedDevices: Device[];
  searchQuery: string;
  sortBy: string;
  sortOrder: "asc" | "desc";
  bulkActions: Array<{
    label: string;
    onClick: () => void;
    variant?: "default" | "destructive";
  }>;
  onSelectionChange: (devices: Device[]) => void;
  onPageChange: (page: number) => void;
  onSearch: (query: string) => void;
  onSort: (column: string) => void;
  renderKey: number;
  selectable?: boolean; // Add selectable prop
}

export const DeviceTableSection = memo(function DeviceTableSection({
  devices,
  columns,
  loading,
  pagination,
  selectedDevices,
  searchQuery,
  sortBy,
  sortOrder,
  bulkActions,
  onSelectionChange,
  onPageChange,
  onSearch,
  onSort,
  renderKey,
  selectable = true, // Default to true for backward compatibility
}: DeviceTableSectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>All Devices</CardTitle>
        <CardDescription>
          Monitor device status, manage configurations, and perform bulk
          operations
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search Input */}
          <SearchInput
            placeholder="Search devices by name, serial number, model, or location..."
            onSearch={onSearch}
            initialValue={searchQuery}
            className="max-w-md"
          />

          {/* Data Table */}
          <DataTable
            key={renderKey}
            data={devices}
            columns={columns}
            loading={loading}
            selectable={selectable}
            onSelectionChange={onSelectionChange}
            pagination={pagination}
            onPageChange={onPageChange}
            onSort={onSort}
            sortBy={sortBy}
            sortOrder={sortOrder}
            emptyMessage={
              searchQuery && devices.length === 0 && !loading
                ? `No devices found matching "${searchQuery}". Try adjusting your search terms.`
                : devices.length === 0 && !loading
                ? "No devices allocated to your company yet."
                : "No devices found matching your search."
            }
            actions={bulkActions}
          />
        </div>
      </CardContent>
    </Card>
  );
});
