import React from "react";
import { Database } from "lucide-react";
import { SettingsSection } from "../settings-section";
import { SettingsField } from "../settings-field";

interface BackupSettingsData {
  autoBackup?: boolean;
  backupFrequency?: string;
  retentionDays?: number;
  backupLocation?: string;
}

interface BackupSettingsProps {
  data: BackupSettingsData;
  onChange: (field: keyof BackupSettingsData, value: any) => void;
  type: "admin" | "company";
}

const frequencyOptions = [
  { value: "hourly", label: "Every Hour" },
  { value: "daily", label: "Daily" },
  { value: "weekly", label: "Weekly" },
  { value: "monthly", label: "Monthly" },
];

export function BackupSettings({ data, onChange, type }: BackupSettingsProps) {
  // Only show backup settings for admin
  if (type !== "admin") {
    return null;
  }

  return (
    <SettingsSection
      title="Backup Settings"
      description="Configure automatic backup policies and data retention"
      icon={Database}
    >
      {data.autoBackup !== undefined && (
        <SettingsField
          type="switch"
          label="Auto Backup"
          checked={data.autoBackup}
          onChange={(checked) => onChange("autoBackup", checked)}
          description="Automatically backup system data at regular intervals"
        />
      )}

      {data.backupFrequency !== undefined && (
        <SettingsField
          type="select"
          label="Backup Frequency"
          value={data.backupFrequency}
          onChange={(value) => onChange("backupFrequency", value)}
          options={frequencyOptions}
          description="How often automatic backups should be performed"
        />
      )}

      {data.retentionDays !== undefined && (
        <SettingsField
          type="number"
          label="Retention Period (days)"
          value={data.retentionDays}
          onChange={(value) => onChange("retentionDays", parseInt(value) || 30)}
          min={1}
          max={365}
          description="How long to keep backup files before deletion"
        />
      )}

      {data.backupLocation !== undefined && (
        <SettingsField
          type="text"
          label="Backup Location"
          value={data.backupLocation}
          onChange={(value) => onChange("backupLocation", value)}
          placeholder="/backups"
          description="Directory path where backup files are stored"
        />
      )}
    </SettingsSection>
  );
}
