"use client";

import { DashboardLayout } from "@/components/shared/dashboard-layout";
import { AdminReturnBanner } from "@/components/shared/admin-return-banner";
import { DeviceStatsCards } from "@/components/features/device-stats-cards";
import { DeviceTableSection } from "@/components/features/device-table-section";
import { SyncDevicesModal } from "@/components/features/sync-devices-modal";
import { Column } from "@/components/shared/data-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  RefreshCw,
  Eye,
  Settings,
  Smartphone,
  CheckCircle,
  AlertCircle,
  Clock,
  Users,
  Activity,
} from "lucide-react";
import { useCompanyDevicesQuery } from "@/hooks/useOptimizedQueries";

import { useRouter } from "next/navigation";
import { useState } from "react";
import { useCompanyProfileForLayout } from "@/hooks/useCompanyProfileForLayout";

export default function UserDevicesPage() {
  const router = useRouter();
  const { userEmail, userName } = useCompanyProfileForLayout();

  // Local state for sync devices modal
  const [showSyncModal, setShowSyncModal] = useState(false);

  // Use optimized device query
  const [filters, setFilters] = useState({
    page: 1,
    pageSize: 10,
    search: "",
    status: "",
    sortBy: "name",
    sortOrder: "asc" as "asc" | "desc",
  });

  const {
    data: deviceData,
    isLoading: loading,
    refetch,
  } = useCompanyDevicesQuery(filters);

  const devices = deviceData?.devices || [];
  const pagination = deviceData?.pagination || {
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  };

  // Local state
  const [selectedDevices, setSelectedDevices] = useState<any[]>([]);
  const [deviceStatuses, setDeviceStatuses] = useState<Record<string, string>>(
    {}
  );
  const [statusLoading, setStatusLoading] = useState(false);

  // Filter change handler
  const handleFilterChange = (newFilters: Partial<typeof filters>) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  };

  // Page change handler
  const handlePageChange = (page: number) => {
    handleFilterChange({ page });
  };

  // Refresh device status handler
  const handleRefreshAllDevicesStatus = async () => {
    setStatusLoading(true);
    try {
      // Refresh device statuses for all devices
      for (const device of devices) {
        try {
          const response = await fetch(
            `/api/dashboard/devices/${device.serialNumber}/status`
          );
          if (response.ok) {
            const data = await response.json();
            setDeviceStatuses((prev) => ({
              ...prev,
              [device.serialNumber]: data.status,
            }));
          }
        } catch (error) {
          console.error(
            `Failed to get status for device ${device.serialNumber}:`,
            error
          );
        }
      }
    } finally {
      setStatusLoading(false);
    }
  };

  // Single device status refresh handler
  const handleRefreshStatus = async (deviceId: string) => {
    try {
      const device = devices.find(
        (d: any) => d.id === deviceId || d.serialNumber === deviceId
      );
      if (!device) return;

      const response = await fetch(
        `/api/dashboard/devices/${device.serialNumber}/status`
      );
      if (response.ok) {
        const data = await response.json();
        setDeviceStatuses((prev) => ({
          ...prev,
          [device.serialNumber]: data.status,
        }));
      }
    } catch (error) {
      console.error(`Failed to refresh status for device ${deviceId}:`, error);
    }
  };

  // Device sync handler
  const handleDeviceSync = () => {
    setShowSyncModal(true);
  };

  // Search handler
  const handleSearch = (query: string) => {
    handleFilterChange({ search: query });
  };

  // Sort handler
  const handleSort = (column: string) => {
    const newSortOrder =
      filters.sortBy === column && filters.sortOrder === "asc" ? "desc" : "asc";

    handleFilterChange({
      sortBy: column,
      sortOrder: newSortOrder,
    });
  };

  // User device columns (same as admin except allocation column) - compatible with DataTable
  const columns: Column<any>[] = [
    {
      key: "deviceInfo",
      label: "Device Info",
      sortable: true,
      render: (_, device) => {
        if (!device) {
          return <div className="text-sm text-gray-500">Unknown Device</div>;
        }
        return (
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                <Smartphone className="h-5 w-5 text-blue-600" />
              </div>
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-sm font-medium text-gray-900 truncate">
                {String(device.name || "Unknown Device")}
              </div>
              <div className="text-xs text-gray-500">
                Model: {String(device.model || "N/A")}
              </div>
              <div className="text-xs text-gray-500">
                Serial: {String(device.serialNumber || "N/A")}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      key: "location",
      label: "Device Location",
      sortable: true,
      render: (_, device) => (
        <div className="text-sm text-gray-900">
          {String(device?.location || "Not specified")}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      sortable: true,
      render: (_, device) => {
        // Use the real-time status from deviceStatuses state
        const status = deviceStatuses[device.serialNumber] || "UNKNOWN";
        switch (status.toUpperCase()) {
          case "ONLINE":
            return (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Online
              </Badge>
            );
          case "OFFLINE":
            return (
              <Badge variant="destructive">
                <AlertCircle className="h-3 w-3 mr-1" />
                Offline
              </Badge>
            );
          case "LOADING":
            return (
              <Badge variant="secondary">
                <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                Loading...
              </Badge>
            );
          default:
            return (
              <Badge variant="secondary">
                <Clock className="h-3 w-3 mr-1" />
                Unknown
              </Badge>
            );
        }
      },
    },
    {
      key: "userLogCount",
      label: "User & Log Count",
      sortable: true,
      render: (_, device) => {
        if (!device) {
          return <div className="text-sm text-gray-500">N/A</div>;
        }
        return (
          <div className="text-sm text-gray-900">
            <div className="flex items-center">
              <Users className="h-3 w-3 mr-1" />
              Users: {Number(device.totalUsers || 0)}
            </div>
            <div className="flex items-center text-xs text-gray-500">
              <Activity className="h-3 w-3 mr-1" />
              Logs: {Number(device.logCount || 0)}
            </div>
          </div>
        );
      },
    },
    {
      key: "biometricFeatures",
      label: "Features",
      render: (_, device) => {
        if (!device) {
          return <div className="text-sm text-gray-500">N/A</div>;
        }
        return (
          <div className="flex flex-wrap gap-1">
            {device.features?.faceRecognition && (
              <Badge variant="outline" className="text-xs">
                Face
              </Badge>
            )}
            {device.features?.fingerprintScanner && (
              <Badge variant="outline" className="text-xs">
                Finger
              </Badge>
            )}
            {device.features?.cardReader && (
              <Badge variant="outline" className="text-xs">
                Card
              </Badge>
            )}
            {!device.features?.faceRecognition &&
              !device.features?.fingerprintScanner &&
              !device.features?.cardReader && (
                <Badge variant="secondary" className="text-xs">
                  None
                </Badge>
              )}
          </div>
        );
      },
    },
    {
      key: "lastUpdate",
      label: "Last Update",
      sortable: true,
      render: (_, device) => {
        const lastSeen = device.lastSeen || device.updatedAt;
        if (!lastSeen || lastSeen === "null" || lastSeen === "{}") {
          return <span className="text-sm text-gray-500">Never</span>;
        }

        try {
          const date = new Date(lastSeen);
          if (isNaN(date.getTime())) {
            return <span className="text-sm text-gray-500">Invalid date</span>;
          }

          const now = new Date();
          const diffMs = now.getTime() - date.getTime();
          const diffMins = Math.floor(diffMs / (1000 * 60));
          const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
          const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

          let timeAgo;
          if (diffMins < 1) {
            timeAgo = "Just now";
          } else if (diffMins < 60) {
            timeAgo = `${diffMins}m ago`;
          } else if (diffHours < 24) {
            timeAgo = `${diffHours}h ago`;
          } else {
            timeAgo = `${diffDays}d ago`;
          }

          return (
            <div className="text-sm">
              <div className="text-gray-900">{timeAgo}</div>
              <div className="text-xs text-gray-500">
                {date.toLocaleDateString()}
              </div>
            </div>
          );
        } catch (error) {
          return <span className="text-sm text-gray-500">Invalid date</span>;
        }
      },
    },
    {
      key: "actions",
      label: "Actions",
      render: (_, device) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleRefreshStatus(device.id)}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Status
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() =>
                router.push(`/dashboard/devices/${device.serialNumber}/actions`)
              }
            >
              <Settings className="mr-2 h-4 w-4" />
              Configure
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <>
      <AdminReturnBanner />
      <DashboardLayout
        userRole="company"
        userEmail={userEmail}
        userName={userName}
      >
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Device Management
              </h1>
              <p className="text-gray-600">
                Monitor and configure your allocated attendance devices
              </p>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={handleRefreshAllDevicesStatus}
                disabled={statusLoading}
              >
                {statusLoading ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Refresh Status
              </Button>
              <Button onClick={() => refetch()} disabled={loading}>
                {loading ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Reload Data
              </Button>
              <Button onClick={() => setShowSyncModal(true)} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Sync Devices
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <DeviceStatsCards
            devices={devices}
            deviceStatuses={deviceStatuses}
            pagination={pagination}
            loading={loading}
          />

          {/* Device Table */}
          <DeviceTableSection
            devices={devices}
            columns={columns}
            loading={loading}
            pagination={pagination}
            selectedDevices={selectedDevices}
            searchQuery={filters.search}
            sortBy={filters.sortBy}
            sortOrder={filters.sortOrder}
            bulkActions={[]}
            onSelectionChange={setSelectedDevices}
            onPageChange={handlePageChange}
            onSearch={handleSearch}
            onSort={handleSort}
            renderKey={Date.now()}
            selectable={false} // Disable checkboxes for company users
          />

          {/* Sync Devices Modal */}
          <SyncDevicesModal
            open={showSyncModal}
            onOpenChange={setShowSyncModal}
            devices={devices}
            onSuccess={() => {
              refetch(); // Refresh data after sync
            }}
          />
        </div>
      </DashboardLayout>
    </>
  );
}
