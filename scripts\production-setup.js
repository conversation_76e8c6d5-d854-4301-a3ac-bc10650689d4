#!/usr/bin/env node

/**
 * Production Setup Script
 * Run this script after deploying to production to ensure everything is configured correctly
 */

const fs = require("fs");
const path = require("path");
const crypto = require("crypto");

console.log("🚀 Smart Attendance Device Manager - Production Setup");
console.log("=".repeat(50));

// Check if .env.local exists
const envPath = path.join(process.cwd(), ".env.local");
if (!fs.existsSync(envPath)) {
  console.error("❌ .env.local file not found!");
  console.log(
    "📝 Please copy .env.production to .env.local and update the values"
  );
  process.exit(1);
}

// Read environment variables
require("dotenv").config({ path: envPath });

// Validation checks
const requiredEnvVars = [
  "DATABASE_URL",
  "NEXTAUTH_URL",
  "NEXTAUTH_SECRET",
  "JWT_SECRET",
  "NODE_ENV",
];

console.log("🔍 Checking environment variables...");
let missingVars = [];

requiredEnvVars.forEach((varName) => {
  if (!process.env[varName]) {
    missingVars.push(varName);
  }
});

if (missingVars.length > 0) {
  console.error("❌ Missing required environment variables:");
  missingVars.forEach((varName) => {
    console.error(`   - ${varName}`);
  });
  process.exit(1);
}

// Check secret strength
const checkSecretStrength = (secret, name) => {
  if (secret.length < 32) {
    console.warn(`⚠️  ${name} should be at least 32 characters long`);
    return false;
  }
  return true;
};

console.log("🔐 Checking secret strength...");
checkSecretStrength(process.env.NEXTAUTH_SECRET, "NEXTAUTH_SECRET");
checkSecretStrength(process.env.JWT_SECRET, "JWT_SECRET");

// Check uploads directory
const uploadsDir = path.join(process.cwd(), "public", "uploads");
if (!fs.existsSync(uploadsDir)) {
  console.log("📁 Creating uploads directory...");
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Check directory permissions (Unix-like systems)
if (process.platform !== "win32") {
  try {
    fs.accessSync(uploadsDir, fs.constants.W_OK);
    console.log("✅ Uploads directory is writable");
  } catch (err) {
    console.error("❌ Uploads directory is not writable");
    console.log("💡 Run: chmod 755 public/uploads");
  }
}

// Generate secrets if needed
const generateSecret = () => {
  return crypto.randomBytes(32).toString("hex");
};

console.log("🔑 Secret generation examples:");
console.log(`NEXTAUTH_SECRET="${generateSecret()}"`);
console.log(`JWT_SECRET="${generateSecret()}"`);

console.log("\n✅ Production setup checks completed!");
console.log("\n📋 Next steps:");
console.log("1. Run: npm install --production");
console.log("2. Run: npm run db:setup");
console.log("3. Run: npm run build:production");
console.log("4. Start your application");

console.log("\n🔒 Security reminders:");
console.log("- Change default admin password after first login");
console.log("- Ensure SSL certificate is installed");
console.log("- Regular database backups");
console.log("- Monitor application logs");
