"use client";

import { useState, useRef } from "react";
import { DashboardLayout } from "@/components/shared/dashboard-layout";
import { AdminReturnBanner } from "@/components/shared/admin-return-banner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  User,
  Building2,
  Mail,
  Phone,
  MapPin,
  Save,
  Upload,
  AlertCircle,
  Activity,
  Users,
  Smartphone,
  Calendar,
} from "lucide-react";
import { toast } from "react-toastify";
import {
  useCompanyProfileQuery,
  useUpdateCompanyProfileMutation,
} from "@/hooks/queries/useProfileQueries";
import { LoginTokenDisplay } from "@/components/features/security/login-token-display";
import { useCompanyProfileForLayout } from "@/hooks/useCompanyProfileForLayout";

export default function ProfilePage() {
  // Use real-time data hooks
  const { data: profile, isLoading, error } = useCompanyProfileQuery();
  const updateProfileMutation = useUpdateCompanyProfileMutation();
  const { userEmail, userName } = useCompanyProfileForLayout();

  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState<{
    name?: string;
    email?: string;
    avatar?: string;
  }>({});
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSave = async () => {
    try {
      await updateProfileMutation.mutateAsync(editData);
      toast.success("Profile updated successfully");
      setIsEditing(false);
      setEditData({});
    } catch (error) {
      toast.error("Failed to update profile");
    }
  };

  const handleInputChange = (field: "name" | "email", value: string) => {
    setEditData((prev) => ({ ...prev, [field]: value }));
  };

  const handleFileUpload = async (file: File) => {
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size must be less than 5MB");
      return;
    }

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/upload/image", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Upload failed");
      }

      const { url } = await response.json();
      setEditData((prev) => ({ ...prev, avatar: url }));
      toast.success("Profile photo uploaded successfully");
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Failed to upload profile photo");
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleEditClick = () => {
    if (profile) {
      setEditData({
        name: profile.name,
        email: profile.email,
        avatar: profile.avatar,
      });
    }
    setIsEditing(true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "bg-green-100 text-green-800";
      case "DEACTIVATED":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <>
        <AdminReturnBanner />
        <DashboardLayout
          userRole="company"
          userEmail={userEmail}
          userName={userName}
        >
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-8 w-32 mb-2" />
                <Skeleton className="h-4 w-64" />
              </div>
              <Skeleton className="h-10 w-32" />
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <Skeleton className="h-6 w-32" />
                </CardHeader>
                <CardContent>
                  <div className="text-center space-y-4">
                    <Skeleton className="h-20 w-20 rounded-full mx-auto" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-32 mx-auto" />
                      <Skeleton className="h-3 w-16 mx-auto" />
                      <Skeleton className="h-3 w-24 mx-auto" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </DashboardLayout>
      </>
    );
  }

  // Error state
  if (error) {
    return (
      <>
        <AdminReturnBanner />
        <DashboardLayout
          userRole="company"
          userEmail={userEmail}
          userName={userName}
        >
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Profile</h1>
              <p className="text-gray-600">
                Manage your company profile and account information
              </p>
            </div>
            <Card>
              <CardContent className="text-center py-12">
                <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Failed to load profile
                </h3>
                <p className="text-gray-600 mb-4">
                  There was an error loading your profile information.
                </p>
                <Button onClick={() => window.location.reload()}>
                  Try Again
                </Button>
              </CardContent>
            </Card>
          </div>
        </DashboardLayout>
      </>
    );
  }

  if (!profile) {
    return null;
  }

  return (
    <>
      <AdminReturnBanner />
      <DashboardLayout
        userRole="company"
        userEmail={userEmail}
        userName={userName}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Profile</h1>
              <p className="text-gray-600">
                Manage your company profile and account information
              </p>
            </div>
            <div className="flex space-x-3">
              {isEditing ? (
                <>
                  <Button variant="outline" onClick={() => setIsEditing(false)}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={updateProfileMutation.isPending}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {updateProfileMutation.isPending
                      ? "Saving..."
                      : "Save Changes"}
                  </Button>
                </>
              ) : (
                <Button onClick={handleEditClick}>
                  <User className="h-4 w-4 mr-2" />
                  Edit Profile
                </Button>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Profile Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Profile Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="flex flex-col items-center space-y-4">
                    <Avatar className="h-20 w-20">
                      <AvatarImage src={editData.avatar || profile.avatar} />
                      <AvatarFallback className="text-lg">
                        {profile.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    {isEditing && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => fileInputRef.current?.click()}
                          disabled={isUploading}
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          {isUploading ? "Uploading..." : "Upload Photo"}
                        </Button>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleFileSelect}
                          className="hidden"
                        />
                      </>
                    )}
                  </div>
                  <h3 className="font-semibold">{profile.name}</h3>
                  <p className="text-sm text-gray-600">
                    {profile.organizationId}
                  </p>
                  <p className="text-sm text-gray-600">
                    {profile.userType?.replace("_", " ") || "Unknown"}
                  </p>
                  <Badge className={`mt-2 ${getStatusColor(profile.status)}`}>
                    {profile.status}
                  </Badge>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-2 text-gray-400" />
                    <span>{profile.email}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                    <span>Created: {formatDate(profile.createdAt)}</span>
                  </div>
                  <div className="flex items-center">
                    <Activity className="h-4 w-4 mr-2 text-gray-400" />
                    <span>Expires: {formatDate(profile.expiresAt)}</span>
                  </div>
                </div>

                {/* Account Stats */}
                <div className="mt-4 pt-4 border-t space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Total Devices:</span>
                    <span className="font-medium">{profile.totalDevices}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Total Employees:</span>
                    <span className="font-medium">
                      {profile.totalEmployees}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Days Remaining:</span>
                    <span
                      className={`font-medium ${
                        profile.daysRemaining < 7
                          ? "text-red-600"
                          : "text-green-600"
                      }`}
                    >
                      {profile.daysRemaining}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Account Health:</span>
                    <span
                      className={`font-medium ${
                        profile.accountHealth < 50
                          ? "text-red-600"
                          : profile.accountHealth < 80
                          ? "text-yellow-600"
                          : "text-green-600"
                      }`}
                    >
                      {profile.accountHealth}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Profile Form */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Company Information</CardTitle>
                  <CardDescription>
                    Update your company details and contact information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="company-name">Company Name</Label>
                      <Input
                        id="company-name"
                        value={
                          isEditing ? editData.name || "" : profile.name || ""
                        }
                        onChange={(e) =>
                          handleInputChange("name", e.target.value)
                        }
                        disabled={!isEditing}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="organization-id">Organization ID</Label>
                      <Input
                        id="organization-id"
                        value={profile.organizationId || ""}
                        disabled
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={
                          isEditing ? editData.email || "" : profile.email || ""
                        }
                        onChange={(e) =>
                          handleInputChange("email", e.target.value)
                        }
                        disabled={!isEditing}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="user-type">User Type</Label>
                      <Input
                        id="user-type"
                        value={profile.userType?.replace("_", " ") || "Unknown"}
                        disabled
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="status">Account Status</Label>
                      <Input
                        id="status"
                        value={profile.status || ""}
                        disabled
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="last-sync">Last Sync</Label>
                      <Input
                        id="last-sync"
                        value={
                          !profile.lastSyncTime ||
                          profile.lastSyncTime === "Never"
                            ? "Never"
                            : formatDate(profile.lastSyncTime)
                        }
                        disabled
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Login Token */}
          <div className="lg:col-span-3">
            <LoginTokenDisplay loginToken={profile?.loginToken} />
          </div>
        </div>
      </DashboardLayout>
    </>
  );
}
