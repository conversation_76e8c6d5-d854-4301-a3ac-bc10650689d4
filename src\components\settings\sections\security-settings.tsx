import React from "react";
import { Shield } from "lucide-react";
import { SettingsSection } from "../settings-section";
import { SettingsField } from "../settings-field";

interface SecuritySettingsData {
  sessionTimeout?: number;
  maxLoginAttempts?: number;
  passwordMinLength?: number;
  requireTwoFactor?: boolean;
  allowPasswordReset?: boolean;
  ipWhitelist?: string[];
}

interface SecuritySettingsProps {
  data: SecuritySettingsData;
  onChange: (field: keyof SecuritySettingsData, value: any) => void;
  type: "admin" | "company";
}

export function SecuritySettings({ data, onChange, type }: SecuritySettingsProps) {
  // Only show security settings for admin
  if (type !== "admin") {
    return null;
  }

  return (
    <SettingsSection
      title="Security Settings"
      description="Configure authentication and security policies for the system"
      icon={Shield}
    >
      {data.sessionTimeout !== undefined && (
        <SettingsField
          type="number"
          label="Session Timeout (minutes)"
          value={data.sessionTimeout}
          onChange={(value) => onChange("sessionTimeout", parseInt(value) || 30)}
          min={5}
          max={1440}
          description="Automatically log out users after this period of inactivity"
        />
      )}

      {data.maxLoginAttempts !== undefined && (
        <SettingsField
          type="number"
          label="Max Login Attempts"
          value={data.maxLoginAttempts}
          onChange={(value) => onChange("maxLoginAttempts", parseInt(value) || 5)}
          min={3}
          max={20}
          description="Lock account after this many failed login attempts"
        />
      )}

      {data.passwordMinLength !== undefined && (
        <SettingsField
          type="number"
          label="Minimum Password Length"
          value={data.passwordMinLength}
          onChange={(value) => onChange("passwordMinLength", parseInt(value) || 8)}
          min={6}
          max={32}
          description="Minimum number of characters required for passwords"
        />
      )}

      {data.requireTwoFactor !== undefined && (
        <SettingsField
          type="switch"
          label="Require Two-Factor Authentication"
          checked={data.requireTwoFactor}
          onChange={(checked) => onChange("requireTwoFactor", checked)}
          description="Force all users to enable 2FA for enhanced security"
        />
      )}

      {data.allowPasswordReset !== undefined && (
        <SettingsField
          type="switch"
          label="Allow Password Reset"
          checked={data.allowPasswordReset}
          onChange={(checked) => onChange("allowPasswordReset", checked)}
          description="Allow users to reset their passwords via email"
        />
      )}

      {data.ipWhitelist !== undefined && (
        <SettingsField
          type="textarea"
          label="IP Whitelist"
          value={data.ipWhitelist.join("\n")}
          onChange={(value) => onChange("ipWhitelist", value.split("\n").filter(ip => ip.trim()))}
          placeholder="***********/24&#10;10.0.0.0/8"
          description="List of allowed IP addresses or CIDR blocks (one per line). Leave empty to allow all IPs."
          rows={4}
        />
      )}
    </SettingsSection>
  );
}
