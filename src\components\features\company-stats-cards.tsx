"use client";

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Building2, CheckCircle, AlertCircle, Users } from "lucide-react";
import { CompanyWithAllocations } from "@/types";

interface CompanyStatsCardsProps {
  companies: CompanyWithAllocations[];
}

export function CompanyStatsCards({ companies }: CompanyStatsCardsProps) {
  const totalCompanies = companies.length;
  const activeCompanies = companies.filter((c) => c.status === "ACTIVE").length;
  const inactiveCompanies = companies.filter(
    (c) => c.status === "DEACTIVATED"
  ).length;
  const totalAllocatedDevices = companies.reduce(
    (sum, company) => sum + (company.allocations?.length || 0),
    0
  );

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Companies</CardTitle>
          <Building2 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalCompanies}</div>
          <p className="text-xs text-muted-foreground">Registered companies</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Active Companies
          </CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {activeCompanies}
          </div>
          <p className="text-xs text-muted-foreground">Currently active</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Inactive Companies
          </CardTitle>
          <AlertCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">
            {inactiveCompanies}
          </div>
          <p className="text-xs text-muted-foreground">Need attention</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Allocated Devices
          </CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {totalAllocatedDevices}
          </div>
          <p className="text-xs text-muted-foreground">
            Total device allocations
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
