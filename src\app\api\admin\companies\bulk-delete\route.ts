import { NextRequest, NextResponse } from "next/server";
import { verifyAdminAuth } from "@/lib/auth";
import { AuthenticatedRequest } from "@/lib/auth/middleware";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

const bulkDeleteSchema = z.object({
  companyIds: z.array(z.string()).min(1, "At least one company ID is required"),
});

async function bulkDeleteCompanies(req: AuthenticatedRequest) {
  try {
    const body = await req.json();
    const validatedData = bulkDeleteSchema.parse(body);

    // Check if companies exist and get allocation counts
    const existingCompanies = await prisma.company.findMany({
      where: {
        id: {
          in: validatedData.companyIds,
        },
      },
      include: {
        allocations: true,
      },
    });

    if (existingCompanies.length === 0) {
      return NextResponse.json(
        { error: "No companies found with the provided IDs" },
        { status: 404 }
      );
    }

    // Calculate total deallocated devices
    const totalDeallocatedDevices = existingCompanies.reduce(
      (total, company) => total + company.allocations.length,
      0
    );

    // Delete companies (this will cascade delete allocations due to onDelete: Cascade)
    const result = await prisma.company.deleteMany({
      where: {
        id: {
          in: validatedData.companyIds,
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: `${result.count} companies deleted successfully`,
      data: {
        deletedCount: result.count,
        deallocatedDevices: totalDeallocatedDevices,
      },
    });
  } catch (error) {
    console.error("Bulk delete error:", error);

    if (error instanceof Error && error.name === "ZodError") {
      return NextResponse.json(
        { error: "Invalid input data" },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  const authResult = await verifyAdminAuth(request);
  if (!authResult.success) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  return bulkDeleteCompanies(request as AuthenticatedRequest);
}
