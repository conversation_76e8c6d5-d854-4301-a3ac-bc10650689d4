import { NextRequest, NextResponse } from "next/server";
import { verifyJWT } from "@/lib/auth";

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Create response
  let response = NextResponse.next();

  // Add security headers for production
  if (process.env.NODE_ENV === "production") {
    response.headers.set("X-Frame-Options", "DENY");
    response.headers.set("X-Content-Type-Options", "nosniff");
    response.headers.set("Referrer-Policy", "origin-when-cross-origin");
    response.headers.set("X-XSS-Protection", "1; mode=block");

    // Content Security Policy
    const csp = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-eval' 'unsafe-inline'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: blob:",
      "font-src 'self'",
      "connect-src 'self'",
      "frame-ancestors 'none'",
    ].join("; ");

    response.headers.set("Content-Security-Policy", csp);

    // HSTS for HTTPS
    if (request.nextUrl.protocol === "https:") {
      response.headers.set(
        "Strict-Transport-Security",
        "max-age=31536000; includeSubDomains"
      );
    }
  }

  // Public routes that don't require authentication
  const publicRoutes = ["/", "/login"];

  // Check if the current path is public
  if (publicRoutes.includes(pathname)) {
    return response;
  }

  // Get token from cookie
  const token = request.cookies.get("auth-token")?.value;

  // If no token, redirect to login
  if (!token) {
    const loginUrl = new URL("/login", request.url);
    response = NextResponse.redirect(loginUrl);
    return response;
  }

  // Verify token
  const payload = verifyJWT(token);

  // If invalid token, redirect to login
  if (!payload) {
    const loginUrl = new URL("/login", request.url);
    response = NextResponse.redirect(loginUrl);

    // Clear invalid token
    response.cookies.set("auth-token", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 0,
    });

    return response;
  }

  // Role-based route protection
  if (pathname.startsWith("/admin")) {
    if (payload.role !== "admin") {
      // Non-admin trying to access admin routes
      const dashboardUrl = new URL("/dashboard", request.url);
      response = NextResponse.redirect(dashboardUrl);
      return response;
    }
  } else if (pathname.startsWith("/dashboard")) {
    if (payload.role !== "company") {
      // Non-company user trying to access company routes
      const adminUrl = new URL("/admin/dashboard", request.url);
      response = NextResponse.redirect(adminUrl);
      return response;
    }
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};
