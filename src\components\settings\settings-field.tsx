import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface BaseFieldProps {
  label: string;
  description?: string;
  required?: boolean;
  className?: string;
  error?: string;
}

interface TextFieldProps extends BaseFieldProps {
  type: "text" | "email" | "password" | "number" | "time";
  value: string | number;
  onChange: (value: string) => void;
  placeholder?: string;
  min?: number;
  max?: number;
}

interface SwitchFieldProps extends BaseFieldProps {
  type: "switch";
  checked: boolean;
  onChange: (checked: boolean) => void;
}

interface SelectFieldProps extends BaseFieldProps {
  type: "select";
  value: string;
  onChange: (value: string) => void;
  options: { value: string; label: string }[];
  placeholder?: string;
}

interface TextareaFieldProps extends BaseFieldProps {
  type: "textarea";
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
}

type SettingsFieldProps =
  | TextFieldProps
  | SwitchFieldProps
  | SelectFieldProps
  | TextareaFieldProps;

export function SettingsField(props: SettingsFieldProps) {
  const { label, description, required, className = "", error } = props;

  const renderField = () => {
    switch (props.type) {
      case "text":
      case "email":
      case "password":
      case "number":
      case "time":
        return (
          <Input
            type={props.type}
            value={props.value}
            onChange={(e) => props.onChange(e.target.value)}
            placeholder={props.placeholder}
            min={props.min}
            max={props.max}
            required={required}
            className={error ? "border-red-500" : ""}
          />
        );

      case "switch":
        return (
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>{label}</Label>
              {description && (
                <p className="text-sm text-gray-600">{description}</p>
              )}
            </div>
            <Switch checked={props.checked} onCheckedChange={props.onChange} />
          </div>
        );

      case "select":
        return (
          <Select value={props.value} onValueChange={props.onChange}>
            <SelectTrigger>
              <SelectValue placeholder={props.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {props.options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case "textarea":
        return (
          <Textarea
            value={props.value}
            onChange={(e) => props.onChange(e.target.value)}
            placeholder={props.placeholder}
            rows={props.rows || 3}
            required={required}
          />
        );

      default:
        return null;
    }
  };

  if (props.type === "switch") {
    return <div className={className}>{renderField()}</div>;
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor={label.toLowerCase().replace(/\s+/g, "-")}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      {renderField()}
      {description && (props as { type: string }).type !== "switch" && (
        <p className="text-sm text-gray-600">{description}</p>
      )}
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  );
}
