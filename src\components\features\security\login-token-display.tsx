"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Co<PERSON>, Eye, EyeOff, Key, CheckCircle } from "lucide-react";
import { toast } from "sonner";

interface LoginTokenDisplayProps {
  loginToken?: string;
}

export function LoginTokenDisplay({ loginToken }: LoginTokenDisplayProps) {
  const [showToken, setShowToken] = useState(false);
  const [copied, setCopied] = useState(false);

  const handleCopyToken = async () => {
    if (!loginToken) {
      toast.error("No login token available");
      return;
    }

    try {
      await navigator.clipboard.writeText(loginToken);
      setCopied(true);
      toast.success("Login token copied to clipboard!");
      
      // Reset copied state after 2 seconds
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error("Failed to copy login token");
    }
  };

  const toggleTokenVisibility = () => {
    setShowToken(!showToken);
  };

  const maskedToken = loginToken 
    ? `${loginToken.substring(0, 8)}${"*".repeat(Math.max(0, loginToken.length - 16))}${loginToken.substring(loginToken.length - 8)}`
    : "No token available";

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center space-x-2">
          <Key className="h-5 w-5 text-blue-600" />
          <CardTitle>Login Token</CardTitle>
        </div>
        <CardDescription>
          Your secure login token for API access and direct authentication. Keep this token safe and do not share it with unauthorized users.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {loginToken ? (
          <>
            <div className="space-y-2">
              <Label htmlFor="login-token">Login Token</Label>
              <div className="flex space-x-2">
                <Input
                  id="login-token"
                  type="text"
                  value={showToken ? loginToken : maskedToken}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={toggleTokenVisibility}
                  title={showToken ? "Hide token" : "Show token"}
                >
                  {showToken ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={handleCopyToken}
                  title="Copy token to clipboard"
                  className={copied ? "bg-green-50 border-green-200" : ""}
                >
                  {copied ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Key className="h-5 w-5 text-blue-600 mt-0.5" />
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-900">How to use your login token:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Use this token for API authentication</li>
                    <li>• Include it in the Authorization header: <code className="bg-blue-100 px-1 rounded">Bearer {loginToken ? loginToken.substring(0, 12) + "..." : "your-token"}</code></li>
                    <li>• Keep this token secure and do not share it</li>
                    <li>• Contact your administrator if you need a new token</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Badge variant="default" className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Active Token
                </Badge>
              </div>
              <p className="text-sm text-gray-500">
                Token length: {loginToken.length} characters
              </p>
            </div>
          </>
        ) : (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Key className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-900">No Login Token Available</h4>
                <p className="text-sm text-yellow-800 mt-1">
                  Contact your administrator to generate a login token for your account.
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
