import axios, { AxiosInstance, AxiosResponse } from 'axios'

// Types for external API
export interface DeviceInfo {
  'MACHINE ID': number
  'MACHINE NAME': string
  'MODEL NO': string
  'SERIAL NO': string
  'TIME ZONE': number
  'MACHINE LOCATION': string
  'STATUS': Record<string, unknown>
  'LOG COUNT': number
  'USER COUNT': number
  'LAST_UPDATE_DATE': string
  'ISONLINECHECK': Record<string, unknown>
  'ISALIAS': number
  'ISFACE': number
  'ISFINGER': number
  'ISCARD': number
  'ISPASSWORD': number
  'FINGERPRINTCOUNT': number
  'FACECOUNT': number
  'PASSWORDCOUNT': number
  'CARDCOUNT': number
  'MODEL NAME': string
  'ENDPOINT URL': string
}

export interface TimeZone {
  TIMEZONE_ID: number
  TIMEZONE_NAME: string
  'TIMEZONE TIME': string
}

export interface AttendanceLog {
  EnrollmentNo: string
  DeviceSlno: string
  PunchDateTime: string
  inout_value: number
  'Mode value': number
  Event_value: number
}

export interface UserData {
  RowId: number
  DevicesSlno: string
  EnrollmentNo: string
  Name: string
  Admin: number
  FingerData: Record<string, unknown>
  FaceData: Record<string, unknown>
  Privilege: Record<string, unknown>
  Enabled: Record<string, unknown>
  Card: Record<string, unknown>
  PWD: Record<string, unknown>
}

export interface ApiResponse<T = unknown> {
  status: string
  msg: string
  data: T
}

class ExternalDeviceAPI {
  private client: AxiosInstance

  constructor(baseURL?: string) {
    this.client = axios.create({
      baseURL: baseURL || process.env.EXTERNAL_API_BASE_URL || 'http://103.240.90.194:7766/api/v1/',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        return response
      },
      (error) => {
        return Promise.reject(error)
      }
    )
  }

  private async makeRequest<T>(method: string, data: unknown[] = [{}]): Promise<ApiResponse<T>> {
    try {
      // Make POST request to BASE_URL + METHOD_NAME with data as body
      const response: AxiosResponse<ApiResponse<T>> = await this.client.post(method, data)
      return response.data
    } catch (error) {
      throw error
    }
  }

  // Admin-only methods
  async selectTimeZone(): Promise<ApiResponse<TimeZone[]>> {
    return this.makeRequest<TimeZone[]>('SELECTTIMEZONE')
  }

  async addEditDevice(devices: Array<{
    DEVICESLNO: string
    DEVICENAME: string
    MODELNO: string
    TIMEZONEID: string
    LOCATION: string
    'ENDPOINT URL'?: string
  }>): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('ADDEDITDEVICE', devices)
  }

  async deleteDevice(devices: Array<{ DEVICESLNO: string }>): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('DELETDEVICE', devices)
  }

  async selectDeviceList(): Promise<ApiResponse<DeviceInfo[]>> {
    return this.makeRequest<DeviceInfo[]>('SELECTDEVICELIST')
  }

  // Common methods (both admin and user)
  async getDeviceOnlineStatus(deviceSlno: string): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('GETDEVICEONLINESTATUS', [{ DEVICESLNO: deviceSlno }])
  }

  async sendCmdAttendanceLog(devices: Array<{
    DEVICESLNO: string
    FROMDATE: string
    TODATE: string
  }>): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('SENDCMDATTENDANCELOG', devices)
  }

  async getAttendanceLog(devices: Array<{
    DEVICESLNO: string
    FROMDATE: string
    TODATE: string
  }>): Promise<ApiResponse<AttendanceLog[]>> {
    return this.makeRequest<AttendanceLog[]>('GETATTENDANCELOG', devices)
  }

  async getCmdUserData(devices: Array<{ DEVICESLNO: string }>): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('GETCMDUSERDATA', devices)
  }

  async getUserData(devices: Array<{ DEVICESLNO: string }>): Promise<ApiResponse<UserData[]>> {
    return this.makeRequest<UserData[]>('GETUSERDATA', devices)
  }

  async getSingleUserData(deviceSlno: string, userId: string): Promise<ApiResponse<UserData[]>> {
    return this.makeRequest<UserData[]>(
      'GETSINGLEUSERDATA',
      [{ DEVICESLNO: deviceSlno, USERID: userId }]
    )
  }

  async syncDevice(
    sourceDeviceSlno: string,
    destinationDeviceSlno: string
  ): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('SYNCDEVICE', [
      {
        SOURCEDEVICESLNO: sourceDeviceSlno,
        DESTINATIONDEVICESLNO: destinationDeviceSlno,
      },
    ])
  }

  async addUser(params: {
    DEVICESLNO: string
    USERID: string
    USERNAME: string
    FINGERDATA?: string
    FACEDATA?: string
    CARDDATA?: string
    PASSWORDDATA?: string
    ADMIN?: string
  }): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('ADDUSER', [params])
  }

  async addUserWithValidity(params: {
    DEVICESLNO: string
    USERID: string
    USERNAME: string
    FINGERDATA?: string
    FACEDATA?: string
    CARDDATA?: string
    PASSWORDDATA?: string
    ADMIN?: string
    FROMDATE: string
    TODATE: string
  }): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('ADDUSERWITHVALIDITY', [params])
  }

  async deleteUser(deviceSlno: string, userId: string): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('DELETEUSER', [{ DEVICESLNO: deviceSlno, USERID: userId }])
  }

  async enableUser(devices: Array<{
    DEVICESLNO: string
    USERID: string
    STATUS: 'ENABLE' | 'DISABLE'
  }>): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('ENABLEUSER', devices)
  }

  async addAdmin(devices: Array<{
    DEVICESLNO: string
    USERID: string
  }>): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('ADDADMIN', devices)
  }

  async deleteAdmin(devices: Array<{
    DEVICESLNO: string
    USERID: string
  }>): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('DELETEADMIN', devices)
  }

  async clearAdmin(deviceSlno: string): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('CLEARADMIN', [{ DEVICESLNO: deviceSlno }])
  }

  async clearAllUser(deviceSlno: string): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('CLEARALLUSER', [{ DEVICESLNO: deviceSlno }])
  }

  async setValidity(params: {
    DEVICESLNO: string
    USERID: string
    FROMDATE: string
    TODATE: string
  }): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('SETVALIDITY', [params])
  }

  async remoteRegisterFace(params: {
    DEVICESLNO: string
    USERID: string
    USERNAME: string
  }): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('REMOTEREGISTERFACE', [params])
  }

  async remoteRegisterFinger(params: {
    DEVICESLNO: string
    USERID: string
    USERNAME: string
  }): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('REMOTEREGISTERFINGER', [params])
  }

  async openDoor(deviceSlno: string): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('OPENDOOR', [{ DEVICESLNO: deviceSlno }])
  }

  async clearAllAttendanceLog(deviceSlno: string): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('CLEARALLATTENDANCELOG', [{ DEVICESLNO: deviceSlno }])
  }

  async rebootDevice(deviceSlno: string): Promise<ApiResponse<null>> {
    return this.makeRequest<null>('REBOOTDEVICE', [{ DEVICESLNO: deviceSlno }])
  }
}

export const externalDeviceAPI = new ExternalDeviceAPI()

export const getExternalDeviceAPI = (baseURL?: string) => {
  if (baseURL) {
    return new ExternalDeviceAPI(baseURL)
  }
  return externalDeviceAPI
}