"use client";

import { use } from "react";
import { DashboardLayout } from "@/components/shared/dashboard-layout";
import { AdminReturnBanner } from "@/components/shared/admin-return-banner";
import { DeviceActionsContent } from "@/components/features/device-actions-content";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCompanyProfileForLayout } from "@/hooks/useCompanyProfileForLayout";

interface DeviceActionsPageProps {
  params: Promise<{
    deviceId: string;
  }>;
}

export default function UserDeviceActionsPage({
  params,
}: DeviceActionsPageProps) {
  const router = useRouter();
  const { deviceId } = use(params);
  const { userEmail, userName } = useCompanyProfileForLayout();

  return (
    <>
      <AdminReturnBanner />
      <DashboardLayout
        userRole="company"
        userEmail={userEmail}
        userName={userName}
      >
        <div className="space-y-6">
          {/* Header with back button */}
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Device Actions - {deviceId}
              </h1>
              <p className="text-gray-600">
                View device information and manage users
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
              className="self-end-safe flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Devices
            </Button>
          </div>

          {/* Device Actions Content - Same as admin but with restrictions */}
          <DeviceActionsContent deviceId={deviceId} userRole="company" />
        </div>
      </DashboardLayout>
    </>
  );
}
