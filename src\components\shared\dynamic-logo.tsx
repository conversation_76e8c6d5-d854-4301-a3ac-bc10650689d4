"use client";

import { Shield } from "lucide-react";
import { useBranding } from "@/hooks/useBranding";
import { cn } from "@/lib/utils";

interface DynamicLogoProps {
  className?: string;
  fallbackClassName?: string;
  alt?: string;
  showFallback?: boolean;
}

export function DynamicLogo({ 
  className = "h-8 w-8", 
  fallbackClassName = "h-8 w-8 text-blue-600",
  alt = "Logo",
  showFallback = true
}: DynamicLogoProps) {
  const { logoUrl, isLoading } = useBranding();

  // Show fallback while loading or if no logo is set
  if (isLoading || !logoUrl) {
    return showFallback ? (
      <Shield className={cn(fallbackClassName)} />
    ) : null;
  }

  return (
    <img
      src={logoUrl}
      alt={alt}
      className={cn("object-contain", className)}
      onError={(e) => {
        // Fallback to Shield icon if image fails to load
        if (showFallback) {
          e.currentTarget.style.display = "none";
          const fallbackElement = e.currentTarget.nextElementSibling;
          if (fallbackElement) {
            fallbackElement.classList.remove("hidden");
          }
        }
      }}
    />
  );
}

// Component that includes both image and fallback in the same container
export function DynamicLogoWithFallback({ 
  className = "h-8 w-8", 
  fallbackClassName = "h-8 w-8 text-blue-600",
  alt = "Logo"
}: DynamicLogoProps) {
  const { logoUrl, isLoading } = useBranding();

  return (
    <>
      {logoUrl && !isLoading && (
        <img
          src={logoUrl}
          alt={alt}
          className={cn("object-contain", className)}
          onError={(e) => {
            // Fallback to Shield icon if image fails to load
            e.currentTarget.style.display = "none";
            const fallbackElement = e.currentTarget.nextElementSibling;
            if (fallbackElement) {
              fallbackElement.classList.remove("hidden");
            }
          }}
        />
      )}
      <Shield
        className={cn(fallbackClassName, logoUrl && !isLoading ? "hidden" : "")}
      />
    </>
  );
}
