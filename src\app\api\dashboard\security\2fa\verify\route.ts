import { NextRequest, NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { authenticator } from 'otplib'
import { prisma } from '@/lib/prisma'

interface Verify2FARequest {
  token: string
  secret: string
}

async function verify2FA(req: AuthenticatedRequest) {
  try {
    const user = req.user!
    const body: Verify2FARequest = await req.json()
    const { token, secret } = body
    
    if (user.role !== 'company') {
      return NextResponse.json(
        { error: 'Company access required' },
        { status: 403 }
      )
    }

    if (!token || !secret) {
      return NextResponse.json(
        { error: 'Token and secret are required' },
        { status: 400 }
      )
    }

    // Verify the token
    const isValid = authenticator.verify({ token, secret })
    
    if (!isValid) {
      return NextResponse.json(
        { error: 'Invalid verification code' },
        { status: 400 }
      )
    }

    try {
      // Enable 2FA for the company
      await prisma.company.update({
        where: { id: user.companyId },
        data: {
          twoFactorSecret: secret,
          twoFactorEnabled: true,
          updatedAt: new Date()
        }
      })
    } catch (dbError) {
      console.warn('Database not available for 2FA verification:', dbError)
      // Continue with success response even if database update fails
    }

    return NextResponse.json({
      success: true,
      message: '2FA has been successfully enabled'
    })
    
  } catch (error) {
    console.error('Company 2FA verification error:', error)
    return NextResponse.json(
      { error: 'Failed to verify 2FA' },
      { status: 500 }
    )
  }
}

export const POST = withAuth(verify2FA)
