import { NextRequest, NextResponse } from 'next/server';
import { externalDeviceAPI, getExternalDeviceAPI } from '@/lib/api/external-device-api';
import { verifyAdminAuth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// Save external API response to database cache
async function saveDeviceApiResponse(externalDevice: Record<string, any>) {
  try {
    const serialNo = externalDevice['SERIAL_NO'] || externalDevice['SERIAL NO'];
    if (!serialNo) return;

    // Helper function to safely extract string values
    const safeString = (value: any, fallback: string = 'Unknown'): string => {
      if (typeof value === 'string') return value;
      if (typeof value === 'number') return value.toString();
      return fallback;
    };

    // Helper function to safely extract number values
    const safeNumber = (value: any, fallback: number = 0): number => {
      if (typeof value === 'number') return value;
      if (typeof value === 'string') {
        const parsed = parseInt(value);
        return isNaN(parsed) ? fallback : parsed;
      }
      return fallback;
    };

    const deviceData = {
      deviceSerialNumber: serialNo,
      apiResponse: externalDevice,
      deviceName: safeString(externalDevice['MACHINE_NAME'] || externalDevice['MACHINE NAME'], 'Unnamed Device'),
      modelNo: safeString(externalDevice['MODEL_NO'] || externalDevice['MODEL NO'], 'Unknown'),
      modelName: safeString(externalDevice['MODEL_NAME'] || externalDevice['MODEL NAME'], 'Unknown'),
      location: safeString(externalDevice['MACHINE_LOCATION'] || externalDevice['MACHINE LOCATION'], 'Not specified'),
      userCount: safeNumber(externalDevice['USER_COUNT'] || externalDevice['USER COUNT'], 0),
      logCount: safeNumber(externalDevice['LOG_COUNT'] || externalDevice['LOG COUNT'], 0),
      timeZone: safeNumber(externalDevice['TIME_ZONE'] || externalDevice['TIME ZONE'], 57),
      endpointUrl: safeString(externalDevice['ENDPOINT_URL'] || externalDevice['ENDPOINT URL'], ''),
      isface: externalDevice['ISFACE'] === 1,
      isfinger: externalDevice['ISFINGER'] === 1,
      iscard: externalDevice['ISCARD'] === 1,
      ispassword: externalDevice['ISPASSWORD'] === 1,
      fingerprintCount: safeNumber(externalDevice['FINGERPRINTCOUNT'], 0),
      faceCount: safeNumber(externalDevice['FACECOUNT'], 0),
      cardCount: safeNumber(externalDevice['CARDCOUNT'], 0),
      passwordCount: safeNumber(externalDevice['PASSWORDCOUNT'], 0),
      lastApiUpdate: new Date(),
    };



    await prisma.deviceApiCache.upsert({
      where: { deviceSerialNumber: serialNo },
      update: deviceData,
      create: deviceData,
    });
  } catch (error) {
    console.error('Error saving device API response:', error);
  }
}

// Transform external API device data to our internal format
function transformDeviceData(externalDevice: Record<string, any>, allocationMap: Map<string, string>) {
  // Handle both field name formats from the API
  const machineName = externalDevice['MACHINE_NAME'] || externalDevice['MACHINE NAME'];
  const serialNo = externalDevice['SERIAL_NO'] || externalDevice['SERIAL NO'];
  const modelNo = externalDevice['MODEL_NO'] || externalDevice['MODEL NO'];
  const modelName = externalDevice['MODEL_NAME'] || externalDevice['MODEL NAME'];
  const location = externalDevice['MACHINE_LOCATION'] || externalDevice['MACHINE LOCATION'];
  const endpointUrl = externalDevice['ENDPOINT_URL'] || externalDevice['ENDPOINT URL'];
  const lastUpdate = externalDevice['LAST_UPDATE_DATE'];
  const timeZone = externalDevice['TIME_ZONE'] || externalDevice['TIME ZONE'];
  const logCount = externalDevice['LOG_COUNT'] || externalDevice['LOG COUNT'];
  const userCount = externalDevice['USER_COUNT'] || externalDevice['USER COUNT'];

  return {
    id: serialNo, // Use serial number as ID
    name: machineName || 'Unnamed Device',
    serialNumber: serialNo,
    model: modelNo || 'Unknown',
    modelName: modelName || 'Unknown',
    location: location || 'Not specified',
    ipAddress: (() => {
      try {
        if (typeof endpointUrl === 'string' && endpointUrl.includes('://') && !endpointUrl.includes(':null')) {
          return new URL(endpointUrl).hostname;
        }
        return 'Unknown';
      } catch {
        return 'Unknown';
      }
    })(),
    port: (() => {
      try {
        if (typeof endpointUrl === 'string' && endpointUrl.includes(':') && !endpointUrl.includes(':null')) {
          const portStr = endpointUrl.split(':').pop() || '4370';
          const port = parseInt(portStr);
          return isNaN(port) ? 4370 : port;
        }
        return 4370;
      } catch {
        return 4370;
      }
    })(),
    status: 'UNKNOWN', // Will be updated with real-time status from GETDEVICEONLINESTATUS
    lastSeen: typeof lastUpdate === 'string' && lastUpdate !== '{}' ? lastUpdate : null,
    firmwareVersion: 'Unknown', // Not provided by external API
    totalUsers: typeof userCount === 'number' ? userCount : 0,
    timeZone: typeof timeZone === 'number' ? timeZone : 57,
    logCount: typeof logCount === 'number' ? logCount : 0,
    features: {
      faceRecognition: externalDevice['ISFACE'] === 1,
      fingerprintScanner: externalDevice['ISFINGER'] === 1,
      cardReader: externalDevice['ISCARD'] === 1,
      temperatureCheck: false, // Not provided by external API
    },
    biometricCounts: {
      fingerprints: externalDevice['FINGERPRINTCOUNT'] || 0,
      faces: externalDevice['FACECOUNT'] || 0,
      cards: externalDevice['CARDCOUNT'] || 0,
      passwords: externalDevice['PASSWORDCOUNT'] || 0,
    },
    createdAt: new Date().toISOString(),
    updatedAt: typeof lastUpdate === 'string' && lastUpdate !== '{}' ? lastUpdate : new Date().toISOString(),
    allocatedCompany: allocationMap.get(serialNo) || null, // Get company name from allocation map
  };
}

// POST /api/admin/devices - Get all devices (using POST as external API requires)
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';
    const sortBy = searchParams.get('sortBy') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';
    const allocation = searchParams.get('allocation') || 'all';
    const companyId = searchParams.get('companyId');

    let api = externalDeviceAPI;

    if (companyId) {
      const company = await prisma.company.findUnique({
        where: { id: companyId },
      });

      if (company && company.externalApiEndpoint) {
        api = getExternalDeviceAPI(company.externalApiEndpoint);
      }
    }

    // Fetch device allocations from database
    const allocations = await prisma.allocation.findMany({
      include: {
        company: {
          select: {
            name: true
          }
        }
      }
    });

    // Create a map of device serial -> company name for quick lookup
    const allocationMap = new Map<string, string>();
    allocations.forEach(allocation => {
      allocationMap.set(allocation.deviceSerialNo, allocation.company.name);
    });

    // Fetch devices from external API (POST request)
    const response = await api.selectDeviceList();

    if (response.status !== '200') {
      return NextResponse.json(
        { error: `External API Error: ${response.msg || 'Unknown error'}` },
        { status: 500 }
      );
    }

    if (!response.data || !Array.isArray(response.data)) {
      return NextResponse.json(
        { error: 'Invalid response data from external API' },
        { status: 500 }
      );
    }

    // Save external API responses to database cache for future use
    await Promise.all(response.data.map(saveDeviceApiResponse));

    // Transform the data (devices will have UNKNOWN status initially)
    let devices = response.data.map(device => transformDeviceData(device, allocationMap));

    // Note: Device status checking is now handled separately via /api/admin/devices/status
    // This improves performance by not blocking the initial table load

    // Apply filters
    if (search) {
      devices = devices.filter(device =>
        device.name.toLowerCase().includes(search.toLowerCase()) ||
        device.serialNumber.toLowerCase().includes(search.toLowerCase()) ||
        device.location.toLowerCase().includes(search.toLowerCase()) ||
        device.model.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Remove status filtering here, as it's handled client-side with a separate API call
    // if (status !== 'all') {
    //   devices = devices.filter(device => 
    //     device.status.toLowerCase() === status.toLowerCase()
    //   );
    // }

    // Apply allocation filtering
    if (allocation !== 'all') {
      if (allocation === 'allocated') {
        devices = devices.filter(device => device.allocatedCompany);
      } else if (allocation === 'unallocated') {
        devices = devices.filter(device => !device.allocatedCompany);
      }
    }

    // Calculate allocated devices from the full list
    const allocatedDevicesCount = devices.filter(device => device.allocatedCompany).length;

    // Placeholder for online/offline counts (will be populated by client-side status checks)
    const onlineDevicesCount = 0; 
    const offlineDevicesCount = 0;

    // Apply search filtering
    if (search && search.trim() !== '') {
      const searchLower = search.toLowerCase().trim();
      devices = devices.filter(device => {
        return (
          device.name.toLowerCase().includes(searchLower) ||
          device.serialNumber.toLowerCase().includes(searchLower) ||
          device.model.toLowerCase().includes(searchLower) ||
          device.location.toLowerCase().includes(searchLower) ||
          (device.modelName && device.modelName.toLowerCase().includes(searchLower))
        );
      });
    }

    // Apply sorting
    devices.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortBy) {
        case 'name':
        case 'deviceInfo':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'serialNumber':
          aValue = a.serialNumber.toLowerCase();
          bValue = b.serialNumber.toLowerCase();
          break;
        case 'location':
          aValue = a.location.toLowerCase();
          bValue = b.location.toLowerCase();
          break;
        case 'model':
          aValue = a.model.toLowerCase();
          bValue = b.model.toLowerCase();
          break;
        case 'totalUsers':
          aValue = a.totalUsers;
          bValue = b.totalUsers;
          break;
        case 'logCount':
          aValue = a.logCount;
          bValue = b.logCount;
          break;
        case 'userLogCount':
          // Sort by total users first, then by log count
          aValue = a.totalUsers * 1000000 + a.logCount;
          bValue = b.totalUsers * 1000000 + b.logCount;
          break;
        // case 'status': // Status sorting is handled client-side
        //   aValue = a.status.toLowerCase();
        //   bValue = b.status.toLowerCase();
        //   break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (aValue < bValue) {
        return sortOrder === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortOrder === 'asc' ? 1 : -1;
      }
      return 0;
    });

    // Apply pagination
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedDevices = devices.slice(startIndex, endIndex);

    return NextResponse.json({
      devices: paginatedDevices,
      pagination: {
        page,
        pageSize,
        total: devices.length,
        totalPages: Math.ceil(devices.length / pageSize),
      },
      summary: {
        totalDevices: devices.length,
        onlineDevices: onlineDevicesCount,
        offlineDevices: offlineDevicesCount,
        allocatedDevices: allocatedDevicesCount,
      },
    });

  } catch (error) {
    console.error('Error fetching devices:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/devices - Add new device
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { deviceSlno, deviceName, modelNo, timeZoneId, location, endpointUrl } = body;

    // Validate required fields
    if (!deviceSlno || !deviceName || !modelNo || !timeZoneId) {
      return NextResponse.json(
        { error: 'Missing required fields: deviceSlno, deviceName, modelNo, timeZoneId' },
        { status: 400 }
      );
    }

    // Add device using external API
    const response = await externalDeviceAPI.addEditDevice([{
      DEVICESLNO: deviceSlno,
      DEVICENAME: deviceName,
      MODELNO: modelNo,
      TIMEZONEID: timeZoneId,
      LOCATION: location || '',
      'ENDPOINT URL': endpointUrl || '',
    }]);

    if (response.status !== '200') {
      return NextResponse.json(
        { error: response.msg || 'Failed to add device' },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      message: 'Device added successfully',
      data: response.data 
    });

  } catch (error) {
    console.error('Error adding device:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/devices - Update device
export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { deviceSlno, deviceName, modelNo, timeZoneId, location, endpointUrl } = body;

    // Validate required fields
    if (!deviceSlno || !deviceName || !modelNo || !timeZoneId) {
      return NextResponse.json(
        { error: 'Missing required fields: deviceSlno, deviceName, modelNo, timeZoneId' },
        { status: 400 }
      );
    }

    // Update device using external API (same endpoint as add)
    const response = await externalDeviceAPI.addEditDevice([{
      DEVICESLNO: deviceSlno,
      DEVICENAME: deviceName,
      MODELNO: modelNo,
      TIMEZONEID: timeZoneId,
      LOCATION: location || '',
      'ENDPOINT URL': endpointUrl || '',
    }]);

    if (response.status !== '200') {
      return NextResponse.json(
        { error: response.msg || 'Failed to update device' },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      message: 'Device updated successfully',
      data: response.data 
    });

  } catch (error) {
    console.error('Error updating device:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/devices - Delete device
export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const deviceSlno = searchParams.get('deviceSlno');

    if (!deviceSlno) {
      return NextResponse.json(
        { error: 'Device serial number is required' },
        { status: 400 }
      );
    }

    // Delete device using external API
    const response = await externalDeviceAPI.deleteDevice([{
      DEVICESLNO: deviceSlno,
    }]);

    if (response.status !== '200') {
      return NextResponse.json(
        { error: response.msg || 'Failed to delete device' },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      message: 'Device deleted successfully',
      data: response.data 
    });

  } catch (error) {
    console.error('Error deleting device:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
