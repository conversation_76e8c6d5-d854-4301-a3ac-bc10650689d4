import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { externalDeviceAPI } from "@/lib/api/external-device-api";
import { verifyAdminAuth } from "@/lib/auth";

const getLogsSchema = z.object({
  action: z.literal("getLogs"),
  fromDate: z.string().min(1, "From date is required"),
  toDate: z.string().min(1, "To date is required"),
});

const clearLogsSchema = z.object({
  action: z.literal("clearLogs"),
});

const requestSchema = z.union([getLogsSchema, clearLogsSchema]);

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const deviceSerial = resolvedParams.deviceId;
    const body = await request.json();
    console.log("Attendance logs request:", { deviceSerial, body });

    // Validate request body
    const validatedData = requestSchema.parse(body);

    switch (validatedData.action) {
      case "getLogs": {
        console.log(
          `Getting attendance logs for device ${deviceSerial} from ${validatedData.fromDate} to ${validatedData.toDate}`
        );

        // First, send command to device to prepare attendance logs
        const cmdResponse = await externalDeviceAPI.sendCmdAttendanceLog([
          {
            DEVICESLNO: deviceSerial,
            FROMDATE: validatedData.fromDate,
            TODATE: validatedData.toDate,
          },
        ]);

        if (cmdResponse.status !== "200") {
          console.error("Failed to send attendance log command:", cmdResponse);
          return NextResponse.json(
            {
              error:
                cmdResponse.msg ||
                "Failed to send attendance log command to device",
            },
            { status: 400 }
          );
        }

        console.log(
          "Attendance log command sent successfully, waiting for device to prepare data..."
        );

        // Wait for device to prepare the data (as suggested by API response)
        await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait 5 seconds

        // Now get the attendance logs
        const logsResponse = await externalDeviceAPI.getAttendanceLog([
          {
            DEVICESLNO: deviceSerial,
            FROMDATE: validatedData.fromDate,
            TODATE: validatedData.toDate,
          },
        ]);

        if (logsResponse.status === "200") {
          const logs = (logsResponse.data || []).map((log) => ({
            enrollmentNo: log.EnrollmentNo,
            deviceSlno: log.DeviceSlno,
            punchDateTime: log.PunchDateTime,
            inOutValue: log.inout_value,
            modeValue: log["Mode value"],
            eventValue: log.Event_value,
            // Format the punch date time for display
            formattedDateTime: new Date(log.PunchDateTime).toLocaleString(),
            // Determine punch type based on inout_value
            punchType:
              log.inout_value === 0
                ? "IN"
                : log.inout_value === 1
                ? "OUT"
                : "UNKNOWN",
            // Determine verification mode based on mode value
            verificationMode:
              log["Mode value"] === 1
                ? "Fingerprint"
                : log["Mode value"] === 8
                ? "Face"
                : log["Mode value"] === 15
                ? "Face + Fingerprint"
                : "Unknown",
          }));

          console.log(`Successfully retrieved ${logs.length} attendance logs`);

          return NextResponse.json({
            success: true,
            data: logs,
            message: `Retrieved ${logs.length} attendance logs`,
            dateRange: {
              from: validatedData.fromDate,
              to: validatedData.toDate,
            },
          });
        } else {
          console.error("Failed to get attendance logs:", logsResponse);
          return NextResponse.json(
            {
              error: logsResponse.msg || "Failed to retrieve attendance logs",
            },
            { status: 400 }
          );
        }
      }

      case "clearLogs": {
        console.log(`Clearing all attendance logs for device ${deviceSerial}`);

        const response = await externalDeviceAPI.clearAllAttendanceLog(
          deviceSerial
        );

        if (response.status === "200") {
          console.log("Successfully cleared all attendance logs");

          return NextResponse.json({
            success: true,
            message: "All attendance logs cleared successfully",
          });
        } else {
          console.error("Failed to clear attendance logs:", response);
          return NextResponse.json(
            {
              error: response.msg || "Failed to clear attendance logs",
            },
            { status: 400 }
          );
        }
      }

      default:
        return NextResponse.json(
          {
            error: "Invalid action",
          },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Attendance logs API error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: error.issues,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}
