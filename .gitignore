# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
.env.local
.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# uploads
/public/uploads/*
!/public/uploads/.gitkeep

# logs
*.log
debug.log

# IDE
.vscode/
.idea/

# OS
Thumbs.db

# Prisma
/prisma/migrations/*
!/prisma/migrations/.gitkeep

# Backup files
*.backup
*.bak

/src/generated/prisma
