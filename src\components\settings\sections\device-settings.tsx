import React from "react";
import { Smartphone } from "lucide-react";
import { SettingsSection } from "../settings-section";
import { SettingsField } from "../settings-field";

interface DeviceSettingsData {
  // Admin settings
  autoSyncInterval?: number;
  maxDevicesPerCompany?: number;
  defaultDeviceTimeout?: number;
  allowBulkOperations?: boolean;
  requireDeviceApproval?: boolean;
  
  // Company settings
  allowMultipleCheckIns?: boolean;
  deviceSyncInterval?: number;
  offlineDataRetention?: number;
  faceRecognitionThreshold?: number;
  fingerprintThreshold?: number;
}

interface DeviceSettingsProps {
  data: DeviceSettingsData;
  onChange: (field: keyof DeviceSettingsData, value: any) => void;
  type: "admin" | "company";
}

export function DeviceSettings({ data, onChange, type }: DeviceSettingsProps) {
  return (
    <SettingsSection
      title="Device Settings"
      description={
        type === "admin"
          ? "Configure global device management settings and policies"
          : "Manage device behavior and recognition settings for your company"
      }
      icon={Smartphone}
    >
      {/* Admin-specific settings */}
      {type === "admin" && (
        <>
          {data.autoSyncInterval !== undefined && (
            <SettingsField
              type="number"
              label="Auto Sync Interval (minutes)"
              value={data.autoSyncInterval}
              onChange={(value) => onChange("autoSyncInterval", parseInt(value) || 60)}
              min={5}
              max={1440}
              description="How often devices should sync data with the server"
            />
          )}

          {data.maxDevicesPerCompany !== undefined && (
            <SettingsField
              type="number"
              label="Max Devices Per Company"
              value={data.maxDevicesPerCompany}
              onChange={(value) => onChange("maxDevicesPerCompany", parseInt(value) || 10)}
              min={1}
              max={1000}
              description="Maximum number of devices that can be allocated to a single company"
            />
          )}

          {data.defaultDeviceTimeout !== undefined && (
            <SettingsField
              type="number"
              label="Default Device Timeout (seconds)"
              value={data.defaultDeviceTimeout}
              onChange={(value) => onChange("defaultDeviceTimeout", parseInt(value) || 300)}
              min={30}
              max={3600}
              description="Default timeout for device operations"
            />
          )}

          {data.allowBulkOperations !== undefined && (
            <SettingsField
              type="switch"
              label="Allow Bulk Operations"
              checked={data.allowBulkOperations}
              onChange={(checked) => onChange("allowBulkOperations", checked)}
              description="Allow companies to perform bulk operations on multiple devices"
            />
          )}

          {data.requireDeviceApproval !== undefined && (
            <SettingsField
              type="switch"
              label="Require Device Approval"
              checked={data.requireDeviceApproval}
              onChange={(checked) => onChange("requireDeviceApproval", checked)}
              description="Require admin approval before new devices can be used"
            />
          )}
        </>
      )}

      {/* Company-specific settings */}
      {type === "company" && (
        <>
          {data.allowMultipleCheckIns !== undefined && (
            <SettingsField
              type="switch"
              label="Allow Multiple Check-ins"
              checked={data.allowMultipleCheckIns}
              onChange={(checked) => onChange("allowMultipleCheckIns", checked)}
              description="Allow employees to check in multiple times per day"
            />
          )}

          {data.deviceSyncInterval !== undefined && (
            <SettingsField
              type="number"
              label="Device Sync Interval (minutes)"
              value={data.deviceSyncInterval}
              onChange={(value) => onChange("deviceSyncInterval", parseInt(value) || 30)}
              min={5}
              max={120}
              description="How often your devices sync attendance data"
            />
          )}

          {data.offlineDataRetention !== undefined && (
            <SettingsField
              type="number"
              label="Offline Data Retention (days)"
              value={data.offlineDataRetention}
              onChange={(value) => onChange("offlineDataRetention", parseInt(value) || 7)}
              min={1}
              max={30}
              description="How long devices store data when offline"
            />
          )}

          {data.faceRecognitionThreshold !== undefined && (
            <SettingsField
              type="number"
              label="Face Recognition Threshold (%)"
              value={data.faceRecognitionThreshold}
              onChange={(value) => onChange("faceRecognitionThreshold", parseInt(value) || 80)}
              min={50}
              max={99}
              description="Minimum confidence level for face recognition (higher = more strict)"
            />
          )}

          {data.fingerprintThreshold !== undefined && (
            <SettingsField
              type="number"
              label="Fingerprint Threshold (%)"
              value={data.fingerprintThreshold}
              onChange={(value) => onChange("fingerprintThreshold", parseInt(value) || 85)}
              min={50}
              max={99}
              description="Minimum confidence level for fingerprint recognition (higher = more strict)"
            />
          )}
        </>
      )}
    </SettingsSection>
  );
}
