import React from "react";
import { Globe, RefreshCw } from "lucide-react";
import { SettingsSection } from "../settings-section";
import { SettingsField } from "../settings-field";
import { Button } from "@/components/ui/button";
import { ImageUpload } from "@/components/ui/image-upload";
import {
  useTimezoneOptions,
  useCompanyTimezoneOptions,
  useSyncTimezonesMutation,
} from "@/hooks/queries/useTimezoneQueries";

interface GeneralSettingsData {
  siteName?: string;
  siteDescription?: string;
  companyName?: string;
  timezone: string;
  dateFormat: string;
  language?: string;
  maintenanceMode?: boolean;
  logoUrl?: string;
  faviconUrl?: string;
}

interface GeneralSettingsProps {
  data: GeneralSettingsData;
  onChange: (field: keyof GeneralSettingsData, value: any) => void;
  type: "admin" | "company";
}

const dateFormatOptions = [
  { value: "MM/DD/YYYY", label: "MM/DD/YYYY (US)" },
  { value: "DD/MM/YYYY", label: "DD/MM/YYYY (UK)" },
  { value: "YYYY-MM-DD", label: "YYYY-MM-DD (ISO)" },
  { value: "DD-MM-YYYY", label: "DD-MM-YYYY" },
];

const languageOptions = [
  { value: "en", label: "English" },
  { value: "es", label: "Spanish" },
  { value: "fr", label: "French" },
  { value: "de", label: "German" },
  { value: "hi", label: "Hindi" },
];

export function GeneralSettings({
  data,
  onChange,
  type,
}: GeneralSettingsProps) {
  // Use different timezone hooks based on user type
  const adminTimezoneData = useTimezoneOptions();
  const companyTimezoneData = useCompanyTimezoneOptions();

  const {
    options: timezoneOptions,
    isLoading: timezonesLoading,
    error: timezonesError,
  } = type === "admin" ? adminTimezoneData : companyTimezoneData;

  const syncTimezonesMutation = useSyncTimezonesMutation();

  const handleSyncTimezones = () => {
    syncTimezonesMutation.mutate();
  };

  return (
    <SettingsSection
      title="General Settings"
      description={
        type === "admin"
          ? "Configure global system settings and preferences"
          : "Configure your company's basic information and preferences"
      }
      icon={Globe}
    >
      {type === "admin" && data.siteName !== undefined && (
        <SettingsField
          type="text"
          label="Site Name"
          value={data.siteName}
          onChange={(value) => onChange("siteName", value)}
          placeholder="Smart Attendance Portal"
          description="The name of your attendance management system"
          required
        />
      )}

      {type === "admin" && data.siteDescription !== undefined && (
        <SettingsField
          type="textarea"
          label="Site Description"
          value={data.siteDescription}
          onChange={(value) => onChange("siteDescription", value)}
          placeholder="Advanced attendance management system"
          description="Brief description of your system"
          rows={2}
        />
      )}

      {type === "admin" && data.logoUrl !== undefined && (
        <ImageUpload
          label="Logo"
          value={data.logoUrl}
          onChange={(value) => onChange("logoUrl", value)}
          placeholder="Enter logo URL or upload image"
          description="Your organization's logo image (optional). Recommended size: 32x32px or larger"
          accept="image/*"
          maxSize={2}
        />
      )}

      {type === "admin" && data.faviconUrl !== undefined && (
        <ImageUpload
          label="Favicon"
          value={data.faviconUrl}
          onChange={(value) => onChange("faviconUrl", value)}
          placeholder="Enter favicon URL or upload image"
          description="Your organization's favicon (optional). Recommended: 16x16px or 32x32px ICO/PNG"
          accept="image/*,.ico"
          maxSize={1}
        />
      )}

      {type === "company" && data.companyName !== undefined && (
        <SettingsField
          type="text"
          label="Company Name"
          value={data.companyName}
          onChange={(value) => onChange("companyName", value)}
          placeholder="Your Company Name"
          description="Your organization's display name"
          required
        />
      )}

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium">Timezone</label>
            <p className="text-sm text-muted-foreground">
              Default timezone for your organization
            </p>
          </div>
          {type === "admin" && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleSyncTimezones}
              disabled={syncTimezonesMutation.isPending}
              className="ml-2"
            >
              {syncTimezonesMutation.isPending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Syncing...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Sync Timezones
                </>
              )}
            </Button>
          )}
        </div>
        <SettingsField
          type="select"
          label=""
          value={data.timezone}
          onChange={(value) => onChange("timezone", value)}
          options={timezoneOptions}
          placeholder={
            timezonesLoading ? "Loading timezones..." : "Select timezone"
          }
        />
        {timezonesError && (
          <p className="text-sm text-red-600">
            Failed to load timezones. Using default options.
          </p>
        )}
      </div>

      <SettingsField
        type="select"
        label="Date Format"
        value={data.dateFormat}
        onChange={(value) => onChange("dateFormat", value)}
        options={dateFormatOptions}
        description="How dates should be displayed throughout the system"
      />

      {type === "admin" && data.language !== undefined && (
        <SettingsField
          type="select"
          label="Default Language"
          value={data.language}
          onChange={(value) => onChange("language", value)}
          options={languageOptions}
          description="Default language for the system interface"
        />
      )}

      {type === "admin" && data.maintenanceMode !== undefined && (
        <SettingsField
          type="switch"
          label="Maintenance Mode"
          checked={data.maintenanceMode}
          onChange={(checked) => onChange("maintenanceMode", checked)}
          description="Enable maintenance mode to restrict access to the system"
        />
      )}
    </SettingsSection>
  );
}
