"use client";

import { memo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { DataTable, Column } from "@/components/shared/data-table";
import { CompanyWithAllocations } from "@/types";

interface PaginationInfo {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

interface CompanyTableSectionProps {
  companies: CompanyWithAllocations[];
  columns: Column<CompanyWithAllocations>[];
  loading: boolean;
  pagination: PaginationInfo;
  selectedCompanies: CompanyWithAllocations[];
  searchQuery: string;
  sortBy: string;
  sortOrder: "asc" | "desc";
  bulkActions: Array<{
    label: string;
    onClick: () => void;
    variant?: "default" | "destructive";
  }>;
  onSelectionChange: (companies: CompanyWithAllocations[]) => void;
  onPageChange: (page: number) => void;
  onSearch: (query: string) => void;
  onSort: (column: string) => void;
}

export const CompanyTableSection = memo(function CompanyTableSection({
  companies,
  columns,
  loading,
  pagination,
  selectedCompanies,
  searchQuery,
  sortBy,
  sortOrder,
  bulkActions,
  onSelectionChange,
  onPageChange,
  onSearch,
  onSort,
}: CompanyTableSectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>All Companies</CardTitle>
        <CardDescription>
          Manage company registrations, view allocations, and perform bulk
          operations
        </CardDescription>
      </CardHeader>
      <CardContent>
        <DataTable
          data={companies}
          columns={columns}
          loading={loading}
          selectable
          onSelectionChange={onSelectionChange}
          pagination={pagination}
          onPageChange={onPageChange}
          onSort={onSort}
          sortBy={sortBy}
          sortOrder={sortOrder}
          emptyMessage={
            companies.length === 0 && !loading
              ? "No companies registered yet. Click 'Register Company' to add the first one."
              : "No companies found matching your search."
          }
          actions={bulkActions}
        />
      </CardContent>
    </Card>
  );
});
