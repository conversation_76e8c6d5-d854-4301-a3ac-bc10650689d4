import { NextRequest, NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import bcrypt from 'bcryptjs'

interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

async function changePassword(req: AuthenticatedRequest) {
  try {
    const user = req.user!
    
    if (user.role !== 'company') {
      return NextResponse.json(
        { error: 'Company access required' },
        { status: 403 }
      )
    }
    
    const body: ChangePasswordRequest = await req.json()
    
    const { currentPassword, newPassword, confirmPassword } = body
    
    // Validate input
    if (!currentPassword || !newPassword || !confirmPassword) {
      return NextResponse.json(
        { error: 'All password fields are required' },
        { status: 400 }
      )
    }
    
    if (newPassword !== confirmPassword) {
      return NextResponse.json(
        { error: 'New passwords do not match' },
        { status: 400 }
      )
    }
    
    if (newPassword.length < 8) {
      return NextResponse.json(
        { error: 'New password must be at least 8 characters long' },
        { status: 400 }
      )
    }
    
    // Password strength validation
    const hasUpperCase = /[A-Z]/.test(newPassword)
    const hasLowerCase = /[a-z]/.test(newPassword)
    const hasNumbers = /\d/.test(newPassword)
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(newPassword)
    
    if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
      return NextResponse.json(
        { 
          error: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character' 
        },
        { status: 400 }
      )
    }
    
    // In a real implementation, you would:
    // 1. Verify the current password against the stored hash
    // 2. Hash the new password
    // 3. Update the password in the database
    
    try {
      // Hash the new password
      const saltRounds = 12
      const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds)
      
      // In a real implementation, update the password in the database
      // await updateCompanyPassword(user.companyId, hashedNewPassword)
      
      return NextResponse.json({
        success: true,
        message: 'Password changed successfully'
      })
      
    } catch (error) {
      console.error('Password change error:', error)
      return NextResponse.json(
        { error: 'Failed to change password' },
        { status: 500 }
      )
    }
    
  } catch (error) {
    console.error('Change password error:', error)
    return NextResponse.json(
      { error: 'Failed to process password change request' },
      { status: 500 }
    )
  }
}

export const POST = withAuth(changePassword)
