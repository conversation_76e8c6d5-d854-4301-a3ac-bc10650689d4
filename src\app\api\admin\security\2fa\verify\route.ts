import { NextRequest, NextResponse } from "next/server";
import { withAdminAuth, AuthenticatedRequest } from "@/lib/auth/middleware";
import { authenticator } from "otplib";
import { prisma } from "@/lib/prisma";

interface Verify2FARequest {
  secret: string;
  token: string;
}

async function verify2FA(req: AuthenticatedRequest) {
  try {
    const user = req.user!;
    const body: Verify2FARequest = await req.json();

    const { secret, token } = body;

    if (!secret || !token) {
      return NextResponse.json(
        { error: "Secret and token are required" },
        { status: 400 }
      );
    }

    // Clean and validate token format
    const cleanToken = token.toString().replace(/\s/g, ""); // Remove any spaces

    if (!/^\d{6}$/.test(cleanToken)) {
      return NextResponse.json(
        { error: "Invalid token format. Please enter a 6-digit code." },
        { status: 400 }
      );
    }

    // Verify the token against the secret
    const isValid = authenticator.verify({
      token: cleanToken,
      secret,
    });

    if (!isValid) {
      return NextResponse.json(
        { error: "Invalid verification code" },
        { status: 400 }
      );
    }

    try {
      // Enable 2FA for the admin
      await prisma.admin.update({
        where: { id: user.userId },
        data: {
          twoFactorSecret: secret,
          twoFactorEnabled: true,
          updatedAt: new Date(),
        },
      });
    } catch (dbError) {
      console.warn(
        "Database not available for admin 2FA verification:",
        dbError
      );
      // Continue with success response even if database update fails
    }

    return NextResponse.json({
      success: true,
      message: "2FA has been successfully enabled",
    });
  } catch (error) {
    console.error("2FA verification error:", error);
    return NextResponse.json(
      { error: "Failed to verify 2FA" },
      { status: 500 }
    );
  }
}

export const POST = withAdminAuth(verify2FA);
