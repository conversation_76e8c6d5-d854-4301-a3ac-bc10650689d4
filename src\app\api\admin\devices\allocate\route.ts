import { NextRequest, NextResponse } from "next/server";
import { withAdminAuth, AuthenticatedRequest } from "@/lib/auth/middleware";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Validation schemas
const allocateDeviceSchema = z.object({
  deviceSerialNumbers: z
    .array(z.string().min(1, "Device serial number is required"))
    .min(1, "At least one device must be selected"),
  companyId: z.string().min(1, "Company ID is required"),
});

const deallocateDeviceSchema = z.object({
  deviceSerialNumbers: z
    .array(z.string().min(1, "Device serial number is required"))
    .min(1, "At least one device must be selected"),
});

// POST /api/admin/devices/allocate - Allocate devices to a company
async function allocateDevices(req: AuthenticatedRequest) {
  try {
    const body = await req.json();
    const validatedData = allocateDeviceSchema.parse(body);

    // Check if company exists
    const company = await prisma.company.findUnique({
      where: { id: validatedData.companyId },
    });

    if (!company) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    // Check if company is active
    if (company.status !== "ACTIVE") {
      return NextResponse.json(
        { error: "Cannot allocate devices to inactive company" },
        { status: 400 }
      );
    }

    // Check if devices are already allocated
    const existingAllocations = await prisma.allocation.findMany({
      where: {
        deviceSerialNo: {
          in: validatedData.deviceSerialNumbers,
        },
      },
      include: {
        company: {
          select: {
            name: true,
          },
        },
      },
    });

    if (existingAllocations.length > 0) {
      const allocatedDevices = existingAllocations.map((allocation) => ({
        deviceSerialNo: allocation.deviceSerialNo,
        companyName: allocation.company.name,
      }));

      return NextResponse.json(
        {
          error: "Some devices are already allocated",
          allocatedDevices,
        },
        { status: 400 }
      );
    }

    // Create allocations
    const allocations = validatedData.deviceSerialNumbers.map((serialNo) => ({
      companyId: validatedData.companyId,
      deviceSerialNo: serialNo,
    }));

    await prisma.allocation.createMany({
      data: allocations,
    });

    // Get updated company with allocation count
    const updatedCompany = await prisma.company.findUnique({
      where: { id: validatedData.companyId },
      include: {
        allocations: {
          select: {
            deviceSerialNo: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "Devices allocated successfully",
      allocatedDevices: validatedData.deviceSerialNumbers.length,
      companyName: updatedCompany?.name,
      totalAllocatedDevices: updatedCompany?.allocations.length || 0,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input", details: error.issues },
        { status: 400 }
      );
    }

    console.error("Error allocating devices:", error);
    return NextResponse.json(
      { error: "Failed to allocate devices" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/devices/allocate - Deallocate devices
async function deallocateDevices(req: AuthenticatedRequest) {
  try {
    const body = await req.json();
    const validatedData = deallocateDeviceSchema.parse(body);

    // Check if devices are allocated
    const existingAllocations = await prisma.allocation.findMany({
      where: {
        deviceSerialNo: {
          in: validatedData.deviceSerialNumbers,
        },
      },
      include: {
        company: {
          select: {
            name: true,
          },
        },
      },
    });

    if (existingAllocations.length === 0) {
      return NextResponse.json(
        { error: "No allocated devices found" },
        { status: 404 }
      );
    }

    // Group by company for response
    const companiesAffected = existingAllocations.reduce((acc, allocation) => {
      const companyName = allocation.company.name;
      if (!acc[companyName]) {
        acc[companyName] = [];
      }
      acc[companyName].push(allocation.deviceSerialNo);
      return acc;
    }, {} as Record<string, string[]>);

    // Delete allocations
    await prisma.allocation.deleteMany({
      where: {
        deviceSerialNo: {
          in: validatedData.deviceSerialNumbers,
        },
      },
    });

    return NextResponse.json({
      message: "Devices deallocated successfully",
      deallocatedDevices: existingAllocations.length,
      companiesAffected,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input", details: error.issues },
        { status: 400 }
      );
    }

    console.error("Error deallocating devices:", error);
    return NextResponse.json(
      { error: "Failed to deallocate devices" },
      { status: 500 }
    );
  }
}

export const POST = withAdminAuth(allocateDevices);
export const DELETE = withAdminAuth(deallocateDevices);
