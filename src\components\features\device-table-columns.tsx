"use client";

import { useMemo, useCallback } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Column } from "@/components/shared/data-table";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Power,
  RefreshCw,
  Smartphone,
  Activity,
  AlertCircle,
  CheckCircle,
  Clock,
  Users,
  Settings,
  DoorOpen,
} from "lucide-react";
import { toast } from "react-toastify";

interface Device {
  id: string;
  name: string;
  serialNumber: string;
  model: string;
  modelName: string;
  location: string;
  ipAddress: string;
  port: number;
  status: string;
  lastSeen?: string | null;
  firmwareVersion: string;
  totalUsers: number;
  timeZone: number;
  logCount: number;
  features: {
    faceRecognition: boolean;
    fingerprintScanner: boolean;
    cardReader: boolean;
    temperatureCheck: boolean;
  };
  biometricCounts: {
    fingerprints: number;
    faces: number;
    cards: number;
    passwords: number;
  };
  createdAt: string;
  updatedAt: string;
  allocatedCompany?: string | null;
}

interface UseDeviceColumnsProps {
  deviceStatuses: Record<string, string>;
  statusHistory: Record<string, string[]>;
  isHydrated: boolean;
  onDeviceAction: (
    action: string,
    deviceSlno: string,
    additionalData?: Record<string, unknown>
  ) => Promise<void> | void;
}

export function useDeviceColumns({
  deviceStatuses,
  statusHistory,
  isHydrated,
  onDeviceAction,
}: UseDeviceColumnsProps) {
  const getStatusBadge = useCallback(
    (device: Device) => {
      // Handle hydration - use device.status during SSR/hydration
      if (!isHydrated) {
        const status = device.status || "UNKNOWN";
        switch (status.toUpperCase()) {
          case "ONLINE":
            return (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Online
              </Badge>
            );
          case "OFFLINE":
            return (
              <Badge variant="destructive">
                <AlertCircle className="h-3 w-3 mr-1" />
                Offline
              </Badge>
            );
          default:
            return (
              <Badge variant="secondary">
                <Clock className="h-3 w-3 mr-1" />
                Unknown
              </Badge>
            );
        }
      }

      // After hydration, use Redux status
      const reduxStatus = deviceStatuses[device.serialNumber];
      const status = reduxStatus || device.status || "UNKNOWN";

      // Check if device status is fluctuating
      const history = statusHistory[device.serialNumber] || [];
      const isFluctuating =
        history.length >= 3 && new Set(history.slice(-3)).size > 1; // Last 3 statuses are different

      switch (status.toUpperCase()) {
        case "ONLINE":
          return (
            <Badge
              variant="default"
              className={`bg-green-100 text-green-800 ${
                isFluctuating ? "border-2 border-yellow-400" : ""
              }`}
              title={
                isFluctuating
                  ? `Status fluctuating. Recent: ${history
                      .slice(-3)
                      .join(" → ")}`
                  : "Device is online"
              }
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              Online{isFluctuating ? " ⚠️" : ""}
            </Badge>
          );
        case "OFFLINE":
          return (
            <Badge
              variant="destructive"
              className={isFluctuating ? "border-2 border-yellow-400" : ""}
              title={
                isFluctuating
                  ? `Status fluctuating. Recent: ${history
                      .slice(-3)
                      .join(" → ")}`
                  : "Device is offline"
              }
            >
              <AlertCircle className="h-3 w-3 mr-1" />
              Offline{isFluctuating ? " ⚠️" : ""}
            </Badge>
          );
        case "LOADING":
          return (
            <Badge variant="outline" className="bg-blue-50 text-blue-700">
              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
              Getting status...
            </Badge>
          );
        case "UNKNOWN":
          return (
            <Badge variant="secondary">
              <Clock className="h-3 w-3 mr-1" />
              Unknown
            </Badge>
          );
        default:
          return (
            <Badge variant="secondary">
              <Clock className="h-3 w-3 mr-1" />
              Unknown
            </Badge>
          );
      }
    },
    [deviceStatuses, statusHistory, isHydrated]
  );

  const getAllocationBadge = useCallback((allocatedCompany?: string | null) => {
    if (allocatedCompany) {
      return (
        <div className="space-y-1">
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            Allocated
          </Badge>
          <div className="text-xs text-gray-600 font-medium">
            {allocatedCompany}
          </div>
        </div>
      );
    }
    return (
      <Badge variant="outline" className="bg-gray-50 text-gray-700">
        Available
      </Badge>
    );
  }, []);

  const columns: Column<Device>[] = useMemo(
    () => [
      {
        key: "deviceInfo",
        label: "Device Info",
        sortable: true,
        render: (_, device) => {
          if (!device) {
            return <div className="text-sm text-gray-500">Unknown Device</div>;
          }
          return (
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Smartphone className="h-8 w-8 text-gray-400" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-900">
                  {String(device.name || "Unknown")}
                </div>
                <div className="text-xs text-gray-500">
                  Model:{" "}
                  {(() => {
                    const modelName = device.modelName;
                    const modelNo = device.model;

                    // Check if modelName is empty object or invalid
                    if (
                      !modelName ||
                      typeof modelName === "object" ||
                      modelName === "{}" ||
                      String(modelName).trim() === ""
                    ) {
                      return String(modelNo || "Unknown");
                    }
                    return String(modelName);
                  })()}
                </div>
                <div className="text-xs text-gray-500">
                  Model No: {String(device.model || "N/A")}
                </div>
                <div className="text-xs text-gray-500">
                  Serial: {String(device.serialNumber || "N/A")}
                </div>
              </div>
            </div>
          );
        },
      },
      {
        key: "allocation",
        label: "Allocation",
        render: (_, device) => {
          if (!device) {
            return <Badge variant="secondary">Unknown</Badge>;
          }
          return getAllocationBadge(device.allocatedCompany);
        },
      },
      {
        key: "location",
        label: "Device Location",
        sortable: true,
        render: (_, device) => (
          <div className="truncate ml-4 text-sm text-gray-900">
            {String(device?.location || "Not specified")}
          </div>
        ),
      },
      {
        key: "status",
        label: "Status",
        sortable: true,
        render: (_, device) => getStatusBadge(device),
      },
      {
        key: "userLogCount",
        label: "User & Log Count",
        sortable: true,
        render: (_, device) => {
          if (!device) {
            return <div className="text-sm text-gray-500">N/A</div>;
          }
          return (
            <div className="flex flex-col gap-1 ml-5 text-sm text-gray-900">
              <div className="flex items-center">
                <Users className="h-3 w-3 mr-1" />
                Users: {Number(device.totalUsers || 0)}
              </div>
              <div className="flex items-center text-xs text-gray-500">
                <Activity className="h-3 w-3 mr-1" />
                Logs: {Number(device.logCount || 0)}
              </div>
            </div>
          );
        },
      },
      {
        key: "biometricFeatures",
        label: "Features",
        render: (_, device) => {
          if (!device) {
            return <div className="text-sm text-gray-500">N/A</div>;
          }
          return (
            <div className="flex flex-wrap gap-1">
              {device.features?.faceRecognition && (
                <Badge variant="outline" className="text-xs">
                  Face
                </Badge>
              )}
              {device.features?.fingerprintScanner && (
                <Badge variant="outline" className="text-xs">
                  Finger
                </Badge>
              )}
              {device.features?.cardReader && (
                <Badge variant="outline" className="text-xs">
                  Card
                </Badge>
              )}
              {!device.features?.faceRecognition &&
                !device.features?.fingerprintScanner &&
                !device.features?.cardReader && (
                  <Badge variant="secondary" className="text-xs">
                    None
                  </Badge>
                )}
            </div>
          );
        },
      },
      {
        key: "lastUpdate",
        label: "Last Update",
        render: (_, device) => {
          if (!device?.updatedAt || device.updatedAt === "{}") {
            return <div className="text-sm text-gray-500">Never</div>;
          }
          try {
            const date = new Date(device.updatedAt);
            if (isNaN(date.getTime())) {
              return <div className="text-sm text-gray-500">Invalid</div>;
            }
            return (
              <div className="text-sm text-gray-900">
                {date.toLocaleDateString()}
                <div className="text-xs text-gray-500">
                  {date.toLocaleTimeString()}
                </div>
              </div>
            );
          } catch {
            return <div className="text-sm text-gray-500">Invalid</div>;
          }
        },
      },
      {
        key: "actions",
        label: "Actions",
        render: (_, device) => {
          if (!device) {
            return <div className="text-sm text-gray-500">No actions</div>;
          }
          return (
            <div className="flex items-center space-x-2">
              <Button
                variant={device.allocatedCompany ? "destructive" : "outline"}
                size="sm"
                onClick={() =>
                  onDeviceAction(
                    device.allocatedCompany ? "deallocate" : "allocate",
                    device.serialNumber
                  )
                }
              >
                {device.allocatedCompany ? "Deallocate" : "Allocate"}
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={() => onDeviceAction("edit", device.serialNumber)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Device
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="text-red-600"
                    onClick={() =>
                      onDeviceAction("delete", device.serialNumber)
                    }
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Device
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() =>
                      onDeviceAction("moreActions", device.serialNumber)
                    }
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    More Actions
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() =>
                      onDeviceAction("refreshStatus", device.serialNumber)
                    }
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh Status
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          );
        },
      },
    ],
    [onDeviceAction, getStatusBadge, getAllocationBadge]
  );

  return columns;
}
