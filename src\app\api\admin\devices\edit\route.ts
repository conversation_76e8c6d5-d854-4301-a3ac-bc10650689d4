import { NextRequest, NextResponse } from 'next/server';
import { externalDeviceAPI } from '@/lib/api/external-device-api';
import { verifyAdminAuth } from '@/lib/auth';

// POST /api/admin/devices/edit - Edit device information
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { deviceSlno, deviceName, modelNo, timeZoneId, location, endpointUrl } = body;

    // Validate required fields
    if (!deviceSlno || !deviceName || !modelNo || !timeZoneId || !location) {
      return NextResponse.json(
        { error: 'All required fields must be provided' },
        { status: 400 }
      );
    }

    // Prepare data for external API
    const deviceData = {
      DEVICESLNO: deviceSlno,
      DEVICENAME: deviceName,
      MODELNO: modelNo,
      TIMEZONEID: timeZoneId,
      LOCATION: location,
      'ENDPOINT URL': endpointUrl || '',
    };

    // Call external API to update device
    const response = await externalDeviceAPI.addEditDevice([deviceData]);

    if (response.status !== '200') {
      return NextResponse.json(
        { error: response.msg || 'Failed to update device' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Device updated successfully',
      data: response.data,
    });

  } catch (error) {
    console.error('Error updating device:', error);
    return NextResponse.json(
      { error: 'Failed to update device' },
      { status: 500 }
    );
  }
}
