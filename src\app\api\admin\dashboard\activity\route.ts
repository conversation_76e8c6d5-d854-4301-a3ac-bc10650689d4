import { NextRequest, NextResponse } from 'next/server'
import { withAdminAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { prisma } from '@/lib/prisma'

interface ActivityItem {
  id: string
  type: 'company_registered' | 'company_activated' | 'company_deactivated' | 'device_allocated' | 'device_deallocated' | 'system_maintenance'
  title: string
  description: string
  timestamp: string
  status: 'success' | 'warning' | 'error' | 'info'
}

async function getRecentActivity(req: AuthenticatedRequest) {
  try {
    const activities: ActivityItem[] = []

    try {
      // Get recent company registrations
      const recentCompanies = await prisma.company.findMany({
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: {
          id: true,
          name: true,
          createdAt: true,
          status: true
        }
      })

      recentCompanies.forEach(company => {
        activities.push({
          id: `company-${company.id}`,
          type: 'company_registered',
          title: 'New company registered',
          description: `${company.name} joined the platform`,
          timestamp: company.createdAt.toISOString(),
          status: 'success'
        })
      })

      // Get recent allocations
      const recentAllocations = await prisma.allocation.findMany({
        orderBy: { createdAt: 'desc' },
        take: 5,
        include: {
          company: {
            select: { name: true }
          }
        }
      })

      recentAllocations.forEach(allocation => {
        activities.push({
          id: `allocation-${allocation.id}`,
          type: 'device_allocated',
          title: 'Device allocated',
          description: `Device ${allocation.deviceSerialNo} allocated to ${allocation.company.name}`,
          timestamp: allocation.createdAt.toISOString(),
          status: 'info'
        })
      })

      // Get companies with status changes (recent updates)
      const recentUpdates = await prisma.company.findMany({
        where: {
          updatedAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        orderBy: { updatedAt: 'desc' },
        take: 3,
        select: {
          id: true,
          name: true,
          status: true,
          updatedAt: true
        }
      })

      recentUpdates.forEach(company => {
        if (company.status === 'ACTIVE') {
          activities.push({
            id: `company-activated-${company.id}`,
            type: 'company_activated',
            title: 'Company activated',
            description: `${company.name} account was activated`,
            timestamp: company.updatedAt.toISOString(),
            status: 'success'
          })
        } else if (company.status === 'DEACTIVATED') {
          activities.push({
            id: `company-deactivated-${company.id}`,
            type: 'company_deactivated',
            title: 'Company deactivated',
            description: `${company.name} account was deactivated`,
            timestamp: company.updatedAt.toISOString(),
            status: 'warning'
          })
        }
      })

    } catch (dbError) {
      console.warn('Database not available for activity data:', dbError)
      // Add a system message when database is not available
      activities.push({
        id: 'system-db-unavailable',
        type: 'system_maintenance',
        title: 'System Status',
        description: 'Database connection unavailable - using cached data',
        timestamp: new Date().toISOString(),
        status: 'warning'
      })
    }

    // Sort activities by timestamp (most recent first)
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    // Return only the most recent 10 activities
    const recentActivities = activities.slice(0, 10)

    return NextResponse.json({
      success: true,
      data: recentActivities
    })

  } catch (error) {
    console.error('Get recent activity error:', error)

    return NextResponse.json(
      { error: 'Failed to fetch recent activity' },
      { status: 500 }
    )
  }
}

export const GET = withAdminAuth(getRecentActivity)
