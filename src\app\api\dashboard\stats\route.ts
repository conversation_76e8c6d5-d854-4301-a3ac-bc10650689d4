import { NextRequest, NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { prisma } from '@/lib/prisma'
import { externalDeviceAPI } from '@/lib/api/external-device-api'

async function getCompanyStats(req: AuthenticatedRequest) {
  try {
    const user = req.user!
    
    if (user.role !== 'company') {
      return NextResponse.json(
        { error: 'Company access required' },
        { status: 403 }
      )
    }

    // No caching for now - direct database queries

    let stats = {
      totalEmployees: 0,
      presentToday: 0,
      totalDevices: 0,
      activeDevices: 0,
      attendanceRate: 0,
      lastSyncTime: 'Never'
    }

    try {
      // Get company details
      const company = await prisma.company.findUnique({
        where: { id: user.companyId },
        include: {
          allocations: {
            select: {
              deviceSerialNo: true,
              createdAt: true
            }
          }
        }
      })

      if (!company) {
        return NextResponse.json(
          { error: 'Company not found' },
          { status: 404 }
        )
      }

      stats.totalDevices = company.allocations.length

      // Get device status for allocated devices
      let activeDeviceCount = 0
      if (company.allocations.length > 0) {
        for (const allocation of company.allocations) {
          try {
            const statusResponse = await externalDeviceAPI.getDeviceOnlineStatus(allocation.deviceSerialNo)
            if (statusResponse.status === '200') {
              activeDeviceCount++
            }
          } catch (error) {
            // Device check failed, count as inactive
          }
        }
      }
      stats.activeDevices = activeDeviceCount

      // Get attendance data for today (if devices are available)
      if (company.allocations.length > 0) {
        const today = new Date()
        const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
        const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000)

        let totalAttendanceRecords = 0
        let uniqueEmployees = new Set<string>()

        for (const allocation of company.allocations) {
          try {
            // Get attendance logs for today
            const attendanceResponse = await externalDeviceAPI.getAttendanceLog([{
              DEVICESLNO: allocation.deviceSerialNo,
              FROMDATE: todayStart.toISOString().slice(0, 16).replace('T', ' '),
              TODATE: todayEnd.toISOString().slice(0, 16).replace('T', ' ')
            }])

            if (attendanceResponse.status === '200' && attendanceResponse.data) {
              totalAttendanceRecords += attendanceResponse.data.length
              attendanceResponse.data.forEach((record: any) => {
                if (record.EnrollmentNo) {
                  uniqueEmployees.add(record.EnrollmentNo)
                }
              })
            }
          } catch (error) {
            console.warn(`Failed to get attendance for device ${allocation.deviceSerialNo}:`, error)
          }
        }

        stats.presentToday = uniqueEmployees.size

        // Get total employee count from user data
        let totalEmployeeCount = 0
        for (const allocation of company.allocations) {
          try {
            // First send command to get user data
            await externalDeviceAPI.getCmdUserData([{
              DEVICESLNO: allocation.deviceSerialNo
            }])

            // Wait a bit for the command to process
            await new Promise(resolve => setTimeout(resolve, 1000))

            // Then get the user data
            const userResponse = await externalDeviceAPI.getUserData([{
              DEVICESLNO: allocation.deviceSerialNo
            }])

            if (userResponse.status === 'Success' && userResponse.data) {
              totalEmployeeCount += userResponse.data.length
            }
          } catch (error) {
            console.warn(`Failed to get user data for device ${allocation.deviceSerialNo}:`, error)
          }
        }

        stats.totalEmployees = totalEmployeeCount
        stats.attendanceRate = totalEmployeeCount > 0 ? (stats.presentToday / totalEmployeeCount) * 100 : 0

        // Set last sync time to the most recent allocation
        if (company.allocations.length > 0) {
          const mostRecentAllocation = company.allocations.reduce((latest, current) => 
            current.createdAt > latest.createdAt ? current : latest
          )
          stats.lastSyncTime = mostRecentAllocation.createdAt.toISOString()
        }
      }

    } catch (dbError) {
      console.warn('Database not available, using default values:', dbError)
      // Use default values when database is not available
      stats = {
        totalEmployees: 0,
        presentToday: 0,
        totalDevices: 0,
        activeDevices: 0,
        attendanceRate: 0,
        lastSyncTime: 'Never'
      }
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Get company stats error:', error)

    return NextResponse.json(
      { error: 'Failed to fetch company statistics' },
      { status: 500 }
    )
  }
}

export const GET = withAuth(getCompanyStats)
