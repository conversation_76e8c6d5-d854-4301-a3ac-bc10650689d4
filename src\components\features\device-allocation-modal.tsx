"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Search,
  Building2,
  Calendar,
  Users,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import { toast } from "react-toastify";

interface Company {
  id: string;
  name: string;
  organizationId: string;
  email: string;
  status: "ACTIVE" | "DEACTIVATED";
  userType: string;
  expiresAt: string;
  daysRemaining: number;
  allocatedDevices: number;
}

interface DeviceAllocationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedDevices: string[];
  onSuccess: () => void;
  mode: "allocate" | "deallocate";
}

export function DeviceAllocationModal({
  open,
  onOpenChange,
  selectedDevices,
  onSuccess,
  mode,
}: DeviceAllocationModalProps) {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [filteredCompanies, setFilteredCompanies] = useState<Company[]>([]);
  const [selectedCompanyId, setSelectedCompanyId] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [fetchingCompanies, setFetchingCompanies] = useState(false);

  // Fetch companies when modal opens
  useEffect(() => {
    if (open && mode === "allocate") {
      fetchCompanies();
    }
  }, [open, mode]);

  // Filter companies based on search
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredCompanies(companies);
    } else {
      const filtered = companies.filter(
        (company) =>
          company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          company.organizationId
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          company.email.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCompanies(filtered);
    }
  }, [searchQuery, companies]);

  const fetchCompanies = async () => {
    setFetchingCompanies(true);
    try {
      const response = await fetch(
        "/api/admin/companies?status=ACTIVE&pageSize=100"
      );
      if (response.ok) {
        const result = await response.json();
        // Handle both old and new API response formats
        const companiesData = result.companies || result.data || [];

        // Transform companies to include calculated fields
        const transformedCompanies = companiesData.map((company: any) => ({
          ...company,
          daysRemaining:
            company.daysRemaining ||
            Math.ceil(
              (new Date(company.expiresAt).getTime() - new Date().getTime()) /
                (1000 * 60 * 60 * 24)
            ),
          allocatedDevices:
            company.allocatedDevices || company.allocations?.length || 0,
        }));

        setCompanies(transformedCompanies);
      } else {
        toast.error("Failed to fetch companies");
      }
    } catch (error) {
      console.error("Error fetching companies:", error);
      toast.error("Error fetching companies");
    } finally {
      setFetchingCompanies(false);
    }
  };

  const handleAllocate = async () => {
    if (!selectedCompanyId) {
      toast.error("Please select a company");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/admin/devices/allocate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          deviceSerialNumbers: selectedDevices,
          companyId: selectedCompanyId,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(
          `Successfully allocated ${data.allocatedDevices} device(s) to ${data.companyName}`
        );
        onSuccess();
        onOpenChange(false);
        resetForm();
      } else {
        if (data.allocatedDevices) {
          toast.error(
            `Some devices are already allocated: ${data.allocatedDevices
              .map((d: any) => `${d.deviceSerialNo} (${d.companyName})`)
              .join(", ")}`
          );
        } else {
          toast.error(data.error || "Failed to allocate devices");
        }
      }
    } catch (error) {
      toast.error("Error allocating devices");
    } finally {
      setLoading(false);
    }
  };

  const handleDeallocate = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/devices/allocate", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          deviceSerialNumbers: selectedDevices,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(
          `Successfully deallocated ${data.deallocatedDevices} device(s)`
        );
        onSuccess();
        onOpenChange(false);
      } else {
        toast.error(data.error || "Failed to deallocate devices");
      }
    } catch (error) {
      toast.error("Error deallocating devices");
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setSelectedCompanyId("");
    setSearchQuery("");
  };

  const selectedCompany = companies.find((c) => c.id === selectedCompanyId);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === "allocate" ? "Allocate Devices" : "Deallocate Devices"}
          </DialogTitle>
          <DialogDescription>
            {mode === "allocate"
              ? `Assign ${selectedDevices.length} selected device(s) to a company`
              : `Remove allocation for ${selectedDevices.length} selected device(s)`}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Selected Devices */}
          <div>
            <Label className="text-sm font-medium">
              Selected Devices ({selectedDevices.length})
            </Label>
            <div className="mt-2 flex flex-wrap gap-2 max-h-20 overflow-y-auto">
              {selectedDevices.map((device) => (
                <Badge key={device} variant="secondary" className="text-xs">
                  {device}
                </Badge>
              ))}
            </div>
          </div>

          <Separator />

          {mode === "allocate" ? (
            <>
              {/* Company Search */}
              <div>
                <Label htmlFor="search" className="text-sm font-medium">
                  Search Companies
                </Label>
                <div className="relative mt-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="search"
                    placeholder="Search by name, organization ID, or email..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Company Selection */}
              <div>
                <Label className="text-sm font-medium">Select Company</Label>
                {fetchingCompanies ? (
                  <div className="mt-2 text-sm text-gray-500">
                    Loading companies...
                  </div>
                ) : (
                  <div className="mt-2 max-h-60 overflow-y-auto border rounded-md">
                    {filteredCompanies.length === 0 ? (
                      <div className="p-4 text-center text-gray-500">
                        {searchQuery
                          ? "No companies found matching your search"
                          : "No active companies available"}
                      </div>
                    ) : (
                      filteredCompanies.map((company) => (
                        <div
                          key={company.id}
                          className={`p-3 border-b last:border-b-0 cursor-pointer hover:bg-gray-50 ${
                            selectedCompanyId === company.id
                              ? "bg-blue-50 border-blue-200"
                              : ""
                          }`}
                          onClick={() => setSelectedCompanyId(company.id)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <Building2 className="h-4 w-4 text-gray-400" />
                                <span className="font-medium">
                                  {company.name}
                                </span>
                                <Badge variant="outline" className="text-xs">
                                  {company.organizationId}
                                </Badge>
                              </div>
                              <div className="mt-1 text-sm text-gray-600">
                                {company.email} •{" "}
                                {company.userType.replace("_", " ")}
                              </div>
                              <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                                <span className="flex items-center">
                                  <Users className="h-3 w-3 mr-1" />
                                  {company.allocatedDevices} devices
                                </span>
                                <span className="flex items-center">
                                  <Calendar className="h-3 w-3 mr-1" />
                                  {company.daysRemaining > 0
                                    ? `${company.daysRemaining} days left`
                                    : "Expired"}
                                </span>
                              </div>
                            </div>
                            {selectedCompanyId === company.id && (
                              <CheckCircle className="h-5 w-5 text-blue-600" />
                            )}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                )}
              </div>

              {/* Selected Company Summary */}
              {selectedCompany && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                    <span className="font-medium text-blue-900">
                      Selected: {selectedCompany.name}
                    </span>
                  </div>
                  <div className="mt-1 text-sm text-blue-700">
                    After allocation:{" "}
                    {selectedCompany.allocatedDevices + selectedDevices.length}{" "}
                    total devices
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <span className="font-medium text-yellow-900">
                  Confirm Deallocation
                </span>
              </div>
              <div className="mt-1 text-sm text-yellow-700">
                This will remove the allocation for {selectedDevices.length}{" "}
                device(s). The devices will become unallocated and available for
                assignment to other companies.
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={mode === "allocate" ? handleAllocate : handleDeallocate}
            disabled={loading || (mode === "allocate" && !selectedCompanyId)}
            className={
              mode === "deallocate" ? "bg-red-600 hover:bg-red-700" : ""
            }
          >
            {loading
              ? "Processing..."
              : mode === "allocate"
              ? "Allocate Devices"
              : "Deallocate Devices"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
