// Settings validation utilities

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface ValidationRule {
  field: string;
  validate: (value: unknown) => ValidationResult;
}

// Common validation functions
export const validators = {
  required: (value: unknown): ValidationResult => ({
    isValid: value !== null && value !== undefined && value !== "",
    errors:
      value === null || value === undefined || value === ""
        ? ["This field is required"]
        : [],
  }),

  email: (value: string): ValidationResult => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return {
      isValid: emailRegex.test(value),
      errors: emailRegex.test(value)
        ? []
        : ["Please enter a valid email address"],
    };
  },

  minLength:
    (min: number) =>
    (value: string): ValidationResult => ({
      isValid: value.length >= min,
      errors:
        value.length >= min ? [] : [`Must be at least ${min} characters long`],
    }),

  maxLength:
    (max: number) =>
    (value: string): ValidationResult => ({
      isValid: value.length <= max,
      errors:
        value.length <= max
          ? []
          : [`Must be no more than ${max} characters long`],
    }),

  range:
    (min: number, max: number) =>
    (value: number): ValidationResult => ({
      isValid: value >= min && value <= max,
      errors:
        value >= min && value <= max
          ? []
          : [`Must be between ${min} and ${max}`],
    }),

  positiveNumber: (value: number): ValidationResult => ({
    isValid: value > 0,
    errors: value > 0 ? [] : ["Must be a positive number"],
  }),

  url: (value: string, required: boolean = true): ValidationResult => {
    // If not required and value is empty, it's valid
    if (!required && (!value || value.trim() === "")) {
      return { isValid: true, errors: [] };
    }

    // If required and value is empty, it's invalid
    if (required && (!value || value.trim() === "")) {
      return { isValid: false, errors: ["URL is required"] };
    }

    try {
      new URL(value);
      return { isValid: true, errors: [] };
    } catch {
      return { isValid: false, errors: ["Please enter a valid URL"] };
    }
  },

  timezone: (value: string): ValidationResult => {
    // Accept timezone ID (numeric string only)
    const isValidTimezoneId = /^\d+$/.test(value);

    return {
      isValid: isValidTimezoneId,
      errors: isValidTimezoneId ? [] : ["Please select a valid timezone ID"],
    };
  },

  dateFormat: (value: string): ValidationResult => {
    const validFormats = [
      "MM/DD/YYYY",
      "DD/MM/YYYY",
      "YYYY-MM-DD",
      "DD-MM-YYYY",
    ];
    return {
      isValid: validFormats.includes(value),
      errors: validFormats.includes(value)
        ? []
        : ["Please select a valid date format"],
    };
  },

  ipAddress: (value: string): ValidationResult => {
    const ipRegex =
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const cidrRegex =
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$/;

    if (value === "") return { isValid: true, errors: [] }; // Allow empty

    const isValid = ipRegex.test(value) || cidrRegex.test(value);
    return {
      isValid,
      errors: isValid ? [] : ["Please enter a valid IP address or CIDR block"],
    };
  },

  time: (value: string): ValidationResult => {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return {
      isValid: timeRegex.test(value),
      errors: timeRegex.test(value)
        ? []
        : ["Please enter a valid time (HH:MM)"],
    };
  },

  imageUrl: (value: string, required: boolean = false): ValidationResult => {
    // If not required and value is empty, it's valid
    if (!required && (!value || value.trim() === "")) {
      return { isValid: true, errors: [] };
    }

    // If required and value is empty, it's invalid
    if (required && (!value || value.trim() === "")) {
      return { isValid: false, errors: ["Image URL is required"] };
    }

    const trimmedValue = value.trim();

    // Check if it's an image URL (basic check)
    const imageExtensions = [
      ".jpg",
      ".jpeg",
      ".png",
      ".gif",
      ".svg",
      ".webp",
      ".bmp",
      ".ico",
    ];
    const hasImageExtension = imageExtensions.some((ext) =>
      trimmedValue.toLowerCase().endsWith(ext)
    );

    // Handle relative paths (like /uploads/filename.jpg)
    if (trimmedValue.startsWith("/")) {
      if (!hasImageExtension) {
        return {
          isValid: false,
          errors: [
            "Please provide a valid image file (jpg, jpeg, png, gif, svg, webp, bmp, ico)",
          ],
        };
      }
      return { isValid: true, errors: [] };
    }

    // Handle absolute URLs
    try {
      new URL(trimmedValue); // Validate URL format

      if (!hasImageExtension) {
        return {
          isValid: false,
          errors: [
            "Please provide a valid image URL (jpg, jpeg, png, gif, svg, webp, bmp, ico)",
          ],
        };
      }

      return { isValid: true, errors: [] };
    } catch {
      return {
        isValid: false,
        errors: ["Please enter a valid image URL or upload an image file"],
      };
    }
  },
};

// Admin settings validation rules
export const adminSettingsValidation: ValidationRule[] = [
  {
    field: "general.siteName",
    validate: (value) => validators.required(value),
  },
  {
    field: "general.siteDescription",
    validate: (value) => validators.required(value),
  },
  {
    field: "general.defaultTimeZone",
    validate: (value) => validators.timezone(value),
  },
  {
    field: "general.dateFormat",
    validate: (value) => validators.dateFormat(value),
  },
  {
    field: "general.logoUrl",
    validate: (value) => validators.imageUrl(value, false), // Optional image URL
  },
  {
    field: "general.faviconUrl",
    validate: (value) => validators.imageUrl(value, false), // Optional image URL
  },
];

// Company settings validation rules
export const companySettingsValidation: ValidationRule[] = [
  {
    field: "general.companyName",
    validate: (value) => validators.required(value),
  },
  {
    field: "general.timezone",
    validate: (value) => validators.timezone(value),
  },
  {
    field: "general.dateFormat",
    validate: (value) => validators.dateFormat(value),
  },
  {
    field: "general.workingHours.start",
    validate: (value) => validators.time(value),
  },
  {
    field: "general.workingHours.end",
    validate: (value) => validators.time(value),
  },
  {
    field: "attendance.graceTime",
    validate: (value) => validators.range(0, 60)(value),
  },
  {
    field: "attendance.autoClockOutTime",
    validate: (value) => validators.time(value),
  },
  {
    field: "attendance.maxWorkingHours",
    validate: (value) => validators.range(1, 24)(value),
  },
  {
    field: "devices.deviceSyncInterval",
    validate: (value) => validators.range(5, 120)(value),
  },
  {
    field: "devices.offlineDataRetention",
    validate: (value) => validators.range(1, 30)(value),
  },
  {
    field: "devices.faceRecognitionThreshold",
    validate: (value) => validators.range(50, 99)(value),
  },
  {
    field: "devices.fingerprintThreshold",
    validate: (value) => validators.range(50, 99)(value),
  },
];

// Validate settings object
export function validateSettings(
  settings: Record<string, unknown>,
  rules: ValidationRule[]
): { isValid: boolean; errors: Record<string, string[]> } {
  const errors: Record<string, string[]> = {};
  let isValid = true;

  for (const rule of rules) {
    const value = getNestedValue(settings, rule.field);
    const result = rule.validate(value);

    if (!result.isValid) {
      errors[rule.field] = result.errors;
      isValid = false;
    }
  }

  return { isValid, errors };
}

// Helper function to get nested object values
function getNestedValue(obj: Record<string, unknown>, path: string): unknown {
  return path.split(".").reduce((current, key) => current?.[key], obj);
}
