import { NextRequest, NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { prisma } from '@/lib/prisma'
import { externalDeviceAPI } from '@/lib/api/external-device-api'

interface CompanyProfile {
  id: string
  name: string
  email: string
  organizationId: string
  userType: string
  status: string
  expiresAt: string
  createdAt: string
  totalDevices: number
  totalEmployees: number
  lastSyncTime: string
  accountHealth: number
  daysRemaining: number
  avatar?: string
  loginToken?: string
}

async function getCompanyProfile(req: AuthenticatedRequest) {
  try {
    const user = req.user!

    if (user.role !== 'company') {
      return NextResponse.json(
        { error: 'Company access required' },
        { status: 403 }
      )
    }

    // No caching for now - direct database queries

    try {
      // Get company details from database
      const company = await prisma.company.findUnique({
        where: { id: user.companyId },
        select: {
          id: true,
          name: true,
          email: true,
          organizationId: true,
          userType: true,
          status: true,
          expiresAt: true,
          createdAt: true,
          loginToken: true,
          avatar: true,
          allocations: {
            select: {
              deviceSerialNo: true,
              createdAt: true
            }
          }
        }
      })

      if (!company) {
        return NextResponse.json(
          { error: 'Company not found' },
          { status: 404 }
        )
      }

      // Calculate days remaining
      const now = new Date()
      const daysRemaining = Math.ceil((company.expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

      let totalEmployees = 0
      let lastSyncTime = 'Never'

      // Get employee count from devices
      if (company.allocations.length > 0) {
        for (const allocation of company.allocations) {
          try {
            // Send command to get user data
            await externalDeviceAPI.getCmdUserData([{
              DEVICESLNO: allocation.deviceSerialNo
            }])

            // Wait a bit for the command to process
            await new Promise(resolve => setTimeout(resolve, 500))

            // Get the user data
            const userResponse = await externalDeviceAPI.getUserData([{
              DEVICESLNO: allocation.deviceSerialNo
            }])

            if (userResponse.status === 'Success' && userResponse.data) {
              totalEmployees += userResponse.data.length
            }
          } catch (error) {
            console.warn(`Failed to get user data for device ${allocation.deviceSerialNo}:`, error)
          }
        }

        // Set last sync time to the most recent allocation
        const mostRecentAllocation = company.allocations.reduce((latest, current) => 
          current.createdAt > latest.createdAt ? current : latest
        )
        lastSyncTime = mostRecentAllocation.createdAt.toISOString()
      }

      // Calculate account health based on various factors
      let accountHealth = 100
      if (daysRemaining < 7) accountHealth -= 30
      if (daysRemaining < 0) accountHealth -= 50
      if (company.allocations.length === 0) accountHealth -= 20
      if (company.status !== 'ACTIVE') accountHealth -= 40
      accountHealth = Math.max(0, accountHealth)

      const profile: CompanyProfile = {
        id: company.id,
        name: company.name,
        email: company.email,
        organizationId: company.organizationId,
        userType: company.userType,
        status: company.status,
        expiresAt: company.expiresAt.toISOString(),
        createdAt: company.createdAt.toISOString(),
        totalDevices: company.allocations.length,
        totalEmployees,
        lastSyncTime,
        accountHealth,
        daysRemaining: Math.max(0, daysRemaining),
        avatar: company.avatar || '',
        loginToken: company.loginToken || undefined
      }

      return NextResponse.json({
        success: true,
        data: profile
      })

    } catch (dbError) {
      console.warn('Database not available for profile data:', dbError)
      
      // Return minimal profile data when database is not available
      const profile: CompanyProfile = {
        id: user.companyId || 'unknown',
        name: 'Company Name',
        email: user.email,
        organizationId: 'ORG001',
        userType: 'API_USER',
        status: 'ACTIVE',
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date().toISOString(),
        totalDevices: 0,
        totalEmployees: 0,
        lastSyncTime: 'Never',
        accountHealth: 50,
        daysRemaining: 30
      }

      return NextResponse.json({
        success: true,
        data: profile
      })
    }

  } catch (error) {
    console.error('Get company profile error:', error)

    return NextResponse.json(
      { error: 'Failed to fetch company profile' },
      { status: 500 }
    )
  }
}

async function updateCompanyProfile(req: AuthenticatedRequest) {
  try {
    const user = req.user!
    
    if (user.role !== 'company') {
      return NextResponse.json(
        { error: 'Company access required' },
        { status: 403 }
      )
    }

    const body = await req.json()
    const { name, email, avatar } = body
    
    if (!name || !email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      )
    }

    try {
      // Update company in database
      const updatedCompany = await prisma.company.update({
        where: { id: user.companyId },
        data: {
          name,
          email,
          avatar,
          updatedAt: new Date()
        }
      })

      return NextResponse.json({
        success: true,
        data: {
          id: updatedCompany.id,
          name: updatedCompany.name,
          email: updatedCompany.email,
          avatar: updatedCompany.avatar,
          updatedAt: updatedCompany.updatedAt.toISOString()
        },
        message: 'Profile updated successfully'
      })

    } catch (dbError) {
      console.warn('Database not available for profile update:', dbError)
      
      return NextResponse.json(
        { error: 'Database not available' },
        { status: 503 }
      )
    }

  } catch (error) {
    console.error('Update company profile error:', error)

    return NextResponse.json(
      { error: 'Failed to update company profile' },
      { status: 500 }
    )
  }
}

export const GET = withAuth(getCompanyProfile)
export const PUT = withAuth(updateCompanyProfile)
