import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "@/lib/queryKeys";
import axios from "axios";

// Types for profile data
export interface AdminProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  department?: string;
  location?: string;
  bio?: string;
  avatar?: string;
  createdAt: string;
  lastLogin: string;
  loginCount: number;
  totalCompaniesManaged: number;
  totalDevicesManaged: number;
  systemUptime: string;
  twoFactorEnabled: boolean;
}

export interface CompanyProfile {
  id: string;
  name: string;
  email: string;
  organizationId: string;
  userType: string;
  status: string;
  expiresAt: string;
  createdAt: string;
  totalDevices: number;
  totalEmployees: number;
  lastSyncTime: string;
  accountHealth: number;
  daysRemaining: number;
  loginToken?: string;
  avatar?: string;
}

// Fetch functions
const fetchAdminProfile = async (): Promise<AdminProfile> => {
  const response = await axios.get("/api/admin/profile");
  return response.data.data;
};

const fetchCompanyProfile = async (): Promise<CompanyProfile> => {
  const response = await axios.get("/api/dashboard/profile");
  return response.data.data;
};

const updateAdminProfile = async (
  profileData: Partial<AdminProfile>
): Promise<AdminProfile> => {
  const response = await axios.put("/api/admin/profile", profileData);
  return response.data.data;
};

const updateCompanyProfile = async (
  profileData: Partial<CompanyProfile>
): Promise<CompanyProfile> => {
  const response = await axios.put("/api/dashboard/profile", profileData);
  return response.data.data;
};

// Hooks for admin profile
export function useAdminProfileQuery() {
  return useQuery({
    queryKey: queryKeys.user.profile(),
    queryFn: fetchAdminProfile,
    staleTime: 60 * 1000, // 1 minute
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    refetchIntervalInBackground: false,
    retry: 2,
  });
}

export function useUpdateAdminProfileMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateAdminProfile,
    onSuccess: (data) => {
      // Update the profile cache
      queryClient.setQueryData(queryKeys.user.profile(), data);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.user.all });
    },
    onError: (error) => {
      console.error("Failed to update admin profile:", error);
    },
  });
}

// Hooks for company profile
export function useCompanyProfileQuery() {
  return useQuery({
    queryKey: queryKeys.user.profile(),
    queryFn: fetchCompanyProfile,
    staleTime: 60 * 1000, // 1 minute
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    refetchIntervalInBackground: false,
    retry: 2,
  });
}

export function useUpdateCompanyProfileMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateCompanyProfile,
    onSuccess: (data) => {
      // Update the profile cache
      queryClient.setQueryData(queryKeys.user.profile(), data);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.user.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.all });
    },
    onError: (error) => {
      console.error("Failed to update company profile:", error);
    },
  });
}
