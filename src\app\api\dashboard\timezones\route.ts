import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { prisma } from '@/lib/prisma';

// GET /api/dashboard/timezones - Get all timezones from database (company access)
async function getTimezones(req: AuthenticatedRequest) {
  try {
    const user = req.user!;

    // Allow both company users and admin users (for admin impersonation)
    if (user.role !== 'company' && user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Company or admin access required' },
        { status: 403 }
      );
    }

    // Get timezones from database
    const timezones = await prisma.timeZone.findMany({
      orderBy: { timezoneId: 'asc' }
    });

    // If no timezones in database, return empty array with message
    if (timezones.length === 0) {
      return NextResponse.json({
        success: true,
        data: [],
        total: 0,
        message: 'No timezones available. Please contact administrator to sync timezones.'
      });
    }

    // Transform the data to a more usable format
    const formattedTimezones = timezones.map(tz => ({
      id: tz.timezoneId,
      name: tz.timezoneName,
      time: tz.timezoneTime,
    }));

    return NextResponse.json({
      success: true,
      data: formattedTimezones,
      total: formattedTimezones.length,
    });

  } catch (error) {
    console.error('Error fetching timezones for company:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export const GET = withAuth(getTimezones);
