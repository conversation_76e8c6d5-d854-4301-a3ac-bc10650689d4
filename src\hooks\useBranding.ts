import { useQuery } from "@tanstack/react-query";

interface BrandingResponse {
  logoUrl: string;
  faviconUrl: string;
  siteName: string;
}

// Fetch branding data from admin settings
const fetchBranding = async (): Promise<BrandingResponse> => {
  const response = await fetch("/api/admin/settings/branding");

  if (!response.ok) {
    // Return defaults if API fails
    return {
      logoUrl: "",
      faviconUrl: "",
      siteName: "SRITechnologies",
    };
  }

  return response.json();
};

// Hook to get branding data with fallback
export const useBranding = () => {
  const { data, isLoading, error } = useQuery({
    queryKey: ["branding"],
    queryFn: fetchBranding,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 1,
  });

  return {
    logoUrl: data?.logoUrl || "",
    faviconUrl: data?.faviconUrl || "",
    siteName: data?.siteName || "SRITechnologies",
    isLoading,
    error,
  };
};
