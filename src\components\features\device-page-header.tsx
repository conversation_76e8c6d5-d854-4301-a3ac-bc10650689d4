"use client";

import { memo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Plus, RefreshCw, Shuffle } from "lucide-react";
import { useTimezoneOptions } from "@/hooks/queries/useTimezoneQueries";

interface DevicePageHeaderProps {
  showAddDialog: boolean;
  showStatusTooltip: boolean;
  statusLoading: boolean;
  deviceFormData: {
    deviceSlno: string;
    deviceName: string;
    modelNo: string;
    timeZoneId: string;
    location: string;
    endpointUrl: string;
  };
  onShowAddDialogChange: (show: boolean) => void;
  onRefreshAllDevicesStatus: () => void;
  onReloadData: () => void;
  onDeviceFormDataChange: (data: {
    deviceSlno: string;
    deviceName: string;
    modelNo: string;
    timeZoneId: string;
    location: string;
    endpointUrl: string;
  }) => void;
  onAddDevice: () => void;
  onSyncDevices?: () => void;
}

export const DevicePageHeader = memo(function DevicePageHeader({
  showAddDialog,
  showStatusTooltip,
  statusLoading,
  deviceFormData,
  onShowAddDialogChange,
  onRefreshAllDevicesStatus,
  onReloadData,
  onDeviceFormDataChange,
  onAddDevice,
  onSyncDevices,
}: DevicePageHeaderProps) {
  const { options: timezoneOptions, isLoading: timezonesLoading } =
    useTimezoneOptions();
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Device Management</h1>
        <p className="text-gray-600">
          Monitor and manage all attendance devices across the platform
        </p>
      </div>
      <div className="flex space-x-3">
        <TooltipProvider>
          <Tooltip open={showStatusTooltip}>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                onClick={() => {
                  onRefreshAllDevicesStatus();
                }}
                className={statusLoading ? "animate-pulse" : ""}
              >
                <RefreshCw
                  className={`h-4 w-4 mr-2 ${
                    statusLoading ? "animate-spin" : ""
                  }`}
                />
                Refresh Status
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>
                ✅ Device statuses loaded! Click here to refresh again if
                needed.
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <Button variant="outline" onClick={onReloadData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Reload Data
        </Button>

        {onSyncDevices && (
          <Button variant="outline" onClick={onSyncDevices}>
            <Shuffle className="h-4 w-4 mr-2" />
            Sync Devices
          </Button>
        )}

        <Dialog open={showAddDialog} onOpenChange={onShowAddDialogChange}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Device
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add New Device</DialogTitle>
              <DialogDescription>
                Register a new attendance device to the system.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="deviceSlno" className="text-right">
                  Serial No
                </Label>
                <Input
                  id="deviceSlno"
                  value={deviceFormData.deviceSlno}
                  onChange={(e) =>
                    onDeviceFormDataChange({
                      ...deviceFormData,
                      deviceSlno: e.target.value,
                    })
                  }
                  className="col-span-3"
                  placeholder="Enter device serial number"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="deviceName" className="text-right">
                  Name
                </Label>
                <Input
                  id="deviceName"
                  value={deviceFormData.deviceName}
                  onChange={(e) =>
                    onDeviceFormDataChange({
                      ...deviceFormData,
                      deviceName: e.target.value,
                    })
                  }
                  className="col-span-3"
                  placeholder="Enter device name"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="modelNo" className="text-right">
                  Model
                </Label>
                <Input
                  id="modelNo"
                  value={deviceFormData.modelNo}
                  onChange={(e) =>
                    onDeviceFormDataChange({
                      ...deviceFormData,
                      modelNo: e.target.value,
                    })
                  }
                  className="col-span-3"
                  placeholder="Enter model number"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="timeZone" className="text-right">
                  Time Zone
                </Label>
                <Select
                  value={deviceFormData.timeZoneId}
                  onValueChange={(value) =>
                    onDeviceFormDataChange({
                      ...deviceFormData,
                      timeZoneId: value,
                    })
                  }
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue
                      placeholder={
                        timezonesLoading
                          ? "Loading timezones..."
                          : "Select timezone"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {timezoneOptions.map(
                      (tz: { value: string; label: string }) => (
                        <SelectItem key={tz.value} value={tz.value}>
                          {tz.label}
                        </SelectItem>
                      )
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="location" className="text-right">
                  Location
                </Label>
                <Input
                  id="location"
                  value={deviceFormData.location}
                  onChange={(e) =>
                    onDeviceFormDataChange({
                      ...deviceFormData,
                      location: e.target.value,
                    })
                  }
                  className="col-span-3"
                  placeholder="Enter device location"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="endpointUrl" className="text-right">
                  Endpoint URL
                </Label>
                <Input
                  id="endpointUrl"
                  value={deviceFormData.endpointUrl}
                  onChange={(e) =>
                    onDeviceFormDataChange({
                      ...deviceFormData,
                      endpointUrl: e.target.value,
                    })
                  }
                  className="col-span-3"
                  placeholder="Enter endpoint URL (optional)"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => onShowAddDialogChange(false)}
              >
                Cancel
              </Button>
              <Button onClick={onAddDevice}>Add Device</Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
});
