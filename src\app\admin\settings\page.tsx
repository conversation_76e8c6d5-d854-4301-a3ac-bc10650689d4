"use client";

import React, { useState } from "react";
import { DashboardLayout } from "@/components/shared/dashboard-layout";
import { toast } from "react-toastify";
import { Skeleton } from "@/components/ui/skeleton";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  AlertTriangle,
  Save,
  Globe,
  Shield,
  Bell,
  Smartphone,
  Database,
  Settings,
} from "lucide-react";
import {
  useAdminSettingsQuery,
  useUpdateAdminSettingsMutation,
} from "@/hooks/queries/useSettingsQueries";
import {
  SettingsLayout,
  GeneralSettings,
  SecuritySettings,
  NotificationSettings,
  DeviceSettings,
  BackupSettings,
} from "@/components/settings";
import { SettingsImportExport } from "@/components/settings/settings-import-export";
import {
  validateSettings,
  adminSettingsValidation,
} from "@/lib/settings-validation";

interface SystemSettings {
  general: {
    siteName: string;
    siteDescription: string;
    defaultTimeZone: string;
    dateFormat: string;
    language: string;
    maintenanceMode: boolean;
    logoUrl: string;
    faviconUrl: string;
  };
}

export default function AdminSettingsPage() {
  // Use real-time data hooks
  const { data: settings, isLoading, error } = useAdminSettingsQuery();
  const updateSettingsMutation = useUpdateAdminSettingsMutation();

  const [editedSettings, setEditedSettings] = useState<SystemSettings | null>(
    null
  );
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string[]>
  >({});

  // Initialize edited settings when data loads
  React.useEffect(() => {
    if (settings && !editedSettings) {
      console.log("Loaded settings:", settings);
      setEditedSettings(settings);
    }
  }, [settings, editedSettings]);

  // Remove hardcoded originalSettings - use data from API instead

  const handleSave = async () => {
    if (!editedSettings) return;

    // Validate settings before saving
    console.log("Settings to validate:", editedSettings);
    const validation = validateSettings(
      editedSettings,
      adminSettingsValidation
    );
    console.log("Validation result:", validation);
    if (!validation.isValid) {
      setValidationErrors(validation.errors);
      console.log("Validation errors:", validation.errors);
      toast.error("Please fix validation errors before saving");
      return;
    }

    // Clear validation errors if valid
    setValidationErrors({});

    try {
      // Only send the general settings since that's all we support now
      const settingsToSave = {
        general: editedSettings.general,
      };
      await updateSettingsMutation.mutateAsync(settingsToSave);
      toast.success("Settings saved successfully");
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.error || "Failed to save settings";
      toast.error(errorMessage);
    }
  };

  // Helper functions to update settings for each section
  const updateGeneralSetting = (field: string, value: any) => {
    if (!editedSettings) return;
    setEditedSettings((prev) => {
      if (!prev) return null;

      // Map component field names to API field names
      const fieldMapping: { [key: string]: string } = {
        timezone: "defaultTimeZone",
      };

      const actualField = fieldMapping[field] || field;

      return {
        ...prev,
        general: {
          ...prev.general,
          [actualField]: value,
        },
      };
    });
  };

  // Loading state
  if (isLoading) {
    return (
      <DashboardLayout userRole="admin" userEmail="<EMAIL>">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Skeleton className="h-8 w-48 mb-2" />
              <Skeleton className="h-4 w-96" />
            </div>
            <Skeleton className="h-10 w-32" />
          </div>
          <div className="space-y-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-32 mb-2" />
                  <Skeleton className="h-4 w-64" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[...Array(3)].map((_, j) => (
                      <div key={j} className="space-y-2">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-10 w-full" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <DashboardLayout userRole="admin" userEmail="<EMAIL>">
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              System Settings
            </h1>
            <p className="text-gray-600">
              Configure system-wide settings and preferences
            </p>
          </div>
          <Card>
            <CardContent className="text-center py-12">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-500" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Failed to load settings
              </h3>
              <p className="text-gray-600 mb-4">
                There was an error loading the system settings.
              </p>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  if (!editedSettings) {
    return null;
  }

  const settingsTabs = [
    {
      id: "general",
      label: "General",
      icon: Globe,
      content: (
        <GeneralSettings
          data={{
            siteName: editedSettings.general.siteName,
            siteDescription: editedSettings.general.siteDescription,
            timezone: editedSettings.general.defaultTimeZone,
            dateFormat: editedSettings.general.dateFormat,
            language: editedSettings.general.language,
            maintenanceMode: editedSettings.general.maintenanceMode,
            logoUrl: editedSettings.general.logoUrl,
            faviconUrl: editedSettings.general.faviconUrl,
          }}
          onChange={updateGeneralSetting}
          type="admin"
        />
      ),
    },
  ];

  return (
    <DashboardLayout userRole="admin" userEmail="<EMAIL>">
      <SettingsLayout
        title="System Settings"
        description="Configure platform-wide settings and preferences"
        tabs={settingsTabs}
        onSave={handleSave}
        isSaving={updateSettingsMutation.isPending}
        hasChanges={JSON.stringify(settings) !== JSON.stringify(editedSettings)}
        defaultTab="general"
      />
    </DashboardLayout>
  );
}
