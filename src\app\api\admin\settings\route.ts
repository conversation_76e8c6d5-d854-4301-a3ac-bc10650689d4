import { NextRequest, NextResponse } from 'next/server'
import { withAdminAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { prisma } from '@/lib/prisma'
import { clearAdminSettingsCache } from '@/lib/settings'

interface SystemSettings {
  general: {
    siteName: string
    siteDescription: string
    defaultTimeZone: string
    dateFormat: string
    logoUrl: string
    faviconUrl: string
  }
}

// Default settings
const defaultSettings: SystemSettings = {
  general: {
    siteName: 'Smart Attendance Portal',
    siteDescription: 'Advanced attendance management system for modern businesses',
    defaultTimeZone: '57', // Default to timezone ID 57 (Asia/Kolkata)
    dateFormat: 'MM/DD/YYYY',
    logoUrl: '',
    faviconUrl: ''
  }
}

async function getSettings(req: AuthenticatedRequest) {
  try {

    // Get or create admin settings
    let adminSettings = await prisma.adminSettings.findFirst()

    if (!adminSettings) {
      // Create default settings if none exist
      adminSettings = await prisma.adminSettings.create({
        data: {
          siteName: defaultSettings.general.siteName,
          siteDescription: defaultSettings.general.siteDescription,
          defaultTimeZoneId: parseInt(defaultSettings.general.defaultTimeZone),
          dateFormat: defaultSettings.general.dateFormat,
          logoUrl: defaultSettings.general.logoUrl,
          faviconUrl: defaultSettings.general.faviconUrl,
        }
      })
    }

    // Convert database format to API format
    const settings: SystemSettings = {
      general: {
        siteName: adminSettings.siteName,
        siteDescription: adminSettings.siteDescription,
        defaultTimeZone: adminSettings.defaultTimeZoneId?.toString() || '57',
        dateFormat: adminSettings.dateFormat,
        logoUrl: adminSettings.logoUrl || '',
        faviconUrl: adminSettings.faviconUrl || '',
      }
    }


    return NextResponse.json(settings)
  } catch (error) {
    console.error('Failed to fetch admin settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    )
  }
}

async function updateSettings(req: AuthenticatedRequest) {
  try {
    const user = req.user!
    const body = await req.json()
    
    // Validate the settings structure
    const { general } = body

    if (!general) {
      return NextResponse.json(
        { error: 'Invalid settings structure' },
        { status: 400 }
      )
    }

    // Validate general settings
    if (!general.siteName || general.siteName.trim().length === 0) {
      return NextResponse.json(
        { error: 'Site name is required' },
        { status: 400 }
      )
    }

    if (!general.siteDescription || general.siteDescription.trim().length === 0) {
      return NextResponse.json(
        { error: 'Site description is required' },
        { status: 400 }
      )
    }
    
    // Get or create admin settings
    let adminSettings = await prisma.adminSettings.findFirst()

    if (!adminSettings) {
      // Create new settings if none exist
      adminSettings = await prisma.adminSettings.create({
        data: {
          siteName: general.siteName,
          siteDescription: general.siteDescription,
          defaultTimeZoneId: parseInt(general.defaultTimeZone),
          dateFormat: general.dateFormat,
          logoUrl: general.logoUrl || '',
          faviconUrl: general.faviconUrl || '',
        }
      })
    } else {
      // Update existing settings
      adminSettings = await prisma.adminSettings.update({
        where: { id: adminSettings.id },
        data: {
          siteName: general.siteName,
          siteDescription: general.siteDescription,
          defaultTimeZoneId: parseInt(general.defaultTimeZone),
          dateFormat: general.dateFormat,
          logoUrl: general.logoUrl || '',
          faviconUrl: general.faviconUrl || '',
        }
      })
    }

    // Clear the settings cache after update
    clearAdminSettingsCache()

    return NextResponse.json({
      success: true,
      message: 'Settings updated successfully'
    })
    
  } catch (error) {
    console.error('Update settings error:', error)
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    )
  }
}

export const GET = withAdminAuth(getSettings)
export const PUT = withAdminAuth(updateSettings)
