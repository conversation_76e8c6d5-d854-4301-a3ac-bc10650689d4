import React from "react";
import { Bell } from "lucide-react";
import { SettingsSection } from "../settings-section";
import { SettingsField } from "../settings-field";

interface NotificationSettingsData {
  emailNotifications?: boolean;
  emailAlerts?: boolean;
  attendanceReminders?: boolean;
  lateArrivalAlerts?: boolean;
  absenteeAlerts?: boolean;
  deviceOfflineAlerts?: boolean;
  companyExpiryAlerts?: boolean;
  systemMaintenanceAlerts?: boolean;
  weeklyReports?: boolean;
  monthlyReports?: boolean;
  adminEmail?: string;
  deviceOfflineMinutes?: number;
  companyExpiryDays?: number;
}

interface NotificationSettingsProps {
  data: NotificationSettingsData;
  onChange: (field: keyof NotificationSettingsData, value: any) => void;
  type: "admin" | "company";
}

export function NotificationSettings({ data, onChange, type }: NotificationSettingsProps) {
  return (
    <SettingsSection
      title="Notifications"
      description={
        type === "admin"
          ? "Configure system-wide notification settings and alert thresholds"
          : "Manage email notifications and attendance alerts for your company"
      }
      icon={Bell}
    >
      {/* Email Settings */}
      {type === "admin" && data.emailNotifications !== undefined && (
        <SettingsField
          type="switch"
          label="Email Notifications"
          checked={data.emailNotifications}
          onChange={(checked) => onChange("emailNotifications", checked)}
          description="Enable email notifications system-wide"
        />
      )}

      {type === "company" && data.emailAlerts !== undefined && (
        <SettingsField
          type="switch"
          label="Email Alerts"
          checked={data.emailAlerts}
          onChange={(checked) => onChange("emailAlerts", checked)}
          description="Receive email notifications for important events"
        />
      )}

      {/* Admin Email */}
      {type === "admin" && data.adminEmail !== undefined && (
        <SettingsField
          type="email"
          label="Admin Email"
          value={data.adminEmail}
          onChange={(value) => onChange("adminEmail", value)}
          placeholder="<EMAIL>"
          description="Primary email address for system notifications"
          required
        />
      )}

      {/* Company-specific notifications */}
      {type === "company" && (
        <>
          {data.attendanceReminders !== undefined && (
            <SettingsField
              type="switch"
              label="Attendance Reminders"
              checked={data.attendanceReminders}
              onChange={(checked) => onChange("attendanceReminders", checked)}
              description="Send attendance reminders to employees"
            />
          )}

          {data.lateArrivalAlerts !== undefined && (
            <SettingsField
              type="switch"
              label="Late Arrival Alerts"
              checked={data.lateArrivalAlerts}
              onChange={(checked) => onChange("lateArrivalAlerts", checked)}
              description="Get notified when employees arrive late"
            />
          )}

          {data.absenteeAlerts !== undefined && (
            <SettingsField
              type="switch"
              label="Absentee Alerts"
              checked={data.absenteeAlerts}
              onChange={(checked) => onChange("absenteeAlerts", checked)}
              description="Alert when employees are absent without notice"
            />
          )}
        </>
      )}

      {/* Device alerts (both admin and company) */}
      {data.deviceOfflineAlerts !== undefined && (
        <SettingsField
          type="switch"
          label="Device Offline Alerts"
          checked={data.deviceOfflineAlerts}
          onChange={(checked) => onChange("deviceOfflineAlerts", checked)}
          description="Alert when attendance devices go offline"
        />
      )}

      {/* Admin-specific alerts */}
      {type === "admin" && (
        <>
          {data.companyExpiryAlerts !== undefined && (
            <SettingsField
              type="switch"
              label="Company Expiry Alerts"
              checked={data.companyExpiryAlerts}
              onChange={(checked) => onChange("companyExpiryAlerts", checked)}
              description="Alert when company subscriptions are about to expire"
            />
          )}

          {data.systemMaintenanceAlerts !== undefined && (
            <SettingsField
              type="switch"
              label="System Maintenance Alerts"
              checked={data.systemMaintenanceAlerts}
              onChange={(checked) => onChange("systemMaintenanceAlerts", checked)}
              description="Receive notifications about system maintenance"
            />
          )}
        </>
      )}

      {/* Reports (company only) */}
      {type === "company" && (
        <>
          {data.weeklyReports !== undefined && (
            <SettingsField
              type="switch"
              label="Weekly Reports"
              checked={data.weeklyReports}
              onChange={(checked) => onChange("weeklyReports", checked)}
              description="Receive weekly attendance summary reports"
            />
          )}

          {data.monthlyReports !== undefined && (
            <SettingsField
              type="switch"
              label="Monthly Reports"
              checked={data.monthlyReports}
              onChange={(checked) => onChange("monthlyReports", checked)}
              description="Receive monthly attendance summary reports"
            />
          )}
        </>
      )}

      {/* Alert Thresholds (admin only) */}
      {type === "admin" && (
        <>
          {data.deviceOfflineMinutes !== undefined && (
            <SettingsField
              type="number"
              label="Device Offline Threshold (minutes)"
              value={data.deviceOfflineMinutes}
              onChange={(value) => onChange("deviceOfflineMinutes", parseInt(value) || 30)}
              min={5}
              max={1440}
              description="Alert when devices are offline for this many minutes"
            />
          )}

          {data.companyExpiryDays !== undefined && (
            <SettingsField
              type="number"
              label="Company Expiry Warning (days)"
              value={data.companyExpiryDays}
              onChange={(value) => onChange("companyExpiryDays", parseInt(value) || 7)}
              min={1}
              max={90}
              description="Alert this many days before company subscription expires"
            />
          )}
        </>
      )}
    </SettingsSection>
  );
}
