import { NextRequest, NextResponse } from 'next/server'
import { withAdminAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { prisma } from '@/lib/prisma'

// GET /api/admin/devices/allocations - Get all device allocations
async function getAllocations(req: AuthenticatedRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const deviceSerialNumbers = searchParams.get('devices')?.split(',') || []

    let whereClause = {}
    
    // If specific devices are requested, filter by them
    if (deviceSerialNumbers.length > 0 && deviceSerialNumbers[0] !== '') {
      whereClause = {
        deviceSerialNo: {
          in: deviceSerialNumbers
        }
      }
    }

    const allocations = await prisma.allocation.findMany({
      where: whereClause,
      include: {
        company: {
          select: {
            id: true,
            name: true,
            organizationId: true,
            email: true,
            status: true,
            userType: true,
            expiresAt: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Transform data for easier consumption
    const allocationMap = allocations.reduce((acc, allocation) => {
      const now = new Date()
      const daysRemaining = Math.ceil((allocation.company.expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

      acc[allocation.deviceSerialNo] = {
        deviceSerialNo: allocation.deviceSerialNo,
        company: {
          ...allocation.company,
          daysRemaining
        },
        allocatedAt: allocation.createdAt
      }
      return acc
    }, {} as Record<string, any>)

    // If specific devices were requested, return in the same order
    if (deviceSerialNumbers.length > 0 && deviceSerialNumbers[0] !== '') {
      const orderedAllocations = deviceSerialNumbers.map(serialNo => 
        allocationMap[serialNo] || null
      ).filter(Boolean)

      return NextResponse.json({
        allocations: orderedAllocations,
        allocationMap
      })
    }

    return NextResponse.json({
      allocations: Object.values(allocationMap),
      allocationMap
    })
  } catch (error) {
    console.error('Error fetching allocations:', error)
    return NextResponse.json(
      { error: 'Failed to fetch allocations' },
      { status: 500 }
    )
  }
}

export const GET = withAdminAuth(getAllocations)
