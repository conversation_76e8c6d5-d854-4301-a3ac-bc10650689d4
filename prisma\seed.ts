import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create default admin user
  const adminEmail = '<EMAIL>'
  const adminPassword = 'Admin@123'

  // Check if admin already exists
  const existingAdmin = await prisma.admin.findUnique({
    where: { email: adminEmail }
  })

  if (!existingAdmin) {
    // Hash the password
    const hashedPassword = await bcrypt.hash(adminPassword, 12)

    // Create admin user
    const admin = await prisma.admin.create({
      data: {
        name: 'System Administrator',
        email: adminEmail,
        passwordHash: hashedPassword,
        phone: '+****************',
        department: 'IT Administration',
        bio: 'System administrator responsible for managing the smart attendance platform.',
        loginCount: 0,
        twoFactorEnabled: false
      }
    })

    console.log('✅ Created admin user:', {
      id: admin.id,
      name: admin.name,
      email: admin.email
    })
  } else {
    console.log('ℹ️  Admin user already exists')
  }

  // Create some sample companies if they don't exist
  const existingCompanies = await prisma.company.count()
  
  if (existingCompanies === 0) {
    console.log('🏢 Creating sample companies...')

    const companies = [
      {
        name: 'Tech Solutions Inc',
        organizationId: 'TECH001',
        email: '<EMAIL>',
        passwordHash: await bcrypt.hash('Company@123', 12),
        userType: 'API_USER' as const,
        status: 'ACTIVE' as const,
        expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        description: 'Technology solutions and consulting company',
        phone: '+****************',
        address: '123 Tech Street, Silicon Valley, CA',
        contactPerson: 'John Smith',
        loginCount: 0,
        twoFactorEnabled: false
      },
      {
        name: 'Global Manufacturing Corp',
        organizationId: 'GLOBAL001',
        email: '<EMAIL>',
        passwordHash: await bcrypt.hash('Company@123', 12),
        userType: 'EMPLOYEE_MANAGEMENT' as const,
        status: 'ACTIVE' as const,
        expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        description: 'Large scale manufacturing and production company',
        phone: '+****************',
        address: '456 Industrial Blvd, Detroit, MI',
        contactPerson: 'Sarah Johnson',
        loginCount: 0,
        twoFactorEnabled: false
      },
      {
        name: 'Education First Academy',
        organizationId: 'EDU001',
        email: '<EMAIL>',
        passwordHash: await bcrypt.hash('Company@123', 12),
        userType: 'SCHOOL_MANAGEMENT' as const,
        status: 'ACTIVE' as const,
        expiresAt: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000), // 6 months from now
        description: 'Private educational institution',
        phone: '+****************',
        address: '789 Education Ave, Boston, MA',
        contactPerson: 'Dr. Michael Brown',
        loginCount: 0,
        twoFactorEnabled: false
      }
    ]

    for (const companyData of companies) {
      const company = await prisma.company.create({
        data: companyData
      })
      console.log(`✅ Created company: ${company.name} (${company.organizationId})`)
    }
  } else {
    console.log('ℹ️  Companies already exist')
  }

  console.log('🎉 Seeding completed!')
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
