import { NextRequest, NextResponse } from "next/server";
import { externalDeviceAPI } from "@/lib/api/external-device-api";
import { verifyAdminAuth } from "@/lib/auth";
import { z } from "zod";

// Transform external API user data to our internal format
function transformUserData(apiUser: any) {
  return {
    id: apiUser.RowId?.toString() || apiUser.EnrollmentNo,
    enrollmentNo: apiUser.EnrollmentNo,
    name: apiUser.Name || `User ${apiUser.EnrollmentNo}`,
    deviceSerial: apiUser.DevicesSlno,
    isAdmin: apiUser.Admin === 1,
    biometrics: {
      hasFace:
        typeof apiUser.FaceData === "string" && apiUser.FaceData.length > 0,
      hasFingerprint:
        apiUser.FingerData && Object.keys(apiUser.FingerData).length > 0,
      hasCard: apiUser.Card && Object.keys(apiUser.Card).length > 0,
      hasPassword:
        (typeof apiUser.PWD === "string" && apiUser.PWD !== "0") ||
        (typeof apiUser.PWD === "object" &&
          Object.keys(apiUser.PWD).length > 0),
    },
    isEnabled: apiUser.Enabled && Object.keys(apiUser.Enabled).length > 0,
    privilege: apiUser.Privilege,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    faceData: apiUser.FaceData || undefined, // Include faceData
  };
}

// GET /api/admin/devices/[deviceId]/users - Get all users for a device
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const deviceSerial = resolvedParams.deviceId;
    console.log(`Getting users for device: ${deviceSerial}`);

    // Step 1: Send command to device to prepare user data
    console.log("Sending GETCMDUSERDATA command...");
    const cmdResponse = await externalDeviceAPI.getCmdUserData([
      {
        DEVICESLNO: deviceSerial,
      },
    ]);

    if (cmdResponse.status !== "200") {
      return NextResponse.json(
        {
          error: `Failed to send user data command: ${cmdResponse.msg}`,
        },
        { status: 400 }
      );
    }

    console.log(
      "Command sent successfully, waiting for device to prepare data..."
    );

    // Step 2: Wait for device to prepare data (3 seconds as recommended)
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Step 3: Retrieve user data from device
    console.log("Retrieving user data...");
    const userResponse = await externalDeviceAPI.getUserData([
      {
        DEVICESLNO: deviceSerial,
      },
    ]);

    if (userResponse.status !== "200") {
      return NextResponse.json(
        {
          error: `Failed to retrieve user data: ${userResponse.msg}`,
        },
        { status: 400 }
      );
    }

    // Step 4: Transform data for frontend
    const users = (userResponse.data || []).map(transformUserData);

    console.log(
      `Successfully retrieved ${users.length} users for device ${deviceSerial}`
    );

    return NextResponse.json({
      success: true,
      data: users,
      meta: {
        deviceSerial,
        userCount: users.length,
        retrievedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Error retrieving device users:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/devices/[deviceId]/users/[userId] - Delete user from device
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const deviceSerial = resolvedParams.deviceId;
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json(
        {
          error: "User ID is required",
        },
        { status: 400 }
      );
    }

    console.log(`Deleting user ${userId} from device ${deviceSerial}`);

    // Delete user from device via external API
    const response = await externalDeviceAPI.deleteUser(deviceSerial, userId);

    if (response.status !== "200") {
      return NextResponse.json(
        {
          error: response.msg || "Failed to delete user",
        },
        { status: 400 }
      );
    }

    console.log(
      `Successfully deleted user ${userId} from device ${deviceSerial}`
    );

    return NextResponse.json({
      success: true,
      message: "User deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting user from device:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}

// PUT /api/admin/devices/[deviceId]/users - Enable/disable user on device
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const deviceSerial = resolvedParams.deviceId;
    const body = await request.json();
    const { userId, enabled } = body;

    if (!userId || enabled === undefined) {
      return NextResponse.json(
        {
          error: "User ID and enabled status are required",
        },
        { status: 400 }
      );
    }

    console.log(
      `${
        enabled ? "Enabling" : "Disabling"
      } user ${userId} on device ${deviceSerial}`
    );

    // Enable/disable user on device via external API
    const response = await externalDeviceAPI.enableUser([
      {
        DEVICESLNO: deviceSerial,
        USERID: userId,
        STATUS: enabled ? "ENABLE" : "DISABLE",
      },
    ]);

    if (response.status !== "200") {
      return NextResponse.json(
        {
          error:
            response.msg || `Failed to ${enabled ? "enable" : "disable"} user`,
        },
        { status: 400 }
      );
    }

    console.log(
      `Successfully ${
        enabled ? "enabled" : "disabled"
      } user ${userId} on device ${deviceSerial}`
    );

    return NextResponse.json({
      success: true,
      message: `User ${enabled ? "enabled" : "disabled"} successfully`,
    });
  } catch (error) {
    console.error("Error updating user status:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}

// Validation schemas for user management actions
const addUserSchema = z.object({
  action: z.literal("addUser"),
  userId: z.string().min(1, "User ID is required"),
  userName: z.string().min(1, "User name is required"),
});

const addUserWithValiditySchema = z.object({
  action: z.literal("addUserWithValidity"),
  userId: z.string().min(1, "User ID is required"),
  userName: z.string().min(1, "User name is required"),
  validityDate: z.string().min(1, "Validity date is required"),
});

const addAdminSchema = z.object({
  action: z.literal("addAdmin"),
  userId: z.string().min(1, "User ID is required"),
  userName: z.string().min(1, "User name is required"),
  faceData: z.string().optional(),
});

const clearAllUsersSchema = z.object({
  action: z.literal("clearAllUsers"),
});

const clearAllAdminsSchema = z.object({
  action: z.literal("clearAllAdmins"),
});

const getUserDataSchema = z.object({
  action: z.literal("getUserData"),
});

const deleteUserSchema = z.object({
  action: z.literal("deleteUser"),
  userId: z.string().min(1, "User ID is required"),
});

const toggleAdminSchema = z.object({
  action: z.enum(["makeAdmin", "removeAdmin"]),
  userId: z.string().min(1, "User ID is required"),
});

const toggleUserStatusSchema = z.object({
  action: z.enum(["enableUser", "disableUser"]),
  userId: z.string().min(1, "User ID is required"),
});

const setValiditySchema = z.object({
  action: z.literal("setValidity"),
  userId: z.string().min(1, "User ID is required"),
  fromDate: z.string().min(1, "From date is required"),
  toDate: z.string().min(1, "To date is required"),
});

// POST /api/admin/devices/[deviceId]/users - Add users, admins, etc.
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const deviceSerial = resolvedParams.deviceId;
    const body = await request.json();

    // Validate action type
    if (!body.action) {
      return NextResponse.json(
        { error: "Action is required" },
        { status: 400 }
      );
    }

    switch (body.action) {
      case "addUser": {
        const validatedData = addUserSchema.parse(body);

        // Add user to device using external API
        const response = await externalDeviceAPI.addUser({
          DEVICESLNO: deviceSerial,
          USERID: validatedData.userId,
          USERNAME: validatedData.userName,
          ADMIN: "0", // Regular user
        });

        if (response.status === "200") {
          return NextResponse.json({
            success: true,
            message: "User added successfully",
            data: {
              userId: validatedData.userId,
              userName: validatedData.userName,
            },
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to add user",
            },
            { status: 400 }
          );
        }
      }

      case "addUserWithValidity": {
        const validatedData = addUserWithValiditySchema.parse(body);

        // Convert date to required format (YYYY-MM-DD HH:mm:ss)
        const validityDate = new Date(validatedData.validityDate);
        const formattedDate = validityDate
          .toISOString()
          .slice(0, 19)
          .replace("T", " ");

        // Add user with validity to device using external API
        const response = await externalDeviceAPI.addUserWithValidity({
          DEVICESLNO: deviceSerial,
          USERID: validatedData.userId,
          USERNAME: validatedData.userName,
          ADMIN: "0", // Regular user
          FROMDATE: new Date().toISOString().slice(0, 19).replace("T", " "), // Current date
          TODATE: formattedDate,
        });

        if (response.status === "200") {
          return NextResponse.json({
            message: "User with validity added successfully",
            userId: validatedData.userId,
            userName: validatedData.userName,
            validityDate: validatedData.validityDate,
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to add user with validity",
            },
            { status: 400 }
          );
        }
      }

      case "addAdmin": {
        const validatedData = addAdminSchema.parse(body);

        // Prepare admin data
        const adminData: any = {
          DEVICESLNO: deviceSerial,
          USERID: validatedData.userId,
          USERNAME: validatedData.userName,
          ADMIN: "1", // Admin user
        };

        // Add face data if provided
        if (validatedData.faceData) {
          adminData.FACEDATA = validatedData.faceData;
        }

        // Add admin to device using external API
        const response = await externalDeviceAPI.addUser(adminData);

        if (response.status === "200") {
          return NextResponse.json({
            message: "Admin added successfully",
            userId: validatedData.userId,
            userName: validatedData.userName,
            hasFaceData: !!validatedData.faceData,
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to add admin",
            },
            { status: 400 }
          );
        }
      }

      case "clearAllUsers": {
        clearAllUsersSchema.parse(body);

        // Clear all users using external API
        const response = await externalDeviceAPI.clearAllUser(deviceSerial);

        if (response.status === "200") {
          return NextResponse.json({
            message: "All users cleared successfully",
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to clear all users",
            },
            { status: 400 }
          );
        }
      }

      case "clearAllAdmins": {
        clearAllAdminsSchema.parse(body);

        // Clear all admins using external API
        const response = await externalDeviceAPI.clearAdmin(deviceSerial);

        if (response.status === "200") {
          return NextResponse.json({
            success: true,
            message: "All admins cleared successfully",
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to clear all admins",
            },
            { status: 400 }
          );
        }
      }

      case "getUserData": {
        getUserDataSchema.parse(body);

        console.log(`Getting users for device: ${deviceSerial}`);

        // Step 1: Send command to device to prepare user data
        console.log("Sending GETCMDUSERDATA command...");
        const cmdResponse = await externalDeviceAPI.getCmdUserData([
          {
            DEVICESLNO: deviceSerial,
          },
        ]);

        if (cmdResponse.status !== "200") {
          return NextResponse.json(
            {
              error: `Failed to send user data command: ${cmdResponse.msg}`,
            },
            { status: 400 }
          );
        }

        console.log(
          "Command sent successfully, waiting for device to prepare data..."
        );

        // Step 2: Wait for device to prepare data (3 seconds as recommended)
        await new Promise((resolve) => setTimeout(resolve, 3000));

        // Step 3: Retrieve user data from device
        console.log("Retrieving user data...");
        const userResponse = await externalDeviceAPI.getUserData([
          {
            DEVICESLNO: deviceSerial,
          },
        ]);

        if (userResponse.status !== "200") {
          return NextResponse.json(
            {
              error: `Failed to retrieve user data: ${userResponse.msg}`,
            },
            { status: 400 }
          );
        }

        // Step 4: Transform data for frontend
        const users = (userResponse.data || []).map(transformUserData);

        console.log(
          `Successfully retrieved ${users.length} users for device ${deviceSerial}`
        );

        return NextResponse.json({
          success: true,
          message: `Retrieved ${users.length} users successfully`,
          data: users,
          meta: {
            deviceSerial,
            userCount: users.length,
            retrievedAt: new Date().toISOString(),
          },
        });
      }

      case "deleteUser": {
        const validatedData = deleteUserSchema.parse(body);

        console.log(
          `Deleting user ${validatedData.userId} from device: ${deviceSerial}`
        );

        // Delete user from device using external API
        const response = await externalDeviceAPI.deleteUser(
          deviceSerial,
          validatedData.userId
        );

        if (response.status === "200") {
          console.log(
            `Successfully deleted user ${validatedData.userId} from device ${deviceSerial}`
          );

          return NextResponse.json({
            success: true,
            message: `User ${validatedData.userId} deleted successfully`,
            data: {
              userId: validatedData.userId,
              deviceSerial,
            },
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to delete user",
            },
            { status: 400 }
          );
        }
      }

      case "makeAdmin": {
        const validatedData = toggleAdminSchema.parse(body);

        console.log(
          `Making user ${validatedData.userId} an admin on device: ${deviceSerial}`
        );

        // Add admin using external API
        const response = await externalDeviceAPI.addAdmin([
          {
            DEVICESLNO: deviceSerial,
            USERID: validatedData.userId,
          },
        ]);

        if (response.status === "200") {
          console.log(
            `Successfully made user ${validatedData.userId} an admin`
          );

          return NextResponse.json({
            success: true,
            message: `User ${validatedData.userId} is now an admin`,
            data: {
              userId: validatedData.userId,
              deviceSerial,
              isAdmin: true,
            },
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to make user admin",
            },
            { status: 400 }
          );
        }
      }

      case "removeAdmin": {
        const validatedData = toggleAdminSchema.parse(body);

        console.log(
          `Removing admin privileges from user ${validatedData.userId} on device: ${deviceSerial}`
        );

        // Remove admin using external API
        const response = await externalDeviceAPI.deleteAdmin([
          {
            DEVICESLNO: deviceSerial,
            USERID: validatedData.userId,
          },
        ]);

        if (response.status === "200") {
          console.log(
            `Successfully removed admin privileges from user ${validatedData.userId}`
          );

          return NextResponse.json({
            success: true,
            message: `Admin privileges removed from user ${validatedData.userId}`,
            data: {
              userId: validatedData.userId,
              deviceSerial,
              isAdmin: false,
            },
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to remove admin privileges",
            },
            { status: 400 }
          );
        }
      }

      case "enableUser": {
        const validatedData = toggleUserStatusSchema.parse(body);

        console.log(
          `Enabling user ${validatedData.userId} on device: ${deviceSerial}`
        );

        // Enable user using external API
        const response = await externalDeviceAPI.enableUser([
          {
            DEVICESLNO: deviceSerial,
            USERID: validatedData.userId,
            STATUS: "ENABLE",
          },
        ]);

        if (response.status === "200") {
          console.log(`Successfully enabled user ${validatedData.userId}`);

          return NextResponse.json({
            success: true,
            message: `User ${validatedData.userId} enabled successfully`,
            data: {
              userId: validatedData.userId,
              deviceSerial,
              isEnabled: true,
            },
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to enable user",
            },
            { status: 400 }
          );
        }
      }

      case "disableUser": {
        const validatedData = toggleUserStatusSchema.parse(body);

        console.log(
          `Disabling user ${validatedData.userId} on device: ${deviceSerial}`
        );

        // Disable user using external API
        const response = await externalDeviceAPI.enableUser([
          {
            DEVICESLNO: deviceSerial,
            USERID: validatedData.userId,
            STATUS: "DISABLE",
          },
        ]);

        if (response.status === "200") {
          console.log(`Successfully disabled user ${validatedData.userId}`);

          return NextResponse.json({
            success: true,
            message: `User ${validatedData.userId} disabled successfully`,
            data: {
              userId: validatedData.userId,
              deviceSerial,
              isEnabled: false,
            },
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to disable user",
            },
            { status: 400 }
          );
        }
      }

      case "setValidity": {
        const validatedData = setValiditySchema.parse(body);

        console.log(
          `Setting validity for user ${validatedData.userId} on device: ${deviceSerial}`
        );

        // Set validity using external API
        const response = await externalDeviceAPI.setValidity({
          DEVICESLNO: deviceSerial,
          USERID: validatedData.userId,
          FROMDATE: validatedData.fromDate,
          TODATE: validatedData.toDate,
        });

        if (response.status === "200") {
          console.log(
            `Successfully set validity for user ${validatedData.userId}`
          );

          return NextResponse.json({
            success: true,
            message: `Validity set for user ${validatedData.userId}`,
            data: {
              userId: validatedData.userId,
              deviceSerial,
              fromDate: validatedData.fromDate,
              toDate: validatedData.toDate,
            },
          });
        } else {
          return NextResponse.json(
            {
              error: response.msg || "Failed to set user validity",
            },
            { status: 400 }
          );
        }
      }

      default:
        return NextResponse.json(
          {
            error: "Invalid action",
          },
          { status: 400 }
        );
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: "Invalid input",
          details: error.issues,
        },
        { status: 400 }
      );
    }

    console.error("Error in user management:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}
