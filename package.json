{"name": "smart-attendance-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts", "db:reset": "npx prisma migrate reset && npm run db:seed", "db:init-settings": "node scripts/init-settings.js", "build:production": "npm run build", "db:migrate": "npx prisma migrate deploy", "db:generate": "npx prisma generate", "db:setup": "npm run db:generate && npm run db:migrate && npm run db:seed", "postinstall": "npx prisma generate", "production:setup": "node scripts/production-setup.js", "production:deploy": "npm run production:setup && npm run db:setup && npm run build:production", "backup": "node scripts/backup-production.js"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.12.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/qrcode": "^1.5.5", "@types/react-datepicker": "^6.2.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "nanoid": "^5.1.5", "next": "15.4.2", "next-themes": "^0.4.6", "otplib": "^12.0.1", "qrcode": "^1.5.4", "react": "19.1.0", "react-datepicker": "^8.4.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-toastify": "^11.0.5", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.2", "postcss": "^8.5.6", "prisma": "^6.12.0", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5"}}