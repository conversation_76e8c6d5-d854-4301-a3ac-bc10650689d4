"use client";

import { useState, useEffect, useRef } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import DatePicker from "@/components/DatePicker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Info,
  Users,
  Clock,
  Settings,
  RefreshCw,
  CheckCircle,
  XCircle,
  Upload,
  User<PERSON>lus,
  Shield,
  Calendar,
  Image,
} from "lucide-react";
import { toast } from "sonner";

interface DeviceActionsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deviceId: string;
}

interface UserFormData {
  userId: string;
  userName: string;
  userType: "user" | "admin";
  validityDate?: Date;
  faceData?: string;
}

interface AddUserFormData {
  userId: string;
  userName: string;
}

interface AddUserWithValidityFormData {
  userId: string;
  userName: string;
  validityDate: Date;
}

interface AddAdminFormData {
  userId: string;
  userName: string;
  faceData?: string;
}

interface CachedDeviceData {
  deviceSerialNumber: string;
  apiResponse: any;
  lastUpdated: string;
  extractedData: {
    deviceName: string;
    modelNo: string;
    modelName: string;
    location: string;
    userCount: number;
    logCount: number;
    timeZone: number;
    endpointUrl: string;
    features: {
      face: boolean;
      finger: boolean;
      card: boolean;
      password: boolean;
    };
    biometricCounts: {
      fingerprints: number;
      faces: number;
      cards: number;
      passwords: number;
    };
  };
}

export function DeviceActionsDialog({
  open,
  onOpenChange,
  deviceId,
}: DeviceActionsDialogProps) {
  const [cachedData, setCachedData] = useState<CachedDeviceData | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingCachedData, setLoadingCachedData] = useState(false);
  const [activeTab, setActiveTab] = useState("device-info");

  // User management form states
  const [addUserForm, setAddUserForm] = useState<AddUserFormData>({
    userId: "",
    userName: "",
  });
  const [addUserWithValidityForm, setAddUserWithValidityForm] =
    useState<AddUserWithValidityFormData>({
      userId: "",
      userName: "",
      validityDate: new Date(),
    });
  const [addAdminForm, setAddAdminForm] = useState<AddAdminFormData>({
    userId: "",
    userName: "",
    faceData: "",
  });

  // Form loading states
  const [addingUser, setAddingUser] = useState(false);
  const [addingUserWithValidity, setAddingUserWithValidity] = useState(false);
  const [addingAdmin, setAddingAdmin] = useState(false);
  const [gettingUserData, setGettingUserData] = useState(false);
  const [clearingUsers, setClearingUsers] = useState(false);
  const [clearingAdmins, setClearingAdmins] = useState(false);

  // File upload ref
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch cached data when dialog opens
  useEffect(() => {
    if (open && deviceId) {
      fetchCachedData();
    }
  }, [open, deviceId]);

  const fetchCachedData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/devices/${deviceId}/api-cache`);
      if (response.ok) {
        const result = await response.json();
        setCachedData(result.data);
      } else {
        toast.warning(
          "No cached device data found. Some information may be limited."
        );
      }
    } catch (error) {
      console.error("Error fetching device info:", error);
      toast.error("Failed to load device information");
    } finally {
      setLoading(false);
    }
  };

  const refreshCachedData = async () => {
    try {
      setLoadingCachedData(true);
      const response = await fetch(`/api/admin/devices/${deviceId}/api-cache`);
      if (response.ok) {
        const result = await response.json();
        setCachedData(result.data);
        toast.success("Device data refreshed successfully");
      } else {
        toast.error("Failed to refresh device data");
      }
    } catch (error) {
      console.error("Error refreshing cached data:", error);
      toast.error("Failed to refresh device data");
    } finally {
      setLoadingCachedData(false);
    }
  };

  // Image compression utility
  const compressImage = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new window.Image();

      img.onload = () => {
        // Calculate new dimensions to maintain aspect ratio
        const maxWidth = 800;
        const maxHeight = 600;
        let { width, height } = img;

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);

        // Convert to base64 with compression
        let quality = 0.8;
        let dataUrl = canvas.toDataURL("image/jpeg", quality);

        // Reduce quality until under 200KB
        while (dataUrl.length > 200 * 1024 && quality > 0.1) {
          quality -= 0.1;
          dataUrl = canvas.toDataURL("image/jpeg", quality);
        }

        resolve(dataUrl);
      };

      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  };

  // Handle file upload for face data
  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      toast.error("Please select an image file");
      return;
    }

    // Validate file size (max 10MB before compression)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("File size must be less than 10MB");
      return;
    }

    try {
      const compressedBase64 = await compressImage(file);
      setAddAdminForm((prev) => ({ ...prev, faceData: compressedBase64 }));
      toast.success("Face image uploaded and compressed successfully");
    } catch (error) {
      console.error("Error compressing image:", error);
      toast.error("Failed to process image");
    }
  };

  // Form submission handlers
  const handleAddUser = async () => {
    if (!addUserForm.userId || !addUserForm.userName) {
      toast.error("Please fill in all required fields");
      return;
    }

    setAddingUser(true);
    try {
      const response = await fetch(`/api/admin/devices/${deviceId}/users`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "addUser",
          userId: addUserForm.userId,
          userName: addUserForm.userName,
        }),
      });

      if (response.ok) {
        toast.success("User added successfully");
        setAddUserForm({ userId: "", userName: "" });
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to add user");
      }
    } catch (error) {
      console.error("Error adding user:", error);
      toast.error("Failed to add user");
    } finally {
      setAddingUser(false);
    }
  };

  const handleAddUserWithValidity = async () => {
    if (
      !addUserWithValidityForm.userId ||
      !addUserWithValidityForm.userName ||
      !addUserWithValidityForm.validityDate
    ) {
      toast.error("Please fill in all required fields");
      return;
    }

    setAddingUserWithValidity(true);
    try {
      const response = await fetch(`/api/admin/devices/${deviceId}/users`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "addUserWithValidity",
          userId: addUserWithValidityForm.userId,
          userName: addUserWithValidityForm.userName,
          validityDate: addUserWithValidityForm.validityDate.toISOString(),
        }),
      });

      if (response.ok) {
        toast.success("User with validity added successfully");
        setAddUserWithValidityForm({
          userId: "",
          userName: "",
          validityDate: new Date(),
        });
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to add user with validity");
      }
    } catch (error) {
      console.error("Error adding user with validity:", error);
      toast.error("Failed to add user with validity");
    } finally {
      setAddingUserWithValidity(false);
    }
  };

  const handleAddAdmin = async () => {
    if (!addAdminForm.userId || !addAdminForm.userName) {
      toast.error("Please fill in all required fields");
      return;
    }

    setAddingAdmin(true);
    try {
      const response = await fetch(`/api/admin/devices/${deviceId}/users`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "addAdmin",
          userId: addAdminForm.userId,
          userName: addAdminForm.userName,
          faceData: addAdminForm.faceData,
        }),
      });

      if (response.ok) {
        toast.success("Admin added successfully");
        setAddAdminForm({ userId: "", userName: "", faceData: "" });
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to add admin");
      }
    } catch (error) {
      console.error("Error adding admin:", error);
      toast.error("Failed to add admin");
    } finally {
      setAddingAdmin(false);
    }
  };

  // Handler for getting user data
  const handleGetUserData = async () => {
    setGettingUserData(true);
    try {
      const response = await fetch(`/api/admin/devices/${deviceId}/users`, {
        method: "GET",
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(`Retrieved ${data.users?.length || 0} users from device`);
        // TODO: Display user data in a table (Task 8)
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to get user data");
      }
    } catch (error) {
      console.error("Error getting user data:", error);
      toast.error("Failed to get user data");
    } finally {
      setGettingUserData(false);
    }
  };

  // Handler for clearing all users
  const handleClearAllUsers = async () => {
    if (
      !confirm(
        "Are you sure you want to clear all users from this device? This action cannot be undone."
      )
    ) {
      return;
    }

    setClearingUsers(true);
    try {
      const response = await fetch(`/api/admin/devices/${deviceId}/users`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "clearAllUsers",
        }),
      });

      if (response.ok) {
        toast.success("All users cleared successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to clear all users");
      }
    } catch (error) {
      console.error("Error clearing users:", error);
      toast.error("Failed to clear all users");
    } finally {
      setClearingUsers(false);
    }
  };

  // Handler for clearing all admins
  const handleClearAllAdmins = async () => {
    if (
      !confirm(
        "Are you sure you want to clear all admins from this device? This action cannot be undone."
      )
    ) {
      return;
    }

    setClearingAdmins(true);
    try {
      const response = await fetch(`/api/admin/devices/${deviceId}/users`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "clearAllAdmins",
        }),
      });

      if (response.ok) {
        toast.success("All admins cleared successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to clear all admins");
      }
    } catch (error) {
      console.error("Error clearing admins:", error);
      toast.error("Failed to clear all admins");
    } finally {
      setClearingAdmins(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div>
              Device Actions -{" "}
              {cachedData?.extractedData.deviceName || deviceId}
            </div>
            {cachedData && (
              <Badge variant="outline">{cachedData.deviceSerialNumber}</Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            Manage device information, users, logs, and perform actions
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-sm text-muted-foreground">
                Loading device information...
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-6 bg-red-600">
            {/* Device Summary */}
            {cachedData && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Info className="h-5 w-5 mr-2" />
                    Device Summary
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Serial Number
                      </p>
                      <p className="text-sm font-mono">
                        {cachedData.deviceSerialNumber}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Device Name
                      </p>
                      <p className="text-sm">
                        {cachedData.extractedData.deviceName}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Model
                      </p>
                      <p className="text-sm">
                        {cachedData.extractedData.modelName}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Location
                      </p>
                      <p className="text-sm">
                        {cachedData.extractedData.location}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Action Categories */}
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="device-info" className="flex items-center">
                  <Info className="h-4 w-4 mr-2" />
                  Device Info
                </TabsTrigger>
                <TabsTrigger value="device-users" className="flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  Device Users
                </TabsTrigger>
                <TabsTrigger
                  value="attendance-logs"
                  className="flex items-center"
                >
                  <Clock className="h-4 w-4 mr-2" />
                  Attendance Logs
                </TabsTrigger>
                <TabsTrigger
                  value="device-actions"
                  className="flex items-center"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Device Actions
                </TabsTrigger>
              </TabsList>

              <TabsContent value="device-info" className="mt-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                      <CardTitle>Device Information</CardTitle>
                      <CardDescription>
                        Detailed information from external API response
                      </CardDescription>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={refreshCachedData}
                      disabled={loadingCachedData}
                    >
                      <RefreshCw
                        className={`h-4 w-4 mr-2 ${
                          loadingCachedData ? "animate-spin" : ""
                        }`}
                      />
                      Refresh
                    </Button>
                  </CardHeader>
                  <CardContent>
                    {cachedData ? (
                      <div className="space-y-6">
                        {/* Basic Device Information */}
                        <div>
                          <h3 className="text-lg font-semibold mb-3">
                            Basic Information
                          </h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div className="space-y-1">
                              <p className="text-sm font-medium text-muted-foreground">
                                Serial Number
                              </p>
                              <p className="text-sm font-mono">
                                {cachedData.deviceSerialNumber}
                              </p>
                            </div>
                            <div className="space-y-1">
                              <p className="text-sm font-medium text-muted-foreground">
                                Device Name
                              </p>
                              <p className="text-sm">
                                {cachedData.extractedData.deviceName}
                              </p>
                            </div>
                            <div className="space-y-1">
                              <p className="text-sm font-medium text-muted-foreground">
                                Model Number
                              </p>
                              <p className="text-sm">
                                {cachedData.extractedData.modelNo}
                              </p>
                            </div>
                            <div className="space-y-1">
                              <p className="text-sm font-medium text-muted-foreground">
                                Model Name
                              </p>
                              <p className="text-sm">
                                {cachedData.extractedData.modelName}
                              </p>
                            </div>
                            <div className="space-y-1">
                              <p className="text-sm font-medium text-muted-foreground">
                                Location
                              </p>
                              <p className="text-sm">
                                {cachedData.extractedData.location}
                              </p>
                            </div>
                            <div className="space-y-1">
                              <p className="text-sm font-medium text-muted-foreground">
                                Time Zone
                              </p>
                              <p className="text-sm">
                                {cachedData.extractedData.timeZone}
                              </p>
                            </div>
                          </div>
                        </div>

                        <Separator />

                        {/* Statistics */}
                        <div>
                          <h3 className="text-lg font-semibold mb-3">
                            Statistics
                          </h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div className="space-y-1">
                              <p className="text-sm font-medium text-muted-foreground">
                                Total Users
                              </p>
                              <p className="text-2xl font-bold">
                                {cachedData.extractedData.userCount}
                              </p>
                            </div>
                            <div className="space-y-1">
                              <p className="text-sm font-medium text-muted-foreground">
                                Total Logs
                              </p>
                              <p className="text-2xl font-bold">
                                {cachedData.extractedData.logCount}
                              </p>
                            </div>
                            <div className="space-y-1">
                              <p className="text-sm font-medium text-muted-foreground">
                                Face Records
                              </p>
                              <p className="text-2xl font-bold">
                                {cachedData.extractedData.biometricCounts.faces}
                              </p>
                            </div>
                            <div className="space-y-1">
                              <p className="text-sm font-medium text-muted-foreground">
                                Fingerprint Records
                              </p>
                              <p className="text-2xl font-bold">
                                {
                                  cachedData.extractedData.biometricCounts
                                    .fingerprints
                                }
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Last Updated */}
                        <div className="text-sm text-muted-foreground">
                          Last updated:{" "}
                          {new Date(cachedData.lastUpdated).toLocaleString()}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <Info className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                        <p className="text-lg font-medium">
                          No Device Data Available
                        </p>
                        <p className="text-sm text-muted-foreground mb-4">
                          No cached device data found. Try refreshing to fetch
                          the latest information.
                        </p>
                        <Button
                          onClick={refreshCachedData}
                          disabled={loadingCachedData}
                        >
                          <RefreshCw
                            className={`h-4 w-4 mr-2 ${
                              loadingCachedData ? "animate-spin" : ""
                            }`}
                          />
                          Refresh Device Data
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Other tabs with placeholder content */}
              <TabsContent value="device-users" className="mt-6">
                <div className="space-y-6">
                  {/* Add User Form */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <UserPlus className="h-5 w-5 mr-2" />
                        Add User
                      </CardTitle>
                      <CardDescription>
                        Add a new user to the device
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="userId">User ID</Label>
                          <Input
                            id="userId"
                            placeholder="Enter user ID"
                            value={addUserForm.userId}
                            onChange={(e) =>
                              setAddUserForm((prev) => ({
                                ...prev,
                                userId: e.target.value,
                              }))
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="userName">User Name</Label>
                          <Input
                            id="userName"
                            placeholder="Enter user name"
                            value={addUserForm.userName}
                            onChange={(e) =>
                              setAddUserForm((prev) => ({
                                ...prev,
                                userName: e.target.value,
                              }))
                            }
                          />
                        </div>
                      </div>
                      <div className="mt-4">
                        <Button
                          onClick={handleAddUser}
                          disabled={addingUser}
                          className="w-full md:w-auto"
                        >
                          {addingUser ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Adding User...
                            </>
                          ) : (
                            <>
                              <UserPlus className="h-4 w-4 mr-2" />
                              Add User
                            </>
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Add User with Validity Form */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Calendar className="h-5 w-5 mr-2" />
                        Add User with Validity
                      </CardTitle>
                      <CardDescription>
                        Add a user with an expiration date
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="userIdValidity">User ID</Label>
                          <Input
                            id="userIdValidity"
                            placeholder="Enter user ID"
                            value={addUserWithValidityForm.userId}
                            onChange={(e) =>
                              setAddUserWithValidityForm((prev) => ({
                                ...prev,
                                userId: e.target.value,
                              }))
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="userNameValidity">User Name</Label>
                          <Input
                            id="userNameValidity"
                            placeholder="Enter user name"
                            value={addUserWithValidityForm.userName}
                            onChange={(e) =>
                              setAddUserWithValidityForm((prev) => ({
                                ...prev,
                                userName: e.target.value,
                              }))
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="validityDate">Validity Date</Label>
                          <DatePicker
                            value={addUserWithValidityForm.validityDate}
                            onChange={(date) =>
                              setAddUserWithValidityForm((prev) => ({
                                ...prev,
                                validityDate: date || new Date(),
                              }))
                            }
                            placeholder="Select validity date"
                            className="w-full"
                          />
                        </div>
                      </div>
                      <div className="mt-4">
                        <Button
                          onClick={handleAddUserWithValidity}
                          disabled={addingUserWithValidity}
                          className="w-full md:w-auto"
                        >
                          {addingUserWithValidity ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Adding User...
                            </>
                          ) : (
                            <>
                              <Calendar className="h-4 w-4 mr-2" />
                              Add User with Validity
                            </>
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Add Admin Form */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Shield className="h-5 w-5 mr-2" />
                        Add Admin
                      </CardTitle>
                      <CardDescription>
                        Add an admin user with optional face data
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="adminUserId">User ID</Label>
                            <Input
                              id="adminUserId"
                              placeholder="Enter admin user ID"
                              value={addAdminForm.userId}
                              onChange={(e) =>
                                setAddAdminForm((prev) => ({
                                  ...prev,
                                  userId: e.target.value,
                                }))
                              }
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="adminUserName">User Name</Label>
                            <Input
                              id="adminUserName"
                              placeholder="Enter admin user name"
                              value={addAdminForm.userName}
                              onChange={(e) =>
                                setAddAdminForm((prev) => ({
                                  ...prev,
                                  userName: e.target.value,
                                }))
                              }
                            />
                          </div>
                        </div>

                        {/* Face Data Upload */}
                        <div className="space-y-2">
                          <Label htmlFor="faceData">Face Data (Optional)</Label>
                          <div className="flex items-center space-x-4">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => fileInputRef.current?.click()}
                              className="flex items-center"
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              Upload Face Image
                            </Button>
                            <input
                              ref={fileInputRef}
                              type="file"
                              accept="image/*"
                              onChange={handleFileUpload}
                              className="hidden"
                            />
                            {addAdminForm.faceData && (
                              <Badge
                                variant="secondary"
                                className="flex items-center"
                              >
                                <Image className="h-3 w-3 mr-1" />
                                Image uploaded
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Upload a face image (max 10MB). Will be compressed
                            to under 200KB.
                          </p>
                        </div>

                        <div className="mt-4">
                          <Button
                            onClick={handleAddAdmin}
                            disabled={addingAdmin}
                            className="w-full md:w-auto"
                          >
                            {addingAdmin ? (
                              <>
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                Adding Admin...
                              </>
                            ) : (
                              <>
                                <Shield className="h-4 w-4 mr-2" />
                                Add Admin
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Action Buttons */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Settings className="h-5 w-5 mr-2" />
                        User Management Actions
                      </CardTitle>
                      <CardDescription>
                        Manage device users and administrators
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Button
                          variant="outline"
                          onClick={handleGetUserData}
                          disabled={gettingUserData}
                          className="w-full"
                        >
                          {gettingUserData ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Getting Data...
                            </>
                          ) : (
                            <>
                              <Users className="h-4 w-4 mr-2" />
                              Get User Data
                            </>
                          )}
                        </Button>

                        <Button
                          variant="destructive"
                          onClick={handleClearAllUsers}
                          disabled={clearingUsers}
                          className="w-full"
                        >
                          {clearingUsers ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Clearing...
                            </>
                          ) : (
                            <>
                              <XCircle className="h-4 w-4 mr-2" />
                              Clear All Users
                            </>
                          )}
                        </Button>

                        <Button
                          variant="destructive"
                          onClick={handleClearAllAdmins}
                          disabled={clearingAdmins}
                          className="w-full"
                        >
                          {clearingAdmins ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Clearing...
                            </>
                          ) : (
                            <>
                              <Shield className="h-4 w-4 mr-2" />
                              Clear All Admins
                            </>
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="attendance-logs" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Attendance Logs</CardTitle>
                    <CardDescription>
                      View, download, and manage attendance logs from this
                      device
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <Clock className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <p className="text-lg font-medium">
                        Attendance Logs Management
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Get logs, download reports, and clear attendance data
                      </p>
                      <Button
                        className="mt-4"
                        onClick={() =>
                          toast.info(
                            "Attendance Logs functionality will be implemented in Task 10"
                          )
                        }
                      >
                        View Logs
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="device-actions" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Device Actions</CardTitle>
                    <CardDescription>
                      Perform remote actions on this device
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <Settings className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <p className="text-lg font-medium">
                        Device Remote Actions
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Remote register face/finger, open door, reboot device
                      </p>
                      <Button
                        className="mt-4"
                        onClick={() =>
                          toast.info(
                            "Device Actions functionality will be implemented in Task 11"
                          )
                        }
                      >
                        Device Actions
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
