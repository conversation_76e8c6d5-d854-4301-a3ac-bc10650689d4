"use client";

import { useMemo } from "react";
import { toast } from "react-toastify";
import { CompanyWithAllocations } from "@/types";

interface UseCompanyBulkActionsProps {
  selectedCompanies: CompanyWithAllocations[];
  onCompanyAction: (action: string) => void;
}

export function useCompanyBulkActions({
  selectedCompanies,
  onCompanyAction,
}: UseCompanyBulkActionsProps) {
  const bulkActions = useMemo(() => [
    {
      label: "Bulk Activate",
      onClick: () => {
        if (selectedCompanies.length === 0) {
          toast.error("Please select companies to activate");
          return;
        }
        onCompanyAction("bulkActivate");
      },
    },
    {
      label: "Bulk Deactivate",
      onClick: () => {
        if (selectedCompanies.length === 0) {
          toast.error("Please select companies to deactivate");
          return;
        }
        onCompanyAction("bulkDeactivate");
      },
      variant: "default" as const,
    },
    {
      label: "Export Selected",
      onClick: () => {
        if (selectedCompanies.length === 0) {
          toast.error("Please select companies to export");
          return;
        }
        toast.info("Export functionality coming soon");
      },
    },
    {
      label: "Bulk Delete",
      onClick: () => {
        if (selectedCompanies.length === 0) {
          toast.error("Please select companies to delete");
          return;
        }
        toast.info("Bulk delete functionality coming soon");
      },
      variant: "destructive" as const,
    },
  ], [selectedCompanies, onCompanyAction]);

  return bulkActions;
}
