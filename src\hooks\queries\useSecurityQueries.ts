import { useMutation, useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "@/lib/queryKeys";
import axios from "axios";

// Types
interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

export interface Setup2FAResponse {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

interface Verify2FAData {
  token: string;
  secret: string;
}

// API functions
const changeAdminPassword = async (data: ChangePasswordData) => {
  const response = await axios.post(
    "/api/admin/security/change-password",
    data
  );
  return response.data;
};

const changeCompanyPassword = async (data: ChangePasswordData) => {
  const response = await axios.post(
    "/api/dashboard/security/change-password",
    data
  );
  return response.data;
};

const setup2FA = async (userType: "admin" | "company") => {
  const endpoint =
    userType === "admin"
      ? "/api/admin/security/2fa/setup"
      : "/api/dashboard/security/2fa/setup";
  const response = await axios.post(endpoint);
  return response.data.data; // Extract the actual data from the API response
};

const verify2FA = async (
  data: Verify2FAData & { userType: "admin" | "company" }
) => {
  const endpoint =
    data.userType === "admin"
      ? "/api/admin/security/2fa/verify"
      : "/api/dashboard/security/2fa/verify";
  const response = await axios.post(endpoint, {
    token: data.token,
    secret: data.secret,
  });
  return response.data; // Return the full response for verify (no nested data)
};

const disable2FA = async (data: {
  token: string;
  userType: "admin" | "company";
}) => {
  const endpoint =
    data.userType === "admin"
      ? "/api/admin/security/2fa/disable"
      : "/api/dashboard/security/2fa/disable";
  const response = await axios.post(endpoint, { token: data.token });
  return response.data; // Return the full response for disable (no nested data)
};

// Hooks for admin security
export function useChangeAdminPasswordMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: changeAdminPassword,
    onSuccess: () => {
      // Invalidate profile data
      queryClient.invalidateQueries({ queryKey: queryKeys.user.profile() });
    },
    onError: (error) => {
      console.error("Failed to change admin password:", error);
    },
  });
}

export function useSetupAdmin2FAMutation() {
  return useMutation({
    mutationFn: () => setup2FA("admin"),
  });
}

export function useVerifyAdmin2FAMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Verify2FAData) =>
      verify2FA({ ...data, userType: "admin" }),
    onSuccess: () => {
      // Invalidate profile data to refresh 2FA status
      queryClient.invalidateQueries({ queryKey: queryKeys.user.profile() });
    },
  });
}

export function useDisableAdmin2FAMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { token: string }) =>
      disable2FA({ ...data, userType: "admin" }),
    onSuccess: () => {
      // Invalidate profile data to refresh 2FA status
      queryClient.invalidateQueries({ queryKey: queryKeys.user.profile() });
    },
  });
}

// Hooks for company security
export function useChangeCompanyPasswordMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: changeCompanyPassword,
    onSuccess: () => {
      // Invalidate profile data
      queryClient.invalidateQueries({ queryKey: queryKeys.user.profile() });
    },
    onError: (error) => {
      console.error("Failed to change company password:", error);
    },
  });
}

export function useSetupCompany2FAMutation() {
  return useMutation({
    mutationFn: () => setup2FA("company"),
    onError: (error) => {
      console.error("Failed to setup company 2FA:", error);
    },
  });
}

export function useVerifyCompany2FAMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Verify2FAData) =>
      verify2FA({ ...data, userType: "company" }),
    onSuccess: () => {
      // Invalidate profile data to refresh 2FA status
      queryClient.invalidateQueries({ queryKey: queryKeys.user.profile() });
    },
    onError: (error) => {
      console.error("Failed to verify company 2FA:", error);
    },
  });
}

export function useDisableCompany2FAMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { token: string }) =>
      disable2FA({ ...data, userType: "company" }),
    onSuccess: () => {
      // Invalidate profile data to refresh 2FA status
      queryClient.invalidateQueries({ queryKey: queryKeys.user.profile() });
    },
    onError: (error) => {
      console.error("Failed to disable company 2FA:", error);
    },
  });
}
