// Centralized query keys for TanStack Query
export const queryKeys = {
  // Companies
  companies: {
    all: ['companies'] as const,
    lists: () => [...queryKeys.companies.all, 'list'] as const,
    list: (filters?: Record<string, unknown>) => [...queryKeys.companies.lists(), { filters }] as const,
    details: () => [...queryKeys.companies.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.companies.details(), id] as const,
    stats: () => [...queryKeys.companies.all, 'stats'] as const,
  },

  // Devices
  devices: {
    all: ['devices'] as const,
    lists: () => [...queryKeys.devices.all, 'list'] as const,
    list: (filters?: Record<string, unknown>) => [...queryKeys.devices.lists(), { filters }] as const,
    details: () => [...queryKeys.devices.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.devices.details(), id] as const,
    status: () => [...queryKeys.devices.all, 'status'] as const,
    statusBatch: (serialNumbers: string[]) => [...queryKeys.devices.status(), { serialNumbers }] as const,
    singleStatus: (serialNo: string) => [...queryKeys.devices.all, 'status', serialNo] as const,
    userData: (serialNo: string) => [...queryKeys.devices.all, 'userData', serialNo] as const,
    attendanceLogs: (serialNo: string, dateRange?: { from: string; to: string }) =>
      [...queryKeys.devices.all, 'attendanceLogs', serialNo, { dateRange }] as const,
  },

  // Allocations
  allocations: {
    all: ['allocations'] as const,
    byCompany: (companyId: string) => [...queryKeys.allocations.all, 'company', companyId] as const,
    byDevice: (deviceSerialNo: string) => [...queryKeys.allocations.all, 'device', deviceSerialNo] as const,
  },

  // External API
  external: {
    all: ['external'] as const,
    timezones: () => [...queryKeys.external.all, 'timezones'] as const,
    deviceList: () => [...queryKeys.external.all, 'deviceList'] as const,
    deviceStatus: (serialNo: string) => [...queryKeys.external.all, 'deviceStatus', serialNo] as const,
  },

  // Dashboard
  dashboard: {
    all: ['dashboard'] as const,
    adminStats: () => [...queryKeys.dashboard.all, 'admin', 'stats'] as const,
    adminActivity: () => [...queryKeys.dashboard.all, 'admin', 'activity'] as const,
    adminSystemStatus: () => [...queryKeys.dashboard.all, 'admin', 'system-status'] as const,
    companyStats: (companyId: string) => [...queryKeys.dashboard.all, 'company', companyId, 'stats'] as const,
    companyActivity: (companyId: string) => [...queryKeys.dashboard.all, 'company', companyId, 'activity'] as const,
  },

  // User/Auth
  user: {
    all: ['user'] as const,
    profile: () => [...queryKeys.user.all, 'profile'] as const,
    settings: () => [...queryKeys.user.all, 'settings'] as const,
  },

  // Settings
  settings: {
    all: ['settings'] as const,
    admin: () => [...queryKeys.settings.all, 'admin'] as const,
    company: (companyId: string) => [...queryKeys.settings.all, 'company', companyId] as const,
  },
} as const

// Helper function to invalidate related queries
export const getInvalidationKeys = {
  // When a company is created/updated/deleted
  onCompanyChange: () => [
    queryKeys.companies.all,
    queryKeys.dashboard.adminStats(),
    queryKeys.allocations.all,
  ],

  // When a device allocation changes
  onAllocationChange: (companyId?: string) => [
    queryKeys.allocations.all,
    queryKeys.dashboard.adminStats(),
    ...(companyId ? [queryKeys.dashboard.companyStats(companyId)] : []),
    queryKeys.companies.all,
  ],

  // When device data changes
  onDeviceChange: () => [
    queryKeys.devices.all,
    queryKeys.external.deviceList(),
    queryKeys.dashboard.adminStats(),
  ],

  // When user profile changes
  onUserChange: () => [
    queryKeys.user.all,
  ],
}
