import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";

interface Timezone {
  id: number;
  name: string;
  time: string;
}

interface TimezoneResponse {
  success: boolean;
  data: Timezone[];
  total: number;
}

// Fetch timezones from API (admin)
const fetchTimezones = async (): Promise<TimezoneResponse> => {
  const response = await fetch("/api/admin/timezones");

  if (!response.ok) {
    throw new Error("Failed to fetch timezones");
  }

  return response.json();
};

// Fetch timezones from API (company)
const fetchCompanyTimezones = async (): Promise<TimezoneResponse> => {
  const response = await fetch("/api/dashboard/timezones");

  if (!response.ok) {
    throw new Error("Failed to fetch timezones");
  }

  return response.json();
};

// Sync timezones from external API
const syncTimezones = async (): Promise<TimezoneResponse> => {
  const response = await fetch("/api/admin/timezones", {
    method: "POST",
  });

  if (!response.ok) {
    throw new Error("Failed to sync timezones");
  }

  return response.json();
};

// Hook to fetch timezones (admin)
export const useTimezonesQuery = () => {
  return useQuery({
    queryKey: ["timezones"],
    queryFn: fetchTimezones,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

// Hook to fetch timezones (company)
export const useCompanyTimezonesQuery = () => {
  return useQuery({
    queryKey: ["company-timezones"],
    queryFn: fetchCompanyTimezones,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

// Hook to sync timezones from external API
export const useSyncTimezonesMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: syncTimezones,
    onSuccess: (data) => {
      // Invalidate and refetch timezones
      queryClient.invalidateQueries({ queryKey: ["timezones"] });
      toast.success(`Successfully synced ${data.data.length} timezones`);
    },
    onError: (error: Error) => {
      console.error("Error syncing timezones:", error);
      toast.error("Failed to sync timezones");
    },
  });
};

// Helper function to format timezone for display
export const formatTimezoneOption = (timezone: Timezone) => ({
  value: timezone.id.toString(),
  label: `${timezone.name} (${timezone.time})`,
});

// Helper function to get timezone options for select components (admin)
export const useTimezoneOptions = () => {
  const { data: timezonesData, isLoading, error } = useTimezonesQuery();

  const options = timezonesData?.data?.map(formatTimezoneOption) || [];

  return {
    options,
    isLoading,
    error,
    timezones: timezonesData?.data || [],
  };
};

// Helper function to get timezone options for select components (company)
export const useCompanyTimezoneOptions = () => {
  const { data: timezonesData, isLoading, error } = useCompanyTimezonesQuery();

  const options = timezonesData?.data?.map(formatTimezoneOption) || [];

  return {
    options,
    isLoading,
    error,
    timezones: timezonesData?.data || [],
  };
};
