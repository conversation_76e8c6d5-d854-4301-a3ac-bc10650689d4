"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { UserType } from "@prisma/client";
import { Eye, EyeOff, Copy, RefreshCw } from "lucide-react";
import { toast } from "react-toastify";
import { useCreateCompanyMutation } from "@/hooks/queries/useCompaniesQuery";
import DatePicker from "@/components/DatePicker";

interface CompanyRegistrationFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  organizationId: string;
  email: string;
  password: string;
  userType: UserType | "";
  validity: string;
  description: string;
  document?: File;
  externalApiEndpoint?: string;
}

interface RegistrationResult {
  company: any;
  loginToken: string;
  password?: string;
}

export function CompanyRegistrationForm({
  open,
  onOpenChange,
  onSuccess,
}: CompanyRegistrationFormProps) {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    organizationId: "",
    email: "",
    password: "",
    userType: "",
    validity: "365",
    description: "",
  });
  const [validityType, setValidityType] = useState<"days" | "date">("days");
  const [customExpiryDate, setCustomExpiryDate] = useState<Date | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [result, setResult] = useState<RegistrationResult | null>(null);
  const [showResult, setShowResult] = useState(false);

  // Use TanStack Query mutation for automatic cache invalidation
  const createCompanyMutation = useCreateCompanyMutation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Calculate validity based on type
      let validityDays: number;
      if (validityType === "date" && customExpiryDate) {
        const today = new Date();
        const expiryDate = new Date(customExpiryDate);
        const diffTime = expiryDate.getTime() - today.getTime();
        validityDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (validityDays <= 0) {
          toast.error("Expiry date must be in the future");
          return;
        }
      } else {
        validityDays = parseInt(formData.validity);
      }

      const submitData = {
        ...formData,
        validity: validityDays,
        userType: formData.userType as UserType,
      };

      // Use TanStack Query mutation which automatically invalidates cache
      const response = await createCompanyMutation.mutateAsync(submitData);

      if (response.success) {
        setResult(response.data);
        setShowResult(true);
        toast.success("Company registered successfully!");
      } else {
        toast.error(response.error || "Registration failed");
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An error occurred during registration";
      toast.error(errorMessage);
    }
  };

  const generatePassword = () => {
    const charset =
      "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";

    // Ensure at least one character from each category
    const lowercase = "abcdefghijklmnopqrstuvwxyz";
    const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const numbers = "0123456789";
    const symbols = "!@#$%^&*";

    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];

    // Fill the rest randomly
    for (let i = 4; i < 12; i++) {
      password += charset[Math.floor(Math.random() * charset.length)];
    }

    // Shuffle the password
    const shuffled = password
      .split("")
      .sort(() => Math.random() - 0.5)
      .join("");
    setFormData({ ...formData, password: shuffled });
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard!`);
  };

  const handleClose = () => {
    if (result) {
      onSuccess();
    }
    setFormData({
      name: "",
      organizationId: "",
      email: "",
      password: "",
      userType: "",
      validity: "365",
      description: "",
    });
    setValidityType("days");
    setCustomExpiryDate(null);
    setResult(null);
    setShowResult(false);
    onOpenChange(false);
  };

  const calculateExpirationDate = () => {
    // Use a fixed date for SSR consistency, then update on client
    if (typeof window === "undefined") {
      return "Calculating...";
    }

    if (validityType === "date" && customExpiryDate) {
      return customExpiryDate.toLocaleDateString();
    } else {
      const days = parseInt(formData.validity) || 0;
      const date = new Date();
      date.setDate(date.getDate() + days);
      return date.toLocaleDateString();
    }
  };

  const handleCustomDateChange = (date: Date | undefined) => {
    setCustomExpiryDate(date || null);
    if (date) {
      const today = new Date();
      const diffTime = date.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      if (diffDays > 0) {
        setFormData({ ...formData, validity: diffDays.toString() });
      }
    }
    // Don't clear validity when date is cleared - keep both fields in sync
  };

  const handleDaysChange = (value: string) => {
    setFormData({ ...formData, validity: value });
    // Update custom date when days change
    const days = parseInt(value);
    if (days > 0) {
      const calculatedDate = new Date();
      calculatedDate.setDate(calculatedDate.getDate() + days);
      setCustomExpiryDate(calculatedDate);
    }
  };

  if (showResult && result) {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Company Registered Successfully!</DialogTitle>
            <DialogDescription>
              Please share these credentials with the company. Make sure to copy
              them before closing.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Company Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium">Company Name</Label>
                  <p className="text-sm text-gray-600">{result.company.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Organization ID</Label>
                  <p className="text-sm text-gray-600">
                    {result.company.organizationId}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Email</Label>
                  <p className="text-sm text-gray-600">
                    {result.company.email}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Login Credentials</CardTitle>
                <CardDescription>
                  Share these credentials with the company for their first login
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Login Token</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      value={result.loginToken}
                      readOnly
                      className="font-mono text-sm"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        copyToClipboard(result.loginToken, "Login token")
                      }
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {result.password && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">
                      Generated Password
                    </Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        value={result.password}
                        readOnly
                        className="font-mono text-sm"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          copyToClipboard(result.password!, "Password")
                        }
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <DialogFooter>
            <Button onClick={handleClose}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Register New Company</DialogTitle>
          <DialogDescription>
            Create a new company account with access to the device management
            system.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Company Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                placeholder="Enter company name"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="organizationId">Organization ID *</Label>
              <Input
                id="organizationId"
                value={formData.organizationId}
                onChange={(e) =>
                  setFormData({ ...formData, organizationId: e.target.value })
                }
                placeholder="Enter organization ID"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email Address *</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              placeholder="Enter email address"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="flex space-x-2">
              <div className="relative flex-1">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) =>
                    setFormData({ ...formData, password: e.target.value })
                  }
                  placeholder="Enter password or generate one"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <Button
                type="button"
                variant="outline"
                onClick={generatePassword}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Generate
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="userType">User Type *</Label>
              <Select
                value={formData.userType}
                onValueChange={(value) =>
                  setFormData({ ...formData, userType: value as UserType })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select user type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="API_USER">API User</SelectItem>
                  <SelectItem value="SCHOOL_MANAGEMENT">
                    School Management
                  </SelectItem>
                  <SelectItem value="EMPLOYEE_MANAGEMENT">
                    Employee Management
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <Label>Validity *</Label>

              {/* Validity Type Selection */}
              <RadioGroup
                value={validityType}
                onValueChange={(value: "days" | "date") => {
                  setValidityType(value);
                  // When switching to date mode, calculate date from current days
                  if (
                    value === "date" &&
                    formData.validity &&
                    !customExpiryDate
                  ) {
                    const days = parseInt(formData.validity);
                    if (days > 0) {
                      const calculatedDate = new Date();
                      calculatedDate.setDate(calculatedDate.getDate() + days);
                      setCustomExpiryDate(calculatedDate);
                    }
                  }
                  // When switching to days mode, calculate days from current date
                  else if (
                    value === "days" &&
                    customExpiryDate &&
                    !formData.validity
                  ) {
                    const today = new Date();
                    const diffTime =
                      customExpiryDate.getTime() - today.getTime();
                    const diffDays = Math.ceil(
                      diffTime / (1000 * 60 * 60 * 24)
                    );
                    if (diffDays > 0) {
                      setFormData({
                        ...formData,
                        validity: diffDays.toString(),
                      });
                    }
                  }
                }}
                className="flex gap-6"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="days" id="validity-days" />
                  <Label htmlFor="validity-days">Number of Days</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="date" id="validity-date" />
                  <Label htmlFor="validity-date">Custom Date</Label>
                </div>
              </RadioGroup>

              {/* Days Input */}
              {validityType === "days" && (
                <Input
                  id="validity"
                  type="number"
                  min="1"
                  max="3650"
                  value={formData.validity}
                  onChange={(e) => handleDaysChange(e.target.value)}
                  placeholder="365"
                  required
                />
              )}

              {/* Date Picker */}
              {validityType === "date" && (
                <DatePicker
                  value={customExpiryDate || undefined}
                  onChange={handleCustomDateChange}
                  placeholder="Select expiry date"
                  className="w-full"
                  minDate={new Date()} // Prevent past dates
                  required
                />
              )}

              {/* Expiry Date Display */}
              {(formData.validity || customExpiryDate) && (
                <p className="text-xs text-gray-500">
                  Expires on: {calculateExpirationDate()}
                  {validityType === "date" && customExpiryDate && (
                    <span className="ml-2">
                      (
                      {Math.ceil(
                        (customExpiryDate.getTime() - new Date().getTime()) /
                          (1000 * 60 * 60 * 24)
                      )}{" "}
                      days)
                    </span>
                  )}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              placeholder="Enter company description"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="externalApiEndpoint">External API Endpoint (Optional)</Label>
            <Input
              id="externalApiEndpoint"
              value={formData.externalApiEndpoint}
              onChange={(e) =>
                setFormData({ ...formData, externalApiEndpoint: e.target.value })
              }
              placeholder="Enter external API endpoint"
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={createCompanyMutation.isPending}>
              {createCompanyMutation.isPending
                ? "Registering..."
                : "Register Company"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
