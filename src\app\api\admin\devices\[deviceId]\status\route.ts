import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '@/lib/auth';
import { externalDeviceAPI } from '@/lib/api/external-device-api';

// GET /api/admin/devices/[deviceId]/status - Get single device status
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { deviceId } = await params;
    const deviceSerial = deviceId;
    console.log(`Checking status for device: ${deviceSerial}`);

    try {
      // Check device status using external API
      const response = await externalDeviceAPI.getDeviceOnlineStatus(deviceSerial);

      // Determine status based on response
      let status = 'UNKNOWN';
      if (response.status === '200') {
        status = 'ONLINE';
      } else if (response.status === '300') {
        status = 'OFFLINE';
      }

      console.log(`Device ${deviceSerial} status: ${status} (API response: ${response.status})`);

      return NextResponse.json({
        success: true,
        status,
        message: response.msg || 'Status checked successfully',
        timestamp: Date.now(),
        deviceSerial
      });

    } catch (error) {
      console.error(`Error checking status for device ${deviceSerial}:`, error);
      
      return NextResponse.json({
        success: false,
        status: 'UNKNOWN',
        message: 'Error checking device status',
        timestamp: Date.now(),
        deviceSerial
      });
    }

  } catch (error) {
    console.error('Error in device status endpoint:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
