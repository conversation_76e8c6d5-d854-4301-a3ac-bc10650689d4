import { prisma } from "@/lib/prisma";

interface AdminSettings {
  siteName: string;
  siteDescription: string;
  adminEmail: string;
  defaultTimeZone: string;
  dateFormat: string;
  language: string;
  maintenanceMode: boolean;
  logoUrl: string;
  faviconUrl: string;
}

interface CompanySettings {
  companyName: string;
  timezone: string;
  dateFormat: string;
  workingHoursStart: string;
  workingHoursEnd: string;
  workingDays: string[];
  graceTime: number;
  emailAlerts: boolean;
}

// Cache for settings to avoid repeated database calls
let adminSettingsCache: AdminSettings | null = null;
let adminSettingsCacheTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Get admin settings with caching
 */
export async function getAdminSettings(): Promise<AdminSettings> {
  const now = Date.now();

  // Return cached settings if still valid
  if (adminSettingsCache && now - adminSettingsCacheTime < CACHE_DURATION) {
    return adminSettingsCache;
  }

  try {
    const settings = await prisma.adminSettings.findFirst();

    if (settings) {
      adminSettingsCache = {
        siteName: settings.siteName,
        siteDescription: settings.siteDescription,
        adminEmail: settings.adminEmail,
        defaultTimeZone: settings.defaultTimeZoneId?.toString() || "UTC",
        dateFormat: settings.dateFormat,
        language: settings.language,
        maintenanceMode: settings.maintenanceMode,
        logoUrl: settings.logoUrl || "",
        faviconUrl: settings.faviconUrl || "",
      };
      adminSettingsCacheTime = now;
      return adminSettingsCache;
    }
  } catch (error) {
    console.warn("Failed to fetch admin settings:", error);
  }

  // Return default settings if database fails
  const defaultSettings: AdminSettings = {
    siteName: "Smart Attendance Portal",
    siteDescription: "Advanced attendance management system",
    adminEmail: "<EMAIL>",
    defaultTimeZone: "UTC",
    dateFormat: "MM/DD/YYYY",
    language: "en",
    maintenanceMode: false,
    logoUrl: "",
    faviconUrl: "",
  };

  adminSettingsCache = defaultSettings;
  adminSettingsCacheTime = now;
  return defaultSettings;
}

/**
 * Get company settings for a specific company
 */
export async function getCompanySettings(
  companyId: string
): Promise<CompanySettings | null> {
  try {
    const settings = await prisma.companySettings.findUnique({
      where: { companyId },
    });

    if (settings) {
      return {
        companyName: settings.companyName,
        timezone: settings.timezone,
        dateFormat: settings.dateFormat,
        workingHoursStart: settings.workingHoursStart,
        workingHoursEnd: settings.workingHoursEnd,
        workingDays: settings.workingDays.split(","),
        graceTime: settings.graceTime,
        emailAlerts: settings.emailAlerts,
      };
    }
  } catch (error) {
    console.warn("Failed to fetch company settings:", error);
  }

  return null;
}

/**
 * Clear the admin settings cache (useful after updates)
 */
export function clearAdminSettingsCache() {
  adminSettingsCache = null;
  adminSettingsCacheTime = 0;
}

/**
 * Get site metadata based on admin settings
 */
export async function getSiteMetadata() {
  const settings = await getAdminSettings();

  const metadata: Record<string, unknown> = {
    title: `${settings.siteName} | SRITechnologies`,
    description: settings.siteDescription,
    keywords: [
      "attendance",
      "biometric",
      "device management",
      "SRITechnologies",
    ],
  };

  // Add favicon if available
  if (settings.faviconUrl) {
    metadata.icons = {
      icon: settings.faviconUrl,
      shortcut: settings.faviconUrl,
      apple: settings.faviconUrl,
    };
  }

  return metadata;
}
