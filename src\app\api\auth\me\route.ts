import { NextRequest, NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { prisma } from '@/lib/prisma'

async function handler(req: AuthenticatedRequest) {
  try {
    const user = req.user!
    
    if (user.role === 'admin') {
      return NextResponse.json({
        success: true,
        user: {
          id: user.userId,
          email: user.email,
          role: user.role
        }
      })
    } else if (user.role === 'company') {
      // Get company details
      const company = await prisma.company.findUnique({
        where: { id: user.companyId },
        select: {
          id: true,
          name: true,
          email: true,
          organizationId: true,
          userType: true,
          status: true,
          expiresAt: true,
          createdAt: true
        }
      })
      
      if (!company) {
        return NextResponse.json(
          { error: 'Company not found' },
          { status: 404 }
        )
      }
      
      return NextResponse.json({
        success: true,
        user: {
          id: user.userId,
          email: user.email,
          role: user.role,
          company
        }
      })
    }
    
    return NextResponse.json(
      { error: 'Invalid user role' },
      { status: 400 }
    )
    
  } catch (error) {
    console.error('Get user info error:', error)
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export const GET = withAuth(handler)
